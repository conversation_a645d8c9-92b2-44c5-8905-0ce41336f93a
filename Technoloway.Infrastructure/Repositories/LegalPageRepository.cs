using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories
{
    public class LegalPageRepository : Repository<LegalPage>, ILegalPageRepository
    {
        public LegalPageRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<LegalPage?> GetBySlugAsync(string slug)
        {
            return await _context.LegalPages
                .FirstOrDefaultAsync(lp => lp.Slug == slug && lp.IsActive);
        }

        public async Task<LegalPage?> GetBySlugWithSectionsAsync(string slug)
        {
            return await _context.LegalPages
                .Include(lp => lp.Sections.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(lp => lp.Slug == slug && lp.IsActive);
        }

        public async Task<IEnumerable<LegalPage>> GetActiveAsync()
        {
            return await _context.LegalPages
                .Where(lp => lp.IsActive)
                .OrderBy(lp => lp.Title)
                .ToListAsync();
        }

        public async Task<LegalPage?> GetWithSectionsAsync(int id)
        {
            return await _context.LegalPages
                .Include(lp => lp.Sections.OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(lp => lp.Id == id);
        }

        public async Task UpdateWithSectionsAsync(LegalPage legalPage, List<LegalPageSection> sections)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Update the legal page
                _context.LegalPages.Update(legalPage);

                // Remove existing sections
                var existingSections = await _context.LegalPageSections
                    .Where(s => s.LegalPageId == legalPage.Id)
                    .ToListAsync();
                _context.LegalPageSections.RemoveRange(existingSections);

                // Add new sections
                foreach (var section in sections)
                {
                    section.LegalPageId = legalPage.Id;
                    _context.LegalPageSections.Add(section);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
