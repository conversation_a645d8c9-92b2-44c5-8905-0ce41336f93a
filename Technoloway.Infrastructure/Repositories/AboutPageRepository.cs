using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories
{
    public class AboutPageRepository : Repository<AboutPage>, IAboutPageRepository
    {
        public AboutPageRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<AboutPage?> GetActiveAboutPageAsync()
        {
            return await _context.AboutPages
                .Where(p => p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync();
        }

        public async Task<AboutPage?> GetWithSectionsAsync(int id)
        {
            return await _context.AboutPages
                .Include(p => p.Sections.Where(s => s.IsActive && !s.IsDeleted))
                .Where(p => p.Id == id && !p.IsDeleted)
                .FirstOrDefaultAsync();
        }

        public async Task<AboutPage?> GetActiveWithSectionsAsync()
        {
            return await _context.AboutPages
                .Include(p => p.Sections.Where(s => s.IsActive && !s.IsDeleted))
                .Where(p => p.IsActive && !p.IsDeleted)
                .OrderByDescending(p => p.LastModified)
                .FirstOrDefaultAsync();
        }

        public new async Task<List<AboutPage>> ListAllAsync()
        {
            return await _context.AboutPages
                .Where(p => !p.IsDeleted)
                .OrderByDescending(p => p.LastModified)
                .ToListAsync();
        }

        public async Task<bool> HasActivePageAsync()
        {
            return await _context.AboutPages
                .AnyAsync(p => p.IsActive && !p.IsDeleted);
        }
    }
}
