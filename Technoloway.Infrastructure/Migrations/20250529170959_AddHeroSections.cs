﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddHeroSections : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HeroSections",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    MetaDescription = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    MetaKeywords = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    MainTitle = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    MainSubtitle = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    MainDescription = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    PrimaryButtonText = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    PrimaryButtonUrl = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    SecondaryButtonText = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    SecondaryButtonUrl = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    EnableSlideshow = table.Column<bool>(type: "INTEGER", nullable: false),
                    SlideshowSpeed = table.Column<int>(type: "INTEGER", nullable: false),
                    AutoPlay = table.Column<bool>(type: "INTEGER", nullable: false),
                    ShowDots = table.Column<bool>(type: "INTEGER", nullable: false),
                    ShowArrows = table.Column<bool>(type: "INTEGER", nullable: false),
                    EnableFloatingElements = table.Column<bool>(type: "INTEGER", nullable: false),
                    FloatingElementsConfig = table.Column<string>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ModifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HeroSections", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HeroSlides",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    HeroSectionId = table.Column<int>(type: "INTEGER", nullable: false),
                    Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Subtitle = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    ImageUrl = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    ImageAlt = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ButtonText = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ButtonUrl = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    AnimationType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Duration = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HeroSlides", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HeroSlides_HeroSections_HeroSectionId",
                        column: x => x.HeroSectionId,
                        principalTable: "HeroSections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HeroSlides_HeroSectionId",
                table: "HeroSlides",
                column: "HeroSectionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HeroSlides");

            migrationBuilder.DropTable(
                name: "HeroSections");
        }
    }
}
