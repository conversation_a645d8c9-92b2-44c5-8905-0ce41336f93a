using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data
{
    public static class HeroSectionSeeder
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            // Check if HeroSections already exist
            if (await context.HeroSections.AnyAsync())
            {
                return; // Data already seeded
            }

            var heroSection = new HeroSection
            {
                Title = "Home Hero Section",
                PageName = "Home",
                MetaDescription = "Dynamic hero section for Technoloway homepage with slideshow functionality",
                MetaKeywords = "hero, slideshow, software development, technology solutions",

                MainTitle = "Innovative <span class=\"title-highlight\">Software Solutions</span> for Your Business",
                MainSubtitle = "Transform your ideas into powerful digital experiences with our cutting-edge technology and expert development team.",
                MainDescription = "We specialize in creating custom software solutions that drive business growth, enhance user experiences, and deliver measurable results. From web applications to mobile apps, we bring your vision to life.",

                PrimaryButtonText = "Get Started Today",
                PrimaryButtonUrl = "/contact",
                SecondaryButtonText = "View Our Work",
                SecondaryButtonUrl = "/projects",

                EnableSlideshow = true,
                SlideshowSpeed = 6000,
                AutoPlay = true,
                ShowDots = true,
                ShowArrows = true,
                EnableFloatingElements = true,
                FloatingElementsConfig = @"{
                    ""elements"": [
                        {""icon"": ""fas fa-code"", ""position"": ""top-left"", ""delay"": 0},
                        {""icon"": ""fas fa-mobile-alt"", ""position"": ""top-right"", ""delay"": 1000},
                        {""icon"": ""fas fa-cloud"", ""position"": ""bottom-left"", ""delay"": 2000},
                        {""icon"": ""fas fa-database"", ""position"": ""bottom-right"", ""delay"": 3000}
                    ]
                }",

                IsActive = true,
                LastModified = DateTime.UtcNow,
                ModifiedBy = "System Seeder"
            };

            context.HeroSections.Add(heroSection);
            await context.SaveChangesAsync();

            // Add hero slides
            var slides = new List<HeroSlide>
            {
                new HeroSlide
                {
                    HeroSectionId = heroSection.Id,
                    Content = "<h2>Custom Web Development</h2><h3>Building Modern Web Applications</h3><p>We create responsive, scalable web applications using the latest technologies like React, Angular, .NET, and Node.js to deliver exceptional user experiences.</p>",
                    MediaType = "image",
                    ImageUrl = "/images/hero/web-development.jpg",
                    MediaAlt = "Modern web development workspace",
                    ButtonText = "Explore Web Services",
                    ButtonUrl = "/services#web-development",
                    DisplayOrder = 1,
                    IsActive = true,
                    AnimationType = "fade",
                    Duration = 6000
                },
                new HeroSlide
                {
                    HeroSectionId = heroSection.Id,
                    Content = "<h2>Mobile App Development</h2><h3>Native & Cross-Platform Solutions</h3><p>From iOS and Android native apps to cross-platform solutions using React Native and Flutter, we build mobile applications that engage users and drive business growth.</p>",
                    MediaType = "image",
                    ImageUrl = "/images/hero/mobile-development.jpg",
                    MediaAlt = "Mobile app development process",
                    ButtonText = "View Mobile Solutions",
                    ButtonUrl = "/services#mobile-development",
                    DisplayOrder = 2,
                    IsActive = true,
                    AnimationType = "slide",
                    Duration = 6000
                },
                new HeroSlide
                {
                    HeroSectionId = heroSection.Id,
                    Content = "<h2>Cloud Solutions & DevOps</h2><h3>Scalable Infrastructure & Deployment</h3><p>Leverage the power of cloud computing with our AWS, Azure, and Google Cloud expertise. We implement DevOps practices for continuous integration and deployment.</p>",
                    MediaType = "video",
                    VideoUrl = "/videos/hero/cloud-demo.mp4",
                    MediaAlt = "Cloud infrastructure and DevOps demonstration",
                    VideoAutoPlay = true,
                    VideoMuted = true,
                    VideoLoop = true,
                    VideoControls = false,
                    ButtonText = "Learn About Cloud",
                    ButtonUrl = "/services#cloud-solutions",
                    DisplayOrder = 3,
                    IsActive = true,
                    AnimationType = "zoom",
                    Duration = 6000
                },
                new HeroSlide
                {
                    HeroSectionId = heroSection.Id,
                    Content = "<h2>AI & Machine Learning</h2><h3>Intelligent Solutions for Tomorrow</h3><p>Harness the power of artificial intelligence and machine learning to automate processes, gain insights from data, and create intelligent applications that adapt and learn.</p>",
                    MediaType = "image",
                    ImageUrl = "/images/hero/ai-ml.jpg",
                    MediaAlt = "AI and machine learning technology",
                    ButtonText = "Discover AI Solutions",
                    ButtonUrl = "/services#ai-solutions",
                    DisplayOrder = 4,
                    IsActive = true,
                    AnimationType = "fade",
                    Duration = 6000
                },
                new HeroSlide
                {
                    HeroSectionId = heroSection.Id,
                    Content = "<h2>E-Commerce Development</h2><h3>Complete Online Store Solutions</h3><p>Build powerful e-commerce platforms with secure payment processing, inventory management, and customer analytics to grow your online business effectively.</p>",
                    MediaType = "image",
                    ImageUrl = "/images/hero/ecommerce.jpg",
                    MediaAlt = "E-commerce development platform",
                    ButtonText = "Start Selling Online",
                    ButtonUrl = "/services#ecommerce",
                    DisplayOrder = 5,
                    IsActive = true,
                    AnimationType = "slide",
                    Duration = 6000
                }
            };

            context.HeroSlides.AddRange(slides);
            await context.SaveChangesAsync();

            // Create hero sections for other pages
            var otherHeroSections = new List<HeroSection>
            {
                // About Page Hero Section
                new HeroSection
                {
                    Title = "About Page Hero Section",
                    PageName = "About",
                    MetaDescription = "Learn about Technoloway's mission, vision, and the passionate team behind our innovative software solutions",
                    MetaKeywords = "about us, company, mission, vision, team, software development",
                    MainTitle = "About <span class=\"title-highlight\">Technoloway</span>",
                    MainSubtitle = "Passionate innovators dedicated to transforming businesses through cutting-edge technology solutions.",
                    MainDescription = "Founded with a vision to bridge the gap between complex technology and business success, we are a team of dedicated professionals committed to delivering exceptional software solutions.",
                    PrimaryButtonText = "Meet Our Team",
                    PrimaryButtonUrl = "/about#team",
                    SecondaryButtonText = "Our Story",
                    SecondaryButtonUrl = "/about#story",
                    EnableSlideshow = false,
                    EnableFloatingElements = true,
                    FloatingElementsConfig = @"{
                        ""elements"": [
                            {""icon"": ""fas fa-users"", ""position"": ""top-left"", ""delay"": 0},
                            {""icon"": ""fas fa-lightbulb"", ""position"": ""top-right"", ""delay"": 1000},
                            {""icon"": ""fas fa-handshake"", ""position"": ""bottom-left"", ""delay"": 2000},
                            {""icon"": ""fas fa-trophy"", ""position"": ""bottom-right"", ""delay"": 3000}
                        ]
                    }",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System Seeder"
                },

                // Services Page Hero Section
                new HeroSection
                {
                    Title = "Services Page Hero Section",
                    PageName = "Services",
                    MetaDescription = "Comprehensive software development services including web development, mobile apps, cloud solutions, and more",
                    MetaKeywords = "services, web development, mobile apps, cloud solutions, software development",
                    MainTitle = "Our <span class=\"title-highlight\">Services</span>",
                    MainSubtitle = "Comprehensive software development solutions tailored to your business needs.",
                    MainDescription = "From custom web applications to mobile apps and cloud infrastructure, we provide end-to-end technology solutions that drive business growth and innovation.",
                    PrimaryButtonText = "View All Services",
                    PrimaryButtonUrl = "/services",
                    SecondaryButtonText = "Get Quote",
                    SecondaryButtonUrl = "/contact",
                    EnableSlideshow = false,
                    EnableFloatingElements = true,
                    FloatingElementsConfig = @"{
                        ""elements"": [
                            {""icon"": ""fas fa-code"", ""position"": ""top-left"", ""delay"": 0},
                            {""icon"": ""fas fa-mobile-alt"", ""position"": ""top-right"", ""delay"": 1000},
                            {""icon"": ""fas fa-cloud"", ""position"": ""bottom-left"", ""delay"": 2000},
                            {""icon"": ""fas fa-cogs"", ""position"": ""bottom-right"", ""delay"": 3000}
                        ]
                    }",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System Seeder"
                },

                // Projects Page Hero Section
                new HeroSection
                {
                    Title = "Projects Page Hero Section",
                    PageName = "Projects",
                    MetaDescription = "Explore our portfolio of successful software development projects and case studies",
                    MetaKeywords = "projects, portfolio, case studies, software development, web applications",
                    MainTitle = "Our <span class=\"title-highlight\">Projects</span>",
                    MainSubtitle = "Discover our portfolio of successful software solutions and digital transformations.",
                    MainDescription = "Explore real-world examples of how we've helped businesses achieve their goals through innovative technology solutions and strategic digital transformation.",
                    PrimaryButtonText = "View Portfolio",
                    PrimaryButtonUrl = "/projects",
                    SecondaryButtonText = "Start Your Project",
                    SecondaryButtonUrl = "/contact",
                    EnableSlideshow = false,
                    EnableFloatingElements = true,
                    FloatingElementsConfig = @"{
                        ""elements"": [
                            {""icon"": ""fas fa-laptop-code"", ""position"": ""top-left"", ""delay"": 0},
                            {""icon"": ""fas fa-chart-line"", ""position"": ""top-right"", ""delay"": 1000},
                            {""icon"": ""fas fa-rocket"", ""position"": ""bottom-left"", ""delay"": 2000},
                            {""icon"": ""fas fa-star"", ""position"": ""bottom-right"", ""delay"": 3000}
                        ]
                    }",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System Seeder"
                },

                // Contact Page Hero Section
                new HeroSection
                {
                    Title = "Contact Page Hero Section",
                    PageName = "Contact",
                    MetaDescription = "Get in touch with Technoloway for your software development needs and project inquiries",
                    MetaKeywords = "contact, get in touch, software development, project inquiry, consultation",
                    MainTitle = "Get In <span class=\"title-highlight\">Touch</span>",
                    MainSubtitle = "Ready to transform your business with innovative software solutions?",
                    MainDescription = "Let's discuss your project requirements and explore how our expertise can help you achieve your business goals. We're here to turn your ideas into reality.",
                    PrimaryButtonText = "Send Message",
                    PrimaryButtonUrl = "/contact#contact-form",
                    SecondaryButtonText = "Schedule Call",
                    SecondaryButtonUrl = "/contact#schedule",
                    EnableSlideshow = false,
                    EnableFloatingElements = true,
                    FloatingElementsConfig = @"{
                        ""elements"": [
                            {""icon"": ""fas fa-envelope"", ""position"": ""top-left"", ""delay"": 0},
                            {""icon"": ""fas fa-phone"", ""position"": ""top-right"", ""delay"": 1000},
                            {""icon"": ""fas fa-map-marker-alt"", ""position"": ""bottom-left"", ""delay"": 2000},
                            {""icon"": ""fas fa-comments"", ""position"": ""bottom-right"", ""delay"": 3000}
                        ]
                    }",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System Seeder"
                }
            };

            context.HeroSections.AddRange(otherHeroSections);
            await context.SaveChangesAsync();
        }
    }
}
