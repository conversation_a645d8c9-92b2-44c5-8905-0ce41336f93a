using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data
{
    public static class AboutPageSeeder
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            if (await context.AboutPages.AnyAsync())
            {
                return; // Already seeded
            }

            var aboutPage = new AboutPage
            {
                Title = "About Us",
                MetaDescription = "Learn about Technoloway, our mission, values, and the team behind our innovative software solutions.",
                MetaKeywords = "about us, software company, development team, tech experts, software engineers",

                // Hero Section
                HeroTitle = "About Technoloway",
                HeroSubtitle = "Learn about our mission, values, and the passionate team behind our innovative software solutions that help businesses thrive in the digital world.",
                HeroImageUrl = "/images/about-hero.jpg",

                // Story Section
                StoryTitle = "Our Story",
                StorySubtitle = "Technoloway was founded in 2015 with a mission to deliver innovative software solutions that help businesses thrive in the digital age.",
                StoryContent = @"<p>We started as a small team of passionate developers and have grown into a full-service software development company with expertise in web development, mobile applications, cloud solutions, and more.</p>
                <p>Our commitment to quality, innovation, and client satisfaction has made us a trusted partner for businesses of all sizes, from startups to large enterprises.</p>
                <p>Today, we continue to push the boundaries of technology, helping our clients achieve their goals through cutting-edge software solutions and exceptional service.</p>",
                StoryImageUrl = "/images/about-us.jpg",

                // Stats
                Stat1Number = "8+",
                Stat1Label = "Years Experience",
                Stat2Number = "100+",
                Stat2Label = "Projects Completed",
                Stat3Number = "50+",
                Stat3Label = "Happy Clients",
                Stat4Number = "24/7",
                Stat4Label = "Support Available",

                // Mission & Vision
                MissionTitle = "Our Mission",
                MissionContent = @"<p>To empower businesses with innovative software solutions that drive growth, efficiency, and success in the digital landscape. We are committed to delivering exceptional value through cutting-edge technology and outstanding service.</p>",

                VisionTitle = "Our Vision",
                VisionContent = @"<p>To be the leading software development partner that businesses trust to transform their ideas into reality. We envision a future where technology seamlessly integrates with business processes to create unprecedented opportunities for growth and innovation.</p>",

                ValuesTitle = "Our Values",
                ValuesContent = @"<p>We believe in excellence, integrity, innovation, and collaboration. These core values guide everything we do, from how we approach projects to how we interact with our clients and team members.</p>",

                // CTA Section
                CtaTitle = "Ready to Work With Us?",
                CtaSubtitle = "Let's discuss how we can help your business succeed with innovative software solutions. Our team is ready to turn your ideas into reality.",
                CtaPrimaryButtonText = "Get Started",
                CtaPrimaryButtonUrl = "/contact",
                CtaSecondaryButtonText = "View Portfolio",
                CtaSecondaryButtonUrl = "/projects",

                IsActive = true,
                LastModified = DateTime.UtcNow,
                ModifiedBy = "System",
                CreatedAt = DateTime.UtcNow,

                Sections = new List<AboutPageSection>
                {
                    new AboutPageSection
                    {
                        Title = "Innovation",
                        Content = @"<p>We stay at the forefront of technology trends and continuously explore new tools and methodologies to deliver cutting-edge solutions that give our clients a competitive advantage.</p>",
                        IconClass = "fas fa-lightbulb",
                        DisplayOrder = 1,
                        IsActive = true,
                        SectionType = "value",
                        CreatedAt = DateTime.UtcNow
                    },
                    new AboutPageSection
                    {
                        Title = "Quality",
                        Content = @"<p>We maintain the highest standards in everything we do, from code quality and testing to project management and client communication. Excellence is not just a goal—it's our standard.</p>",
                        IconClass = "fas fa-award",
                        DisplayOrder = 2,
                        IsActive = true,
                        SectionType = "value",
                        CreatedAt = DateTime.UtcNow
                    },
                    new AboutPageSection
                    {
                        Title = "Collaboration",
                        Content = @"<p>We believe that the best solutions come from working closely with our clients. We foster open communication and collaborative partnerships to ensure project success.</p>",
                        IconClass = "fas fa-handshake",
                        DisplayOrder = 3,
                        IsActive = true,
                        SectionType = "value",
                        CreatedAt = DateTime.UtcNow
                    },
                    new AboutPageSection
                    {
                        Title = "Reliability",
                        Content = @"<p>Our clients depend on us to deliver on time, within budget, and to specification. We take this responsibility seriously and have built our reputation on consistent, reliable delivery.</p>",
                        IconClass = "fas fa-shield-alt",
                        DisplayOrder = 4,
                        IsActive = true,
                        SectionType = "value",
                        CreatedAt = DateTime.UtcNow
                    },
                    new AboutPageSection
                    {
                        Title = "Scalability",
                        Content = @"<p>We design and build solutions that grow with your business. Our scalable architectures and forward-thinking approach ensure your technology investment continues to deliver value as you expand.</p>",
                        IconClass = "fas fa-chart-line",
                        DisplayOrder = 5,
                        IsActive = true,
                        SectionType = "feature",
                        CreatedAt = DateTime.UtcNow
                    },
                    new AboutPageSection
                    {
                        Title = "Security",
                        Content = @"<p>We prioritize security in every aspect of our development process. From secure coding practices to robust data protection measures, we ensure your applications and data are safe.</p>",
                        IconClass = "fas fa-lock",
                        DisplayOrder = 6,
                        IsActive = true,
                        SectionType = "feature",
                        CreatedAt = DateTime.UtcNow
                    }
                }
            };

            await context.AboutPages.AddAsync(aboutPage);
            await context.SaveChangesAsync();
        }
    }
}
