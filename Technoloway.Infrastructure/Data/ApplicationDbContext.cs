using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data;

public class ApplicationDbContext : IdentityDbContext<IdentityUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Service> Services { get; set; }
    public DbSet<Project> Projects { get; set; }
    public DbSet<Technology> Technologies { get; set; }
    public DbSet<TeamMember> TeamMembers { get; set; }
    public DbSet<BlogPost> BlogPosts { get; set; }
    public DbSet<JobListing> JobListings { get; set; }
    public DbSet<JobApplication> JobApplications { get; set; }
    public DbSet<Client> Clients { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<InvoiceItem> InvoiceItems { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<ProjectDocument> ProjectDocuments { get; set; }
    public DbSet<SiteSetting> SiteSettings { get; set; }
    public DbSet<Testimonial> Testimonials { get; set; }
    public DbSet<ContactForm> ContactForms { get; set; }
    public DbSet<Feedback> Feedbacks { get; set; }
    public DbSet<LegalPage> LegalPages { get; set; }
    public DbSet<LegalPageSection> LegalPageSections { get; set; }
    public DbSet<AboutPage> AboutPages { get; set; }
    public DbSet<AboutPageSection> AboutPageSections { get; set; }
    public DbSet<HeroSection> HeroSections { get; set; }

    // Chatbot entities
    public DbSet<ChatbotIntent> ChatbotIntents { get; set; }
    public DbSet<ChatbotResponse> ChatbotResponses { get; set; }
    public DbSet<ChatbotQuickAction> ChatbotQuickActions { get; set; }
    public DbSet<ChatbotKeyword> ChatbotKeywords { get; set; }
    public DbSet<HeroSlide> HeroSlides { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure relationships
        builder.Entity<Project>()
            .HasOne(p => p.Service)
            .WithMany(s => s.Projects)
            .HasForeignKey(p => p.ServiceId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Project>()
            .HasOne(p => p.Client)
            .WithMany(c => c.Projects)
            .HasForeignKey(p => p.ClientId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Project>()
            .HasMany(p => p.Technologies)
            .WithMany(t => t.Projects)
            .UsingEntity(j => j.ToTable("ProjectTechnologies"));

        builder.Entity<Invoice>()
            .HasOne(i => i.Client)
            .WithMany(c => c.Invoices)
            .HasForeignKey(i => i.ClientId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Invoice>()
            .HasOne(i => i.Project)
            .WithMany(p => p.Invoices)
            .HasForeignKey(i => i.ProjectId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Payment>()
            .HasOne(p => p.Invoice)
            .WithMany(i => i.Payments)
            .HasForeignKey(p => p.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<InvoiceItem>()
            .HasOne(ii => ii.Invoice)
            .WithMany(i => i.Items)
            .HasForeignKey(ii => ii.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<Message>()
            .HasOne(m => m.Project)
            .WithMany(p => p.Messages)
            .HasForeignKey(m => m.ProjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ProjectDocument>()
            .HasOne(pd => pd.Project)
            .WithMany(p => p.Documents)
            .HasForeignKey(pd => pd.ProjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<JobApplication>()
            .HasOne(ja => ja.JobListing)
            .WithMany(jl => jl.Applications)
            .HasForeignKey(ja => ja.JobListingId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure SiteSetting Icon field as optional for Entity Framework validation
        builder.Entity<SiteSetting>()
            .Property(s => s.Icon)
            .IsRequired(false); // Make Icon field optional for EF validation

        // Configure Testimonial ClientPhotoUrl field as optional for Entity Framework validation
        builder.Entity<Testimonial>()
            .Property(t => t.ClientPhotoUrl)
            .IsRequired(false); // Make ClientPhotoUrl field optional for EF validation

        // Configure Project ImageUrl field as optional for Entity Framework validation
        builder.Entity<Project>()
            .Property(p => p.ImageUrl)
            .IsRequired(false); // Make ImageUrl field optional for EF validation

        // Configure Client LogoUrl field as optional for Entity Framework validation
        builder.Entity<Client>()
            .Property(c => c.LogoUrl)
            .IsRequired(false); // Make LogoUrl field optional for EF validation

        // Configure Feedback relationships
        builder.Entity<Feedback>()
            .HasOne(f => f.Client)
            .WithMany()
            .HasForeignKey(f => f.ClientId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Feedback>()
            .HasOne(f => f.Project)
            .WithMany()
            .HasForeignKey(f => f.ProjectId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure Feedback optional fields
        builder.Entity<Feedback>()
            .Property(f => f.Rating)
            .IsRequired(false);

        builder.Entity<Feedback>()
            .Property(f => f.AdminResponse)
            .IsRequired(false);

        builder.Entity<Feedback>()
            .Property(f => f.AdminName)
            .IsRequired(false);

        // Configure LegalPage relationships
        builder.Entity<LegalPageSection>()
            .HasOne(lps => lps.LegalPage)
            .WithMany(lp => lp.Sections)
            .HasForeignKey(lps => lps.LegalPageId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure LegalPage unique slug
        builder.Entity<LegalPage>()
            .HasIndex(lp => lp.Slug)
            .IsUnique();

        // Configure AboutPage relationships
        builder.Entity<AboutPageSection>()
            .HasOne(aps => aps.AboutPage)
            .WithMany(ap => ap.Sections)
            .HasForeignKey(aps => aps.AboutPageId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure HeroSection relationships
        builder.Entity<HeroSlide>()
            .HasOne(hs => hs.HeroSection)
            .WithMany(h => h.Slides)
            .HasForeignKey(hs => hs.HeroSectionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure Chatbot entity relationships
        builder.Entity<ChatbotResponse>()
            .HasOne(cr => cr.ChatbotIntent)
            .WithMany(ci => ci.Responses)
            .HasForeignKey(cr => cr.ChatbotIntentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotKeyword>()
            .HasOne(ck => ck.ChatbotIntent)
            .WithMany(ci => ci.Keywords)
            .HasForeignKey(ck => ck.ChatbotIntentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotQuickAction>()
            .HasOne(cqa => cqa.ChatbotResponse)
            .WithMany(cr => cr.QuickActions)
            .HasForeignKey(cqa => cqa.ChatbotResponseId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotQuickAction>()
            .HasOne(cqa => cqa.ChatbotIntent)
            .WithMany()
            .HasForeignKey(cqa => cqa.ChatbotIntentId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure AboutPage optional fields
        builder.Entity<AboutPage>()
            .Property(ap => ap.HeroImageUrl)
            .IsRequired(false);

        builder.Entity<AboutPage>()
            .Property(ap => ap.StoryImageUrl)
            .IsRequired(false);

        builder.Entity<AboutPageSection>()
            .Property(aps => aps.ImageUrl)
            .IsRequired(false);
    }
}
