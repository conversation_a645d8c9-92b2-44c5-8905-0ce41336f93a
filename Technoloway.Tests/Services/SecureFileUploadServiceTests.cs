using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Technoloway.Core.Configuration;
using Technoloway.Web.Services;
using Xunit;

namespace Technoloway.Tests.Services;

public class SecureFileUploadServiceTests
{
    private readonly Mock<IWebHostEnvironment> _mockEnvironment;
    private readonly Mock<ILogger<SecureFileUploadService>> _mockLogger;
    private readonly Mock<IOptions<FileUploadSettings>> _mockOptions;
    private readonly SecureFileUploadService _service;
    private readonly FileUploadSettings _settings;

    public SecureFileUploadServiceTests()
    {
        _mockEnvironment = new Mock<IWebHostEnvironment>();
        _mockLogger = new Mock<ILogger<SecureFileUploadService>>();
        _mockOptions = new Mock<IOptions<FileUploadSettings>>();

        _settings = new FileUploadSettings
        {
            MaxFileSizeBytes = 5242880, // 5MB
            AllowedImageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" },
            AllowedDocumentExtensions = new[] { ".pdf", ".doc", ".docx", ".txt" },
            UploadPath = "wwwroot/uploads",
            ScanForViruses = false,
            EnableContentTypeValidation = true,
            EnableFileSignatureValidation = true
        };

        _mockOptions.Setup(x => x.Value).Returns(_settings);
        _mockEnvironment.Setup(x => x.WebRootPath).Returns("/app/wwwroot");

        _service = new SecureFileUploadService(_mockEnvironment.Object, _mockLogger.Object, _mockOptions.Object);
    }

    [Fact]
    public void GenerateSecureFileName_ShouldSanitizeFileName()
    {
        // Arrange
        var originalFileName = "test file with spaces & special chars!@#.jpg";

        // Act
        var result = _service.GenerateSecureFileName(originalFileName);

        // Assert
        result.Should().NotContain(" ");
        result.Should().NotContain("&");
        result.Should().NotContain("!");
        result.Should().NotContain("@");
        result.Should().NotContain("#");
        result.Should().EndWith(".jpg");
        result.Should().Contain("testfilewithspacesspecialchars");
    }

    [Fact]
    public void GenerateSecureFileName_ShouldAddTimestampAndRandomComponent()
    {
        // Arrange
        var originalFileName = "test.jpg";

        // Act
        var result1 = _service.GenerateSecureFileName(originalFileName);
        var result2 = _service.GenerateSecureFileName(originalFileName);

        // Assert
        result1.Should().NotBe(result2);
        result1.Should().MatchRegex(@"test-\d{8}-\d{6}-[a-f0-9]{8}\.jpg");
        result2.Should().MatchRegex(@"test-\d{8}-\d{6}-[a-f0-9]{8}\.jpg");
    }

    [Fact]
    public void GenerateSecureFileName_ShouldHandleEmptyFileName()
    {
        // Arrange
        var originalFileName = "...jpg"; // Only extension

        // Act
        var result = _service.GenerateSecureFileName(originalFileName);

        // Assert
        result.Should().StartWith("file-");
        result.Should().EndWith(".jpg");
    }

    [Fact]
    public void GenerateSecureFileName_ShouldLimitLength()
    {
        // Arrange
        var longFileName = new string('a', 100) + ".jpg";

        // Act
        var result = _service.GenerateSecureFileName(longFileName);

        // Assert
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(result);
        var baseName = nameWithoutExtension.Split('-')[0];
        baseName.Length.Should().BeLessOrEqualTo(50);
    }

    [Theory]
    [InlineData("test.jpg", "image/jpeg", true)]
    [InlineData("test.png", "image/png", true)]
    [InlineData("test.gif", "image/gif", true)]
    [InlineData("test.webp", "image/webp", true)]
    [InlineData("test.bmp", "image/bmp", false)] // Not allowed extension
    [InlineData("test.jpg", "application/pdf", false)] // Wrong MIME type
    public void IsValidImageFile_ShouldValidateExtensionAndMimeType(string fileName, string contentType, bool expected)
    {
        // Arrange
        var mockFile = CreateMockFile(fileName, contentType, 1024);

        // Act
        var result = _service.IsValidImageFile(mockFile);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void IsValidImageFile_ShouldRejectOversizedFiles()
    {
        // Arrange
        var mockFile = CreateMockFile("test.jpg", "image/jpeg", _settings.MaxFileSizeBytes + 1);

        // Act
        var result = _service.IsValidImageFile(mockFile);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsValidImageFile_ShouldRejectNullOrEmptyFiles()
    {
        // Arrange & Act & Assert
        _service.IsValidImageFile(null!).Should().BeFalse();
        
        var emptyFile = CreateMockFile("test.jpg", "image/jpeg", 0);
        _service.IsValidImageFile(emptyFile).Should().BeFalse();
    }

    [Theory]
    [InlineData("document.pdf", "application/pdf", true)]
    [InlineData("document.doc", "application/msword", true)]
    [InlineData("document.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", true)]
    [InlineData("document.txt", "text/plain", true)]
    [InlineData("document.exe", "application/octet-stream", false)] // Not allowed extension
    [InlineData("document.pdf", "image/jpeg", false)] // Wrong MIME type
    public void IsValidDocumentFile_ShouldValidateExtensionAndMimeType(string fileName, string contentType, bool expected)
    {
        // Arrange
        var mockFile = CreateMockFile(fileName, contentType, 1024);

        // Act
        var result = _service.IsValidDocumentFile(mockFile);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void DeleteFile_ShouldPreventDirectoryTraversal()
    {
        // Arrange
        var maliciousPath = "../../../etc/passwd";

        // Act
        var result = _service.DeleteFile(maliciousPath);

        // Assert
        result.Should().BeFalse();
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("outside allowed directories")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Theory]
    [InlineData("/images/test/file.jpg", true)]
    [InlineData("/documents/test/file.pdf", true)]
    [InlineData("/uploads/malicious.exe", false)]
    [InlineData("../../../etc/passwd", false)]
    public void DeleteFile_ShouldOnlyAllowFilesInAllowedDirectories(string filePath, bool shouldAllow)
    {
        // Act
        var result = _service.DeleteFile(filePath);

        // Assert
        if (shouldAllow)
        {
            // Should not log warning (file doesn't exist, but path is allowed)
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("outside allowed directories")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Never);
        }
        else
        {
            result.Should().BeFalse();
        }
    }

    private static IFormFile CreateMockFile(string fileName, string contentType, long length)
    {
        var mockFile = new Mock<IFormFile>();
        mockFile.Setup(f => f.FileName).Returns(fileName);
        mockFile.Setup(f => f.ContentType).Returns(contentType);
        mockFile.Setup(f => f.Length).Returns(length);
        
        // Create a memory stream with some dummy data
        var stream = new MemoryStream(new byte[length]);
        mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
        
        return mockFile.Object;
    }
}
