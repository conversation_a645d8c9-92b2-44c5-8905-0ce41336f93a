{"format": 1, "restore": {"/Volumes/Files/Technoloway (Processing)/Technoloway.Tests/Technoloway.Tests.csproj": {}}, "projects": {"/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj", "projectName": "Technoloway.Core", "projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj", "projectName": "Technoloway.Infrastructure", "projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Tests/Technoloway.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/Files/Technoloway (Processing)/Technoloway.Tests/Technoloway.Tests.csproj", "projectName": "Technoloway.Tests", "projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Tests/Technoloway.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj"}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj"}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/Technoloway.Web.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/Technoloway.Web.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.1, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.Configuration.UserSecrets": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/Technoloway.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/Technoloway.Web.csproj", "projectName": "Technoloway.Web", "projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/Technoloway.Web.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Core/Technoloway.Core.csproj"}, "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj": {"projectPath": "/Volumes/Files/Technoloway (Processing)/Technoloway.Infrastructure/Technoloway.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.11, )"}, "Stripe.net": {"target": "Package", "version": "[48.1.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}