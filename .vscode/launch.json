{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/Technoloway.Web/bin/Debug/net8.0/Technoloway.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Technoloway.Web",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "DOTNET_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Technoloway.Web/Views"
            },
            "console": "integratedTerminal",
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "name": ".NET Core Launch (web - HTTPS)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/Technoloway.Web/bin/Debug/net8.0/Technoloway.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Technoloway.Web",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "DOTNET_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:7224;http://localhost:5258"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Technoloway.Web/Views"
            },
            "console": "integratedTerminal",
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:5258",
            "webRoot": "${workspaceFolder}/Technoloway.Web/wwwroot"
        },
        {
            "name": "Launch Chrome (HTTPS)",
            "request": "launch",
            "type": "chrome",
            "url": "https://localhost:7224",
            "webRoot": "${workspaceFolder}/Technoloway.Web/wwwroot"
        }
    ],
    "compounds": [
        {
            "name": "Run Web App (.NET + Chrome)",
            "configurations": [".NET Core Launch (web)", "Launch Chrome"]
        },
        {
            "name": "Run Web App HTTPS (.NET + Chrome)",
            "configurations": [".NET Core Launch (web - HTTPS)", "Launch Chrome (HTTPS)"]
        }
    ]
}