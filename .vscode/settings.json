{"dotnet.defaultSolution": "Technoloway.sln", "dotnet.server.useOmnisharp": true, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.useModernNet": false, "omnisharp.path": "latest", "omnisharp.projectLoadTimeout": 120, "omnisharp.maxProjectResults": 250, "omnisharp.enableMsBuildLoadProjectsOnDemand": false, "omnisharp.autoStart": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "files.exclude": {"**/bin": true, "**/obj": true}}