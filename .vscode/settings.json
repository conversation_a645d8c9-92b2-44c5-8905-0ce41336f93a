{"dotnet.defaultSolution": "Technoloway.sln", "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.useModernNet": true, "omnisharp.path": "latest", "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true, "dotnet.formatting.organizeImportsOnFormat": true}