using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces
{
    public interface IHeroSectionRepository : IRepository<HeroSection>
    {
        Task<HeroSection?> GetActiveWithSlidesAsync();
        Task<HeroSection?> GetActiveByPageWithSlidesAsync(string pageName);
        Task<HeroSection?> GetByIdWithSlidesAsync(int id);
        Task<List<HeroSection>> GetAllWithSlidesAsync();
        Task<List<HeroSection>> GetByPageWithSlidesAsync(string pageName);
    }
}
