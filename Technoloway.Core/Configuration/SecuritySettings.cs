using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Configuration;

public class SecuritySettings
{
    public const string SectionName = "Security";

    [Required]
    public bool RequireEmailConfirmation { get; set; } = true;

    [Required]
    public bool EnableAccountLockout { get; set; } = true;

    [Range(3, 10)]
    public int MaxFailedAccessAttempts { get; set; } = 5;

    [Required]
    public TimeSpan LockoutTimeSpan { get; set; } = TimeSpan.FromMinutes(15);

    [Required]
    public bool PasswordRequireDigit { get; set; } = true;

    [Required]
    public bool PasswordRequireLowercase { get; set; } = true;

    [Required]
    public bool PasswordRequireUppercase { get; set; } = true;

    [Required]
    public bool PasswordRequireNonAlphanumeric { get; set; } = true;

    [Range(8, 128)]
    public int PasswordRequiredLength { get; set; } = 8;

    [Range(5, 480)]
    public int SessionTimeoutMinutes { get; set; } = 30;

    public bool RequireHttps { get; set; } = false;

    public bool EnableHsts { get; set; } = false;

    public TimeSpan HstsMaxAge { get; set; } = TimeSpan.FromDays(365);
}

public class FileUploadSettings
{
    public const string SectionName = "FileUpload";

    [Range(1024, *********)] // 1KB to 100MB
    public long MaxFileSizeBytes { get; set; } = 5242880; // 5MB

    [Required]
    public string[] AllowedImageExtensions { get; set; } = Array.Empty<string>();

    [Required]
    public string[] AllowedDocumentExtensions { get; set; } = Array.Empty<string>();

    [Required]
    public string UploadPath { get; set; } = "wwwroot/uploads";

    public bool ScanForViruses { get; set; } = false;

    public bool EnableContentTypeValidation { get; set; } = true;

    public bool EnableFileSignatureValidation { get; set; } = true;
}

public class CachingSettings
{
    public const string SectionName = "Caching";

    [Range(1, 1440)]
    public int DefaultCacheTimeMinutes { get; set; } = 30;

    [Range(1, 60)]
    public int SlidingExpirationMinutes { get; set; } = 10;

    [Range(1, 1440)]
    public int AbsoluteExpirationMinutes { get; set; } = 60;
}

public class RateLimitingSettings
{
    public const string SectionName = "RateLimiting";

    public bool EnableRateLimiting { get; set; } = true;

    [Range(10, 1000)]
    public int RequestsPerMinute { get; set; } = 100;

    [Range(50, 2000)]
    public int BurstLimit { get; set; } = 200;
}
