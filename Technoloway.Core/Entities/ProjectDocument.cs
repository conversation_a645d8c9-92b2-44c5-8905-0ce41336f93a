using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ProjectDocument : BaseEntity
{
    public string FileName { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string UploadedById { get; set; } = string.Empty; // Identity user ID
    public string UploadedByName { get; set; } = string.Empty;
    
    // Navigation properties
    public int ProjectId { get; set; }
    public Project Project { get; set; } = null!;
}
