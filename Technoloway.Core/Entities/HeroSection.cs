using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities
{
    public class HeroSection : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        public string? MetaDescription { get; set; }

        [StringLength(500)]
        public string? MetaKeywords { get; set; }

        // Page Assignment
        [Required]
        [StringLength(50)]
        public string PageName { get; set; } = "Home"; // Home, About, Services, Projects, Contact, etc.

        // Main Hero Content
        [Required]
        [StringLength(200)]
        public string MainTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string MainSubtitle { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? MainDescription { get; set; }

        // Primary CTA Button
        [StringLength(50)]
        public string? PrimaryButtonText { get; set; }

        [StringLength(200)]
        public string? PrimaryButtonUrl { get; set; }

        // Secondary CTA Button
        [StringLength(50)]
        public string? SecondaryButtonText { get; set; }

        [StringLength(200)]
        public string? SecondaryButtonUrl { get; set; }

        // Slideshow Settings
        public bool EnableSlideshow { get; set; } = true;
        public int SlideshowSpeed { get; set; } = 5000; // milliseconds
        public bool AutoPlay { get; set; } = true;
        public bool ShowDots { get; set; } = true;
        public bool ShowArrows { get; set; } = true;

        // Floating Elements Settings
        public bool EnableFloatingElements { get; set; } = true;
        public string? FloatingElementsConfig { get; set; } // JSON config for floating elements

        public bool IsActive { get; set; } = true;
        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        // Navigation properties
        public virtual ICollection<HeroSlide> Slides { get; set; } = new List<HeroSlide>();
    }

    public class HeroSlide : BaseEntity
    {
        [Required]
        public int HeroSectionId { get; set; }

        [Required]
        public string Content { get; set; } = string.Empty; // Combined Title, Subtitle, and Description with rich text

        // Media Content
        [StringLength(10)]
        public string MediaType { get; set; } = "image"; // "image" or "video"

        [StringLength(500)]
        public string? ImageUrl { get; set; }

        [StringLength(500)]
        public string? VideoUrl { get; set; }

        [StringLength(200)]
        public string? MediaAlt { get; set; }

        // Video Settings
        public bool VideoAutoPlay { get; set; } = true;
        public bool VideoMuted { get; set; } = true;
        public bool VideoLoop { get; set; } = true;
        public bool VideoControls { get; set; } = false;

        // Slide-specific CTA
        [StringLength(50)]
        public string? ButtonText { get; set; }

        [StringLength(200)]
        public string? ButtonUrl { get; set; }

        // Display settings
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; } = true;

        // Animation settings
        [StringLength(50)]
        public string? AnimationType { get; set; } = "fade"; // fade, slide, zoom

        public int Duration { get; set; } = 5000; // milliseconds

        // Navigation property
        public virtual HeroSection HeroSection { get; set; } = null!;
    }
}
