using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities
{
    public class AboutPage : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        public string? MetaDescription { get; set; }

        [StringLength(500)]
        public string? MetaKeywords { get; set; }

        // Hero Section
        [Required]
        [StringLength(200)]
        public string HeroTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string HeroSubtitle { get; set; } = string.Empty;

        [StringLength(500)]
        public string? HeroImageUrl { get; set; }

        // Main Story Section
        [Required]
        [StringLength(100)]
        public string StoryTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(300)]
        public string StorySubtitle { get; set; } = string.Empty;

        [Required]
        public string StoryContent { get; set; } = string.Empty;

        [StringLength(500)]
        public string? StoryImageUrl { get; set; }

        // Stats Section
        [StringLength(50)]
        public string? Stat1Number { get; set; }

        [StringLength(100)]
        public string? Stat1Label { get; set; }

        [StringLength(50)]
        public string? Stat2Number { get; set; }

        [StringLength(100)]
        public string? Stat2Label { get; set; }

        [StringLength(50)]
        public string? Stat3Number { get; set; }

        [StringLength(100)]
        public string? Stat3Label { get; set; }

        [StringLength(50)]
        public string? Stat4Number { get; set; }

        [StringLength(100)]
        public string? Stat4Label { get; set; }

        // Mission & Vision Section
        [StringLength(100)]
        public string? MissionTitle { get; set; }

        public string? MissionContent { get; set; }

        [StringLength(100)]
        public string? VisionTitle { get; set; }

        public string? VisionContent { get; set; }

        // Values Section
        [StringLength(100)]
        public string? ValuesTitle { get; set; }

        public string? ValuesContent { get; set; }

        // CTA Section
        [StringLength(100)]
        public string? CtaTitle { get; set; }

        [StringLength(300)]
        public string? CtaSubtitle { get; set; }

        [StringLength(50)]
        public string? CtaPrimaryButtonText { get; set; }

        [StringLength(200)]
        public string? CtaPrimaryButtonUrl { get; set; }

        [StringLength(50)]
        public string? CtaSecondaryButtonText { get; set; }

        [StringLength(200)]
        public string? CtaSecondaryButtonUrl { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        // Navigation properties for sections
        public virtual ICollection<AboutPageSection> Sections { get; set; } = new List<AboutPageSection>();
    }

    public class AboutPageSection : BaseEntity
    {
        [Required]
        public int AboutPageId { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        [StringLength(50)]
        public string? IconClass { get; set; }

        [StringLength(500)]
        public string? ImageUrl { get; set; }

        public int DisplayOrder { get; set; }

        public bool IsActive { get; set; } = true;

        [StringLength(50)]
        public string SectionType { get; set; } = "content"; // content, feature, value, etc.

        // Navigation property
        public virtual AboutPage AboutPage { get; set; } = null!;
    }
}
