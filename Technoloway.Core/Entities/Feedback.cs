using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Feedback : BaseEntity
{
    [Required(ErrorMessage = "Subject is required")]
    [StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters")]
    public string Subject { get; set; } = string.Empty;

    [Required(ErrorMessage = "Message is required")]
    [StringLength(2000, MinimumLength = 10, ErrorMessage = "Message must be between 10 and 2000 characters")]
    public string Message { get; set; } = string.Empty;

    [Required(ErrorMessage = "Feedback type is required")]
    [StringLength(50, ErrorMessage = "Feedback type cannot exceed 50 characters")]
    public string FeedbackType { get; set; } = string.Empty; // Bug Report, Feature Request, General Feedback, Complaint, Compliment

    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
    public int? Rating { get; set; } // Optional rating 1-5 stars

    [StringLength(100, ErrorMessage = "Client name cannot exceed 100 characters")]
    public string ClientName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Client email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string ClientEmail { get; set; } = string.Empty;

    [StringLength(20, ErrorMessage = "Priority cannot exceed 20 characters")]
    public string Priority { get; set; } = "Medium"; // Low, Medium, High, Critical

    [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
    public string Status { get; set; } = "New"; // New, In Review, In Progress, Resolved, Closed

    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }
    public DateTime? ResolvedAt { get; set; }

    [StringLength(1000, ErrorMessage = "Admin response cannot exceed 1000 characters")]
    public string AdminResponse { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "Admin name cannot exceed 100 characters")]
    public string AdminName { get; set; } = string.Empty;

    public DateTime? ResponseDate { get; set; }

    // Navigation properties
    public int? ClientId { get; set; }
    public Client? Client { get; set; }

    public int? ProjectId { get; set; }
    public Project? Project { get; set; }
}
