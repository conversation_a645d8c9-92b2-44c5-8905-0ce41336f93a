using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Payment : BaseEntity
{
    [StringLength(50, ErrorMessage = "Transaction ID cannot exceed 50 characters")]
    public string TransactionId { get; set; } = string.Empty;

    [Required(ErrorMessage = "Payment amount is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Payment amount must be greater than 0")]
    public decimal Amount { get; set; }

    [Required(ErrorMessage = "Payment date is required")]
    public DateTime PaymentDate { get; set; } = DateTime.UtcNow;

    [Required(ErrorMessage = "Payment method is required")]
    [StringLength(50, ErrorMessage = "Payment method cannot exceed 50 characters")]
    public string PaymentMethod { get; set; } = string.Empty; // Credit Card, PayPal, Bank Transfer, etc.

    [Required(ErrorMessage = "Payment status is required")]
    [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
    public string Status { get; set; } = "Completed"; // Pending, Completed, Failed, Refunded

    [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
    public string Notes { get; set; } = string.Empty;

    // Navigation properties
    [Required(ErrorMessage = "Invoice is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid invoice")]
    public int InvoiceId { get; set; }
    public Invoice? Invoice { get; set; }
}
