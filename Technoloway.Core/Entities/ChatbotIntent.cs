using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ChatbotIntent : BaseEntity
{
    [Required(ErrorMessage = "Intent name is required")]
    [StringLength(50, ErrorMessage = "Intent name cannot exceed 50 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Display name is required")]
    [StringLength(100, ErrorMessage = "Display name cannot exceed 100 characters")]
    public string DisplayName { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
    public string IconClass { get; set; } = string.Empty;

    // Navigation properties
    public virtual ICollection<ChatbotResponse> Responses { get; set; } = new List<ChatbotResponse>();
    public virtual ICollection<ChatbotKeyword> Keywords { get; set; } = new List<ChatbotKeyword>();
}
