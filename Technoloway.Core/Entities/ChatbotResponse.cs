using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ChatbotResponse : BaseEntity
{
    [Required(ErrorMessage = "Response title is required")]
    [StringLength(200, ErrorMessage = "Response title cannot exceed 200 characters")]
    public string Title { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    [StringLength(2000, ErrorMessage = "Content cannot exceed 2000 characters")]
    public string Content { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "Response type cannot exceed 50 characters")]
    public string ResponseType { get; set; } = "text"; // text, html, dynamic

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    // Conditions for when to show this response
    [StringLength(500, ErrorMessage = "Conditions cannot exceed 500 characters")]
    public string Conditions { get; set; } = string.Empty; // JSON string for complex conditions

    // Template variables that can be replaced dynamically
    [StringLength(1000, ErrorMessage = "Template variables cannot exceed 1000 characters")]
    public string TemplateVariables { get; set; } = string.Empty; // JSON string

    // Navigation properties
    [Required]
    public int ChatbotIntentId { get; set; }
    public virtual ChatbotIntent ChatbotIntent { get; set; } = null!;
    
    public virtual ICollection<ChatbotQuickAction> QuickActions { get; set; } = new List<ChatbotQuickAction>();
}
