using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class JobListing : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty; // Full-time, Part-time, Contract, etc.
    public decimal? SalaryMin { get; set; }
    public decimal? SalaryMax { get; set; }
    public string SalaryCurrency { get; set; } = "USD";
    public bool IsRemote { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public DateTime? ExpiresAt { get; set; }
    
    // Navigation properties
    public ICollection<JobApplication> Applications { get; set; } = new List<JobApplication>();
}
