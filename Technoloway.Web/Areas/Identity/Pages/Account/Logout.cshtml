@page
@model Technoloway.Web.Areas.Identity.Pages.Account.LogoutModel
@{
    ViewData["Title"] = "Log out";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Logout</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Logout</span> Successful
                    </h1>
                    <p class="login-subtitle">
                        You have been successfully logged out
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Goodbye!</span>
                        </h3>
                        <p class="card-subtitle">
                            Thank you for using our services
                        </p>
                    </div>

                    <div class="card-body text-center">
                        <div class="logout-success-icon mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        
                        <p class="mb-4">You have been successfully logged out of your account.</p>
                        
                        <div class="d-grid gap-2">
                            <a href="/" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>Return to Homepage
                            </a>
                            <a asp-page="./Login" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Login Again
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
