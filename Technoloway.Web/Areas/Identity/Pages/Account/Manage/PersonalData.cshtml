@page
@model Technoloway.Web.Areas.Identity.Pages.Account.Manage.PersonalDataModel
@{
    ViewData["Title"] = "Personal Data";
    ViewData["ActivePage"] = "PersonalData";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <a asp-page="./Index" class="breadcrumb-link">Account Settings</a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Personal Data</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Personal</span> Data
                    </h1>
                    <p class="login-subtitle">
                        Manage your personal data and privacy settings
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="modern-card login-card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-user-shield me-2"></i>Personal Data Management
                        </h3>
                    </div>

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="data-action-card">
                                    <div class="data-icon">
                                        <i class="fas fa-download text-primary"></i>
                                    </div>
                                    <h5>Download Your Data</h5>
                                    <p class="text-muted">Download a copy of all your personal data stored in our system.</p>
                                    <form asp-page-handler="DownloadPersonalData" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>Download Data
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <div class="data-action-card">
                                    <div class="data-icon">
                                        <i class="fas fa-trash-alt text-danger"></i>
                                    </div>
                                    <h5>Delete Your Account</h5>
                                    <p class="text-muted">Permanently delete your account and all associated data.</p>
                                    <a asp-page="./DeletePersonalData" class="btn btn-danger">
                                        <i class="fas fa-trash-alt me-2"></i>Delete Account
                                    </a>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="privacy-info">
                            <h5><i class="fas fa-shield-alt me-2"></i>Privacy Information</h5>
                            <p class="text-muted">
                                Your personal data is protected and handled according to our privacy policy. 
                                We collect only the necessary information to provide our services and never 
                                share your data with third parties without your consent.
                            </p>
                            
                            <div class="privacy-points">
                                <div class="privacy-point">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Data is encrypted and stored securely</span>
                                </div>
                                <div class="privacy-point">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>You have full control over your data</span>
                                </div>
                                <div class="privacy-point">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>Data is only used to provide our services</span>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a asp-page="./Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.data-action-card {
    text-align: center;
    padding: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    height: 100%;
}

.data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.privacy-points {
    margin-top: 1rem;
}

.privacy-point {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}
</style>
