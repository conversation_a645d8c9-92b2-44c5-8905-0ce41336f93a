@page
@model Technoloway.Web.Areas.Identity.Pages.Account.LoginModel

@{
    ViewData["Title"] = "Client Login";
    Layout = "_ClientLoginLayout";
}

<!-- Modern Client Login Header -->
<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Client Login</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Client</span> Portal
                    </h1>
                    <p class="login-subtitle">
                        Access your projects, documents, and collaborate with our team
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Client Login Section -->
<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Welcome</span> Back
                        </h3>
                        <p class="card-subtitle">
                            Sign in to access your client portal
                        </p>
                    </div>

                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                        {
                            <div class="modern-alert error">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <h4>Login Failed</h4>
                                    <p>@Model.ErrorMessage</p>
                                </div>
                            </div>
                        }

                        <form id="account" method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

                            <div class="form-group">
                                <label asp-for="Input.Email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    <span>Email Address</span>
                                </label>
                                <input asp-for="Input.Email" class="form-input" placeholder="Enter your email address" autocomplete="username" />
                                <span asp-validation-for="Input.Email" class="form-error"></span>
                            </div>

                            <div class="form-group">
                                <label asp-for="Input.Password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    <span>Password</span>
                                </label>
                                <div class="password-input-wrapper">
                                    <input asp-for="Input.Password" class="form-input" placeholder="Enter your password" autocomplete="current-password" />
                                    <button type="button" class="password-toggle" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.Password" class="form-error"></span>
                            </div>

                            <div class="form-check-wrapper">
                                <label class="modern-checkbox">
                                    <input asp-for="Input.RememberMe" type="checkbox" />
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">Keep me signed in</span>
                                </label>
                            </div>

                            <div class="form-actions">
                                <button id="login-submit" type="submit" class="modern-btn client-primary large full-width">
                                    <span class="btn-text">Sign In</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </span>
                                </button>
                            </div>
                        </form>

                        <div class="login-footer">
                            <div class="footer-links">
                                <a href="/" class="footer-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Website</span>
                                </a>
                                <a asp-page="./ForgotPassword" class="footer-link">
                                    <i class="fas fa-key"></i>
                                    <span>Forgot Password?</span>
                                </a>
                            </div>
                            <div class="additional-links">
                                <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl" class="additional-link">
                                    <i class="fas fa-user-plus"></i>
                                    <span>New client? Register here</span>
                                </a>
                                <a asp-page="./ResendEmailConfirmation" class="additional-link">
                                    <i class="fas fa-envelope-open"></i>
                                    <span>Resend email confirmation</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Client Portal Notice -->
                <div class="client-portal-notice">
                    <div class="portal-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="portal-content">
                        <h5>Client Portal Access</h5>
                        <p>Manage your projects, view progress updates, and communicate with our team in your dedicated client portal.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Enhanced client login interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Form input animations
            const formInputs = document.querySelectorAll('.form-input');

            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // Check if input has value on load
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });

            // Form submission enhancement
            const loginForm = document.querySelector('.modern-form');
            const submitBtn = document.querySelector('.modern-btn.client-primary');

            if (loginForm && submitBtn) {
                loginForm.addEventListener('submit', function(e) {
                    submitBtn.classList.add('loading');
                    submitBtn.innerHTML = `
                        <span class="btn-text">Signing In...</span>
                        <span class="btn-icon">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    `;
                });
            }

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
                element.classList.add('floating-animation');
            });
        });

        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="Input.Password"]');
            const passwordEye = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordEye.classList.remove('fa-eye');
                passwordEye.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordEye.classList.remove('fa-eye-slash');
                passwordEye.classList.add('fa-eye');
            }
        }
    </script>
}
