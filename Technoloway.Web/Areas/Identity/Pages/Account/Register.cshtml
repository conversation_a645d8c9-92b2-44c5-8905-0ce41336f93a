@page
@model Technoloway.Web.Areas.Identity.Pages.Account.RegisterModel

@{
    ViewData["Title"] = "Client Registration";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <a asp-page="./Login" class="breadcrumb-link">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Register</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Create</span> Account
                    </h1>
                    <p class="login-subtitle">
                        Join our client portal to manage your projects and collaborate with our team
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Client Registration Section -->
<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Client</span> Registration
                        </h3>
                        <p class="card-subtitle">
                            Create your account to access our client portal
                        </p>
                    </div>

                    <div class="card-body">
                        <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Input.Email" class="form-label">
                                            <i class="fas fa-envelope"></i>
                                            <span>Email Address</span>
                                        </label>
                                        <input asp-for="Input.Email" class="form-input" placeholder="Enter your email address" autocomplete="username" />
                                        <span asp-validation-for="Input.Email" class="form-error"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Input.ConfirmEmail" class="form-label">
                                            <i class="fas fa-envelope-open"></i>
                                            <span>Confirm Email</span>
                                        </label>
                                        <input asp-for="Input.ConfirmEmail" class="form-input" placeholder="Confirm your email address" autocomplete="username" />
                                        <span asp-validation-for="Input.ConfirmEmail" class="form-error"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Input.Password" class="form-label">
                                            <i class="fas fa-lock"></i>
                                            <span>Password</span>
                                        </label>
                                        <div class="password-input-wrapper">
                                            <input asp-for="Input.Password" class="form-input" placeholder="Create a strong password" autocomplete="new-password" />
                                            <button type="button" class="password-toggle" onclick="togglePassword('Input_Password')">
                                                <i class="fas fa-eye" id="password-eye-1"></i>
                                            </button>
                                        </div>
                                        <span asp-validation-for="Input.Password" class="form-error"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Input.ConfirmPassword" class="form-label">
                                            <i class="fas fa-lock"></i>
                                            <span>Confirm Password</span>
                                        </label>
                                        <div class="password-input-wrapper">
                                            <input asp-for="Input.ConfirmPassword" class="form-input" placeholder="Confirm your password" autocomplete="new-password" />
                                            <button type="button" class="password-toggle" onclick="togglePassword('Input_ConfirmPassword')">
                                                <i class="fas fa-eye" id="password-eye-2"></i>
                                            </button>
                                        </div>
                                        <span asp-validation-for="Input.ConfirmPassword" class="form-error"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="password-requirements">
                                    <h6><i class="fas fa-shield-alt me-2"></i>Password Requirements:</h6>
                                    <ul class="requirements-list">
                                        <li><i class="fas fa-check"></i> At least 8 characters long</li>
                                        <li><i class="fas fa-check"></i> Contains uppercase and lowercase letters</li>
                                        <li><i class="fas fa-check"></i> Contains at least one number</li>
                                        <li><i class="fas fa-check"></i> Contains at least one special character</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="form-check-wrapper">
                                <label class="modern-checkbox">
                                    <input type="checkbox" required />
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">
                                        I agree to the <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>
                                    </span>
                                </label>
                            </div>

                            <button id="registerSubmit" type="submit" class="btn-modern primary full-width">
                                <i class="fas fa-user-plus"></i>
                                <span>Create Account</span>
                                <div class="btn-loading">
                                    <div class="spinner"></div>
                                </div>
                            </button>
                        </form>

                        <div class="login-footer">
                            <div class="footer-links">
                                <a href="/" class="footer-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Website</span>
                                </a>
                                <a asp-page="./Login" asp-route-returnUrl="@Model.ReturnUrl" class="footer-link">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>Already have an account? Sign in</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Client Portal Benefits -->
                <div class="client-portal-notice">
                    <div class="portal-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="portal-content">
                        <h5>Client Portal Benefits</h5>
                        <ul class="benefits-list">
                            <li><i class="fas fa-project-diagram"></i> Track project progress in real-time</li>
                            <li><i class="fas fa-comments"></i> Direct communication with our team</li>
                            <li><i class="fas fa-file-invoice"></i> Access invoices and billing information</li>
                            <li><i class="fas fa-cloud-download-alt"></i> Download project files and documents</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Form submission handling
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('registerSubmit');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
        });

        // Password strength indicator
        document.getElementById('Input_Password').addEventListener('input', function(e) {
            const password = e.target.value;
            const requirements = document.querySelectorAll('.requirements-list li');
            
            // Check each requirement
            const checks = [
                password.length >= 8,
                /[a-z]/.test(password) && /[A-Z]/.test(password),
                /\d/.test(password),
                /[^a-zA-Z0-9]/.test(password)
            ];
            
            requirements.forEach((req, index) => {
                const icon = req.querySelector('i');
                if (checks[index]) {
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-check-circle');
                    req.classList.add('valid');
                } else {
                    icon.classList.remove('fa-check-circle');
                    icon.classList.add('fa-check');
                    req.classList.remove('valid');
                }
            });
        });
    </script>
}

<style>
    .password-requirements {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .requirements-list {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0 0 0;
    }

    .requirements-list li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0;
        font-size: 0.875rem;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .requirements-list li.valid {
        color: #28a745;
    }

    .requirements-list li i {
        width: 16px;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .requirements-list li.valid i {
        color: #28a745;
    }

    .benefits-list {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0 0 0;
    }

    .benefits-list li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0;
        font-size: 0.875rem;
        color: #6c757d;
    }

    .benefits-list li i {
        width: 16px;
        color: #007bff;
    }

    .btn-modern.loading .btn-loading {
        display: flex;
    }

    .btn-modern.loading span {
        opacity: 0;
    }
</style>
