using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.ViewModels
{
    public class InvoiceAnalyticsViewModel
    {
        // Basic counts
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int PendingInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        public int OverdueInvoices { get; set; }

        // Financial totals
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal PartiallyPaidAmount { get; set; }
        public decimal OverdueAmount { get; set; }

        // Chart data
        public List<MonthlyInvoiceData> MonthlyData { get; set; } = new List<MonthlyInvoiceData>();
        public List<StatusDistributionData> StatusData { get; set; } = new List<StatusDistributionData>();
        public List<ClientRevenueData> ClientData { get; set; } = new List<ClientRevenueData>();

        // Recent activity
        public List<Invoice> RecentInvoices { get; set; } = new List<Invoice>();

        // Calculated metrics
        public decimal AverageInvoiceAmount { get; set; }
        public double AverageDaysToPayment { get; set; }
        public decimal MonthOverMonthGrowth { get; set; }
        public decimal YearOverYearGrowth { get; set; }

        // Calculated properties
        public decimal CollectionRate => TotalInvoices > 0 ? (decimal)PaidInvoices / TotalInvoices * 100 : 0;
        public decimal OutstandingAmount => TotalAmount - PaidAmount;
        public decimal PaymentRate => TotalAmount > 0 ? PaidAmount / TotalAmount * 100 : 0;
    }

    public class MonthlyInvoiceData
    {
        public string Month { get; set; } = string.Empty;
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
    }

    public class StatusDistributionData
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Amount { get; set; }
    }

    public class ClientRevenueData
    {
        public string ClientName { get; set; } = string.Empty;
        public decimal TotalRevenue { get; set; }
        public int InvoiceCount { get; set; }
        public decimal PaidAmount { get; set; }
        public int ProjectCount { get; set; }
    }
}
