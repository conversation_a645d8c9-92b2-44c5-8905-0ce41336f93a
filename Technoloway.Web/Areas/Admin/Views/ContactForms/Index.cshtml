@model IEnumerable<Technoloway.Core.Entities.ContactForm>

@{
    ViewData["Title"] = "Contact Forms";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid p-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Contact Forms Management</h1>
            <p class="text-muted mb-0">Manage customer inquiries and contact submissions</p>
        </div>
        <div class="d-flex gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterByStatus('all')">
                    <i class="fas fa-list me-1"></i>All
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterByStatus('new')">
                    <i class="fas fa-envelope me-1"></i>Unread
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="filterByStatus('read')">
                    <i class="fas fa-envelope-open me-1"></i>Read
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="filterByStatus('replied')">
                    <i class="fas fa-reply me-1"></i>Replied
                </button>
            </div>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-download"></i>
                Export
            </button>
        </div>
    </div>

    <!-- Contact Form Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Messages</p>
                        <h3 class="admin-stat-number" id="total-count">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Unread</p>
                        <h3 class="admin-stat-number" id="unread-count">@Model.Count(c => !c.IsRead)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Today</p>
                        <h3 class="admin-stat-number" id="today-count">@Model.Count(c => c.CreatedAt.Date == DateTime.Today)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">This Week</p>
                        <h3 class="admin-stat-number" id="week-count">@Model.Count(c => c.CreatedAt >= DateTime.Today.AddDays(-7))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Forms Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">Contact Form Submissions</h5>
                <p class="text-muted mb-0 small">@Model.Count() total contact submissions</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="contactFormsTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Contact</th>
                                <th class="border-0 fw-semibold">Subject</th>
                                <th class="border-0 fw-semibold">Message</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Date</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var contactForm in Model.OrderByDescending(c => c.CreatedAt))
                            {
                                <tr class="contact-form-row @(!contactForm.IsRead ? "table-warning" : "")"
                                    data-status="@contactForm.Status.ToLower()"
                                    data-read="@contactForm.IsRead.ToString().ToLower()">
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@contactForm.Name</div>
                                                <div class="text-muted small">
                                                    <a href="mailto:@contactForm.Email" class="text-decoration-none">
                                                        <i class="fas fa-envelope me-1"></i>@contactForm.Email
                                                    </a>
                                                </div>
                                                @if (!string.IsNullOrEmpty(contactForm.Phone))
                                                {
                                                    <div class="text-muted small">
                                                        <i class="fas fa-phone me-1"></i>@contactForm.Phone
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="fw-semibold text-gray-800">@contactForm.Subject</div>
                                        <div class="text-muted small">Subject Line</div>
                                    </td>
                                    <td class="border-0">
                                        <div style="max-width: 300px;" class="position-relative">
                                            @if (contactForm.Message.Length > 100)
                                            {
                                                <span class="message-content" data-full-content="@contactForm.Message">
                                                    @(contactForm.Message.Substring(0, 100) + "...")
                                                </span>
                                                <button class="btn btn-link btn-sm p-0 ms-1" onclick="toggleMessage(this)" title="Show full message">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <span>@contactForm.Message</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex flex-column gap-1">
                                            @if (!contactForm.IsRead)
                                            {
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-envelope me-1"></i>Unread
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-envelope-open me-1"></i>Read
                                                </span>
                                            }
                                            <span class="badge @(contactForm.Status switch {
                                                "New" => "bg-primary",
                                                "Read" => "bg-info",
                                                "Replied" => "bg-success",
                                                "Archived" => "bg-secondary",
                                                _ => "bg-primary"
                                            })">
                                                <i class="fas @(contactForm.Status switch {
                                                    "New" => "fa-star",
                                                    "Read" => "fa-eye",
                                                    "Replied" => "fa-reply",
                                                    "Archived" => "fa-archive",
                                                    _ => "fa-star"
                                                }) me-1"></i>@contactForm.Status
                                            </span>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar me-1"></i>
                                            @contactForm.CreatedAt.ToString("MMM dd, yyyy")
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            @contactForm.CreatedAt.ToString("h:mm tt")
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Details" asp-route-id="@contactForm.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-@(contactForm.IsRead ? "warning" : "success") btn-sm"
                                                    onclick="toggleRead(@contactForm.Id, this)"
                                                    title="@(contactForm.IsRead ? "Mark as Unread" : "Mark as Read")">
                                                <i class="fas @(contactForm.IsRead ? "fa-envelope" : "fa-envelope-open")"></i>
                                            </button>
                                            <a href="mailto:@contactForm.Email?subject=Re: @contactForm.Subject"
                                               class="btn btn-outline-primary btn-sm" title="Reply via Email">
                                                <i class="fas fa-reply"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@contactForm.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Message"
                                               onclick="return confirm('Are you sure you want to delete this contact form submission?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-inbox text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No contact form submissions yet</h5>
                    <p class="text-muted">Contact form submissions will appear here when visitors send messages through your website.</p>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            if ($('#contactFormsTable tbody tr').length > 0) {
                $('#contactFormsTable').DataTable({
                    "order": [[4, "desc"]], // Sort by date column descending
                    "pageLength": 25,
                    "responsive": true,
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 5] } // Disable sorting for Contact and Actions columns
                    ]
                });
            }
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Enhanced toggle read function
        function toggleRead(id, button) {
            $.post('@Url.Action("ToggleRead")', { id: id })
                .done(function(data) {
                    if (data.success) {
                        // Update the button and row styling without full reload
                        const row = $(button).closest('tr');
                        const isRead = data.isRead;

                        if (isRead) {
                            $(button).removeClass('btn-outline-success').addClass('btn-outline-warning');
                            $(button).find('i').removeClass('fa-envelope-open').addClass('fa-envelope');
                            $(button).attr('title', 'Mark as Unread');
                            row.removeClass('table-warning');

                            // Update status badges
                            const statusCell = row.find('td:nth-child(4)');
                            statusCell.find('.badge:first').removeClass('bg-warning').addClass('bg-success');
                            statusCell.find('.badge:first i').removeClass('fa-envelope').addClass('fa-envelope-open');
                            statusCell.find('.badge:first').html('<i class="fas fa-envelope-open me-1"></i>Read');
                        } else {
                            $(button).removeClass('btn-outline-warning').addClass('btn-outline-success');
                            $(button).find('i').removeClass('fa-envelope').addClass('fa-envelope-open');
                            $(button).attr('title', 'Mark as Read');
                            row.addClass('table-warning');

                            // Update status badges
                            const statusCell = row.find('td:nth-child(4)');
                            statusCell.find('.badge:first').removeClass('bg-success').addClass('bg-warning');
                            statusCell.find('.badge:first i').removeClass('fa-envelope-open').addClass('fa-envelope');
                            statusCell.find('.badge:first').html('<i class="fas fa-envelope me-1"></i>Unread');
                        }

                        // Update statistics
                        updateStatistics();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating read status');
                });
        }

        // Enhanced filter function
        function filterByStatus(status) {
            const rows = document.querySelectorAll('.contact-form-row');

            rows.forEach(row => {
                if (status === 'all') {
                    row.style.display = '';
                } else if (status === 'new') {
                    row.style.display = row.dataset.read === 'false' ? '' : 'none';
                } else if (status === 'read') {
                    row.style.display = row.dataset.read === 'true' ? '' : 'none';
                } else {
                    row.style.display = row.dataset.status === status ? '' : 'none';
                }
            });

            // Update button states
            document.querySelectorAll('.btn-group button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Function to toggle message display
        function toggleMessage(button) {
            const messageSpan = $(button).siblings('.message-content');
            const fullContent = messageSpan.data('full-content');
            const currentContent = messageSpan.text();

            if (currentContent.includes('...')) {
                messageSpan.text(fullContent);
                $(button).html('<i class="fas fa-compress-alt"></i>');
                $(button).attr('title', 'Show less');
            } else {
                messageSpan.text(fullContent.substring(0, 100) + '...');
                $(button).html('<i class="fas fa-expand-alt"></i>');
                $(button).attr('title', 'Show full message');
            }
        }

        // Update statistics function
        function updateStatistics() {
            const totalMessages = @Model.Count();
            const unreadMessages = $('.table-warning').length;
            const todayMessages = @Model.Count(c => c.CreatedAt.Date == DateTime.Today);
            const weekMessages = @Model.Count(c => c.CreatedAt >= DateTime.Today.AddDays(-7));

            $('#total-count').text(totalMessages);
            $('#unread-count').text(unreadMessages);
            $('#today-count').text(todayMessages);
            $('#week-count').text(weekMessages);
        }

        // Add contact form statistics calculations
        function updateContactFormStats() {
            const totalForms = @Model.Count();
            const unreadForms = @Model.Count(c => !c.IsRead);
            const todayForms = @Model.Count(c => c.CreatedAt.Date == DateTime.Today);
            const weekForms = @Model.Count(c => c.CreatedAt >= DateTime.Today.AddDays(-7));

            if (totalForms > 0) {
                const unreadPercentage = Math.round((unreadForms / totalForms) * 100);
                const todayPercentage = Math.round((todayForms / totalForms) * 100);
                console.log(`Unread messages: ${unreadPercentage}%`);
                console.log(`Today's messages: ${todayPercentage}%`);
                console.log(`This week's messages: ${weekForms}`);
            }
        }

        updateContactFormStats();

        // Add user icon hover effects
        $('.fa-user').parent().hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add email link hover effects
        $('a[href^="mailto:"]').hover(
            function() {
                $(this).css('transform', 'translateX(3px)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'translateX(0)');
            }
        );

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const contactName = $(this).closest('tr').find('.fw-semibold').first().text();
            if (!confirm(`Are you sure you want to delete the message from "${contactName}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add envelope icon hover effects in stat cards
        $('.fa-envelope, .fa-envelope-open').parent().hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );
    </script>
}
