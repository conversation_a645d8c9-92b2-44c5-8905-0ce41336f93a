@model IEnumerable<Technoloway.Core.Entities.Service>

@{
    ViewData["Title"] = "Services";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Services Management</h1>
            <p class="text-muted mb-0">Manage your service offerings and pricing</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-sort"></i>
                Sort Order
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Service
            </a>
        </div>
    </div>

    <!-- Services Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Services</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Services</p>
                        <h3 class="admin-stat-number">@Model.Count(s => s.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Inactive Services</p>
                        <h3 class="admin-stat-number">@Model.Count(s => !s.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Avg. Price</p>
                        <h3 class="admin-stat-number">@(Model.Any() ? Model.Average(s => s.Price).ToString("C0") : "$0")</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Services</h5>
                <p class="text-muted mb-0 small">@Model.Count() total services</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Service</th>
                                <th class="border-0 fw-semibold">Icon</th>
                                <th class="border-0 fw-semibold">Price</th>
                                <th class="border-0 fw-semibold">Order</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.OrderBy(s => s.DisplayOrder))
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                @if (!string.IsNullOrEmpty(item.IconClass))
                                                {
                                                    @if (item.IconClass.StartsWith("/images/"))
                                                    {
                                                        <img src="@item.IconClass" alt="Service Icon" style="width: 24px; height: 24px; object-fit: contain;" />
                                                    }
                                                    else
                                                    {
                                                        <i class="@item.IconClass text-white"></i>
                                                    }
                                                }
                                                else
                                                {
                                                    <i class="fas fa-cog text-white"></i>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Name</div>
                                                <div class="text-muted small">@item.Description?.Substring(0, Math.Min(item.Description?.Length ?? 0, 50))...</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.IconClass))
                                        {
                                            @if (item.IconClass.StartsWith("/images/"))
                                            {
                                                <span class="badge bg-light text-dark">
                                                    <i class="fas fa-image me-1"></i>Custom Image
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-light text-dark">
                                                    <i class="@item.IconClass me-1"></i>@item.IconClass.Split(' ').LastOrDefault()
                                                </span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">No icon</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <span class="fw-semibold text-success">@item.Price.ToString("C")</span>
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">#@item.DisplayOrder</span>
                                    </td>
                                    <td class="border-0">
                                        @if (item.IsActive)
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Active
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-pause me-1"></i>Inactive
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Service">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (item.IsActive)
                                            {
                                                <button class="btn btn-outline-warning btn-sm" title="Deactivate"
                                                        onclick="toggleServiceStatus(@item.Id, false)">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-outline-success btn-sm" title="Activate"
                                                        onclick="toggleServiceStatus(@item.Id, true)">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            }
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Service"
                                               onclick="return confirm('Are you sure you want to delete this service?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-server text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No services found</h5>
                    <p class="text-muted">Create your first service to get started.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Service
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "asc"]], // Sort by Display Order
                "columnDefs": [
                    { "orderable": false, "targets": [1, 5] } // Disable sorting for Icon and Actions columns
                ]
            });
        });

        // Function to toggle service status
        function toggleServiceStatus(serviceId, isActive) {
            if (confirm(`Are you sure you want to ${isActive ? 'activate' : 'deactivate'} this service?`)) {
                // Here you would make an AJAX call to update the service status
                // For now, we'll just reload the page
                // $.post('/Admin/Services/ToggleStatus', { id: serviceId, isActive: isActive })
                //     .done(function() {
                //         location.reload();
                //     });

                // Temporary: Just show an alert
                alert(`Service ${isActive ? 'activated' : 'deactivated'} successfully! (This would normally update via AJAX)`);
            }
        }

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );
    </script>
}
