@model Technoloway.Core.Entities.Service

@{
    ViewData["Title"] = "Service Details";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Service Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary shadow-sm me-2">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit Service
        </a>
        <a asp-action="Index" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Services
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (!string.IsNullOrEmpty(Model.IconClass))
                    {
                        @if (Model.IconClass.StartsWith("/images/"))
                        {
                            <img src="@Model.IconClass" alt="@Model.Name Icon" style="height: 24px; width: 24px; object-fit: contain;" class="me-2" />
                        }
                        else
                        {
                            <i class="@Model.IconClass me-2"></i>
                        }
                    }
                    else
                    {
                        <i class="fas fa-cog me-2"></i>
                    }
                    @Model.Name
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Service ID:</th>
                            <td>@Model.Id</td>
                        </tr>
                        <tr>
                            <th>Service Name:</th>
                            <td><strong>@Model.Name</strong></td>
                        </tr>
                        <tr>
                            <th>Description:</th>
                            <td>@Model.Description</td>
                        </tr>
                        <tr>
                            <th>Icon:</th>
                            <td>
                                @if (!string.IsNullOrEmpty(Model.IconClass))
                                {
                                    @if (Model.IconClass.StartsWith("/images/"))
                                    {
                                        <img src="@Model.IconClass" alt="Service Icon" style="height: 32px; width: 32px; object-fit: contain;" class="me-2" />
                                        <small class="text-muted">Custom Image: @Model.IconClass</small>
                                    }
                                    else
                                    {
                                        <i class="@Model.IconClass ms-2 text-primary"></i>
                                        <code class="ms-2">@Model.IconClass</code>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">No icon</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Price:</th>
                            <td>
                                <span class="h5 text-success">
                                    $@Model.Price.ToString("N2")
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-pause me-1"></i>Inactive
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Display Order:</th>
                            <td>
                                <span class="badge bg-info">@Model.DisplayOrder</span>
                            </td>
                        </tr>
                        <tr>
                            <th>Created Date:</th>
                            <td>@Model.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</td>
                        </tr>
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <tr>
                                <th>Last Updated:</th>
                                <td>@Model.UpdatedAt.Value.ToString("MMM dd, yyyy 'at' h:mm tt")</td>
                            </tr>
                        }
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Service
                    </a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Service
                    </a>
                    <a asp-controller="Services" asp-action="Details" asp-route-id="@Model.Id"
                       asp-area="" class="btn btn-info" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>View on Website
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-chart-bar me-2"></i>Service Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-primary">@Model.Projects.Count()</div>
                            <small class="text-muted">Projects</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">$@Model.Price.ToString("N0")</div>
                        <small class="text-muted">Base Price</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-info-circle me-2"></i>Service Information
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li><strong>Visibility:</strong> @(Model.IsActive ? "Visible on website" : "Hidden from website")</li>
                    <li><strong>Ordering:</strong> Displayed in position @Model.DisplayOrder</li>
                    <li><strong>Icon:</strong> Uses Font Awesome icon class</li>
                    <li><strong>Pricing:</strong> Base price for estimation purposes</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@if (Model.Projects.Any())
{
    <div class="card shadow mt-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-project-diagram me-2"></i>Related Projects (@Model.Projects.Count())
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead>
                        <tr>
                            <th>Project Name</th>
                            <th>Client</th>
                            <th>Completion Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var project in Model.Projects.Where(p => !p.IsDeleted).OrderByDescending(p => p.CompletionDate))
                        {
                            <tr>
                                <td>
                                    <strong>@project.Name</strong>
                                    @if (project.IsFeatured)
                                    {
                                        <span class="badge bg-warning text-dark ms-1">Featured</span>
                                    }
                                </td>
                                <td>@project.ClientName</td>
                                <td>@project.CompletionDate.ToString("MMM dd, yyyy")</td>
                                <td>
                                    @if (!project.IsDeleted)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Deleted</span>
                                    }
                                </td>
                                <td>
                                    <a asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id"
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
