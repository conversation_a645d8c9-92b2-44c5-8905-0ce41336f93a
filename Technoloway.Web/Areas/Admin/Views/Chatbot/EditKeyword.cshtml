@model Technoloway.Web.Areas.Admin.Models.ChatbotKeywordViewModel
@{
    ViewData["Title"] = "Edit Chatbot Keyword";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-key mr-2"></i>
                        Edit Chatbot Keyword
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Keywords", new { intentId = Model.ChatbotIntentId })" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Keywords
                        </a>
                    </div>
                </div>
                <form asp-action="EditKeyword" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Keyword" class="form-label"></label>
                                    <input asp-for="Keyword" class="form-control" placeholder="e.g., service, portfolio, contact" />
                                    <span asp-validation-for="Keyword" class="text-danger"></span>
                                    <small class="form-text text-muted">The main keyword that triggers this intent</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ChatbotIntentId" class="form-label"></label>
                                    <select asp-for="ChatbotIntentId" class="form-control">
                                        <option value="">Select an Intent</option>
                                        @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                                        {
                                            <option value="@intent.Id" selected="@(intent.Id == Model.ChatbotIntentId)">@intent.DisplayName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ChatbotIntentId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Synonyms" class="form-label"></label>
                            <input asp-for="Synonyms" class="form-control" placeholder="e.g., services, offerings, solutions" />
                            <span asp-validation-for="Synonyms" class="text-danger"></span>
                            <small class="form-text text-muted">Comma-separated list of synonyms (optional)</small>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Weight" class="form-label"></label>
                                    <input asp-for="Weight" class="form-control" type="number" min="1" max="10" />
                                    <span asp-validation-for="Weight" class="text-danger"></span>
                                    <small class="form-text text-muted">1-10 (higher = more important)</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="MatchType" class="form-label"></label>
                                    <select asp-for="MatchType" class="form-control">
                                        <option value="contains">Contains</option>
                                        <option value="exact">Exact Match</option>
                                        <option value="starts_with">Starts With</option>
                                        <option value="ends_with">Ends With</option>
                                    </select>
                                    <span asp-validation-for="MatchType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="custom-control custom-switch mt-4">
                                        <input asp-for="IsCaseSensitive" class="custom-control-input" type="checkbox" />
                                        <label asp-for="IsCaseSensitive" class="custom-control-label"></label>
                                    </div>
                                    <small class="form-text text-muted">Match case exactly</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="custom-control custom-switch mt-4">
                                        <input asp-for="IsActive" class="custom-control-input" type="checkbox" />
                                        <label asp-for="IsActive" class="custom-control-label"></label>
                                    </div>
                                    <small class="form-text text-muted">Only active keywords are used</small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-1"></i> Keyword Matching Tips</h6>
                            <ul class="mb-0">
                                <li><strong>Contains:</strong> Matches if the keyword appears anywhere in the message</li>
                                <li><strong>Exact:</strong> Matches only if the entire message equals the keyword</li>
                                <li><strong>Starts With:</strong> Matches if the message begins with the keyword</li>
                                <li><strong>Ends With:</strong> Matches if the message ends with the keyword</li>
                                <li><strong>Weight:</strong> Higher weights make this keyword more influential in intent detection</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i>
                            Update Keyword
                        </button>
                        <a href="@Url.Action("Keywords", new { intentId = Model.ChatbotIntentId })" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
