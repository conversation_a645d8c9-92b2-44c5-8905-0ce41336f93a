@model Technoloway.Core.Entities.Payment

@{
    ViewData["Title"] = "Edit Payment";
    Layout = "_AdminLayout";
    var invoice = ViewBag.Invoice as Technoloway.Core.Entities.Invoice;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Payment</h1>
    <div>
        <a asp-area="Admin" asp-controller="Invoices" asp-action="Payments" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Payments
        </a>
        <a asp-area="Admin" asp-controller="Invoices" asp-action="Details" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Invoice
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Details</h6>
            </div>
            <div class="card-body">
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger">
                        @TempData["ErrorMessage"]
                    </div>
                }
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success">
                        @TempData["SuccessMessage"]
                    </div>
                }

                <form asp-area="Admin" asp-controller="Invoices" asp-action="EditPayment" method="post">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="All" class="text-danger"></div>
                    <input type="hidden" asp-for="Id" />
                    <input type="hidden" asp-for="InvoiceId" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="TransactionId" class="control-label">Transaction ID</label>
                                <input asp-for="TransactionId" class="form-control" readonly />
                                <span asp-validation-for="TransactionId" class="text-danger"></span>
                                <small class="form-text text-muted">Transaction ID cannot be changed</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Amount" class="control-label">Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="Amount" type="number" step="0.01" min="0.01" class="form-control" required />
                                </div>
                                <span asp-validation-for="Amount" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="PaymentDate" class="control-label">Payment Date</label>
                                <input asp-for="PaymentDate" type="date" class="form-control" value="@Model.PaymentDate.ToString("yyyy-MM-dd")" required />
                                <span asp-validation-for="PaymentDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="PaymentMethod" class="control-label">Payment Method</label>
                                <select asp-for="PaymentMethod" class="form-control" required>
                                    <option value="">Select payment method...</option>
                                    <option value="Cash">Cash</option>
                                    <option value="Credit Card">Credit Card</option>
                                    <option value="Debit Card">Debit Card</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="PayPal">PayPal</option>
                                    <option value="Stripe">Stripe</option>
                                    <option value="Check">Check</option>
                                    <option value="Wire Transfer">Wire Transfer</option>
                                    <option value="Other">Other</option>
                                </select>
                                <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Status" class="control-label">Status</label>
                                <select asp-for="Status" class="form-control" required>
                                    <option value="">Select status...</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Failed">Failed</option>
                                    <option value="Refunded">Refunded</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Notes" class="control-label">Notes</label>
                        <textarea asp-for="Notes" class="form-control" rows="3" placeholder="Optional notes about this payment..."></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Payment
                        </button>
                        <a asp-area="Admin" asp-controller="Invoices" asp-action="Payments" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <h4>Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h4>
                    <p>
                        @switch (invoice?.Status)
                        {
                            case "Pending":
                                <span class="badge bg-warning">Pending</span>
                                break;
                            case "Paid":
                                <span class="badge bg-success">Paid</span>
                                break;
                            case "Partially Paid":
                                <span class="badge bg-info">Partially Paid</span>
                                break;
                            case "Overdue":
                                <span class="badge bg-danger">Overdue</span>
                                break;
                            case "Cancelled":
                                <span class="badge bg-secondary">Cancelled</span>
                                break;
                            default:
                                <span class="badge bg-info">@invoice?.Status</span>
                                break;
                        }
                    </p>
                </div>

                <dl class="row">
                    <dt class="col-sm-6">Client</dt>
                    <dd class="col-sm-6">@(invoice?.Client?.CompanyName ?? "N/A")</dd>

                    <dt class="col-sm-6">Issue Date</dt>
                    <dd class="col-sm-6">@(invoice?.IssueDate.ToString("yyyy-MM-dd") ?? "N/A")</dd>

                    <dt class="col-sm-6">Due Date</dt>
                    <dd class="col-sm-6">@(invoice?.DueDate.ToString("yyyy-MM-dd") ?? "N/A")</dd>

                    <dt class="col-sm-6">Total Amount</dt>
                    <dd class="col-sm-6"><strong>@(invoice?.TotalAmount.ToString("C") ?? "$0.00")</strong></dd>
                </dl>

                <div class="alert alert-info mt-3">
                    <p class="mb-0"><i class="fas fa-info-circle"></i> Editing this payment will recalculate the invoice balance and may update the invoice status.</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
