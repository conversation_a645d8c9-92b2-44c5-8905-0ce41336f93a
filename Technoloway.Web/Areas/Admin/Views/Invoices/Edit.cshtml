@model Technoloway.Core.Entities.Invoice

@{
    ViewData["Title"] = "Edit Invoice";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Invoice</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Invoice Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="InvoiceNumber" class="control-label">Invoice Number</label>
                        <input asp-for="InvoiceNumber" class="form-control" readonly />
                        <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="ClientId" class="control-label">Client</label>
                        <select asp-for="ClientId" asp-items="ViewBag.Clients" class="form-control" required>
                            <option value="">-- Select Client --</option>
                        </select>
                        <span asp-validation-for="ClientId" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="ProjectId" class="control-label">Project (Optional)</label>
                        <select asp-for="ProjectId" asp-items="ViewBag.Projects" class="form-control">
                            <option value="">-- Select Project --</option>
                        </select>
                        <span asp-validation-for="ProjectId" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="IssueDate" class="control-label">Issue Date</label>
                        <input asp-for="IssueDate" type="date" class="form-control" value="@Model.IssueDate.ToString("yyyy-MM-dd")" />
                        <span asp-validation-for="IssueDate" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="DueDate" class="control-label">Due Date</label>
                        <input asp-for="DueDate" type="date" class="form-control" value="@Model.DueDate.ToString("yyyy-MM-dd")" />
                        <span asp-validation-for="DueDate" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Status" class="control-label">Status</label>
                        <select asp-for="Status" class="form-control">
                            <option value="Pending">Pending</option>
                            <option value="Paid">Paid</option>
                            <option value="Overdue">Overdue</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                        <span asp-validation-for="Status" class="text-danger"></span>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="Amount" class="control-label">Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input asp-for="Amount" type="number" step="0.01" class="form-control" />
                        </div>
                        <span asp-validation-for="Amount" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="TaxRate" class="control-label">Tax Rate (%)</label>
                        <div class="input-group">
                            <input asp-for="TaxRate" type="number" step="0.01" class="form-control" />
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <span asp-validation-for="TaxRate" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="control-label">Total Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" class="form-control" id="totalAmount" readonly />
                        </div>
                        <small class="form-text text-muted">Calculated automatically based on amount and tax rate.</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="Notes" class="control-label">Notes</label>
                <textarea asp-for="Notes" class="form-control" rows="4"></textarea>
                <span asp-validation-for="Notes" class="text-danger"></span>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Save</button>
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Calculate total amount when amount or tax rate changes
            $('#Amount, #TaxRate').on('input', function() {
                calculateTotal();
            });
            
            function calculateTotal() {
                var amount = parseFloat($('#Amount').val()) || 0;
                var taxRate = parseFloat($('#TaxRate').val()) || 0;
                var taxAmount = amount * (taxRate / 100);
                var totalAmount = amount + taxAmount;
                
                $('#totalAmount').val(totalAmount.toFixed(2));
            }
            
            // Initial calculation
            calculateTotal();
        });
    </script>
}
