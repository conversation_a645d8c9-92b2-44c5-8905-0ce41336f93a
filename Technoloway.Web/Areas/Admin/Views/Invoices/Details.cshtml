@model Technoloway.Core.Entities.Invoice

@{
    ViewData["Title"] = "Invoice Details";
    Layout = "_AdminLayout";
    var client = ViewBag.Client as Technoloway.Core.Entities.Client;
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
    var items = ViewBag.Items as IEnumerable<Technoloway.Core.Entities.InvoiceItem>;
    var payments = ViewBag.Payments as IEnumerable<Technoloway.Core.Entities.Payment>;
    var totalPaid = ViewBag.TotalPaid as decimal? ?? 0;
    var balance = ViewBag.Balance as decimal? ?? 0;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Invoice #@Model.InvoiceNumber</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Items" asp-route-id="@Model.Id" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-list fa-sm text-white-50"></i> Items
        </a>
        <a asp-action="Payments" asp-route-id="@Model.Id" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-money-bill-wave fa-sm text-white-50"></i> Payments
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
                <div>
                    @switch (Model.Status)
                    {
                        case "Pending":
                            <span class="badge bg-warning">Pending</span>
                            break;
                        case "Paid":
                            <span class="badge bg-success">Paid</span>
                            break;
                        case "Overdue":
                            <span class="badge bg-danger">Overdue</span>
                            break;
                        case "Cancelled":
                            <span class="badge bg-secondary">Cancelled</span>
                            break;
                        default:
                            <span class="badge bg-info">@Model.Status</span>
                            break;
                    }
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Invoice Number</dt>
                            <dd class="col-sm-7">@Model.InvoiceNumber</dd>
                            
                            <dt class="col-sm-5">Issue Date</dt>
                            <dd class="col-sm-7">@Model.IssueDate.ToString("yyyy-MM-dd")</dd>
                            
                            <dt class="col-sm-5">Due Date</dt>
                            <dd class="col-sm-7">
                                @{
                                    var daysUntilDue = (Model.DueDate - DateTime.UtcNow).Days;
                                    var dueDateClass = "";
                                    
                                    if (Model.Status == "Paid")
                                    {
                                        dueDateClass = "text-success";
                                    }
                                    else if (Model.DueDate < DateTime.UtcNow)
                                    {
                                        dueDateClass = "text-danger";
                                    }
                                    else if (daysUntilDue <= 7)
                                    {
                                        dueDateClass = "text-warning";
                                    }
                                }
                                <span class="@dueDateClass">@Model.DueDate.ToString("yyyy-MM-dd")</span>
                                @if (Model.Status != "Paid" && Model.DueDate < DateTime.UtcNow)
                                {
                                    <span class="badge bg-danger ml-2">Overdue</span>
                                }
                                else if (Model.Status != "Paid" && daysUntilDue <= 7 && daysUntilDue >= 0)
                                {
                                    <span class="badge bg-warning ml-2">Due soon</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-5">Created</dt>
                            <dd class="col-sm-7">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>
                            
                            @if (Model.UpdatedAt.HasValue)
                            {
                                <dt class="col-sm-5">Last Updated</dt>
                                <dd class="col-sm-7">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")</dd>
                            }
                        </dl>
                    </div>
                    
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Amount</dt>
                            <dd class="col-sm-7">@Model.Amount.ToString("C")</dd>
                            
                            <dt class="col-sm-5">Tax Rate</dt>
                            <dd class="col-sm-7">@Model.TaxRate.ToString("F2")%</dd>
                            
                            <dt class="col-sm-5">Tax Amount</dt>
                            <dd class="col-sm-7">@Model.TaxAmount.ToString("C")</dd>
                            
                            <dt class="col-sm-5">Total Amount</dt>
                            <dd class="col-sm-7"><strong>@Model.TotalAmount.ToString("C")</strong></dd>
                            
                            <dt class="col-sm-5">Paid Amount</dt>
                            <dd class="col-sm-7"><span class="text-success">@totalPaid.ToString("C")</span></dd>
                            
                            <dt class="col-sm-5">Balance</dt>
                            <dd class="col-sm-7">
                                @if (balance <= 0)
                                {
                                    <span class="text-success">@balance.ToString("C")</span>
                                }
                                else
                                {
                                    <span class="text-danger">@balance.ToString("C")</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.Notes))
                {
                    <div class="mt-4">
                        <h6 class="font-weight-bold">Notes</h6>
                        <p>@Model.Notes</p>
                    </div>
                }
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Items</h6>
                <a asp-action="AddItem" asp-route-id="@Model.Id" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Add Item
                </a>
            </div>
            <div class="card-body">
                @if (items == null || !items.Any())
                {
                    <div class="alert alert-info">
                        <p class="mb-0">No items have been added to this invoice yet.</p>
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in items)
                                {
                                    <tr>
                                        <td>@item.Description</td>
                                        <td>@item.Quantity</td>
                                        <td>@item.UnitPrice.ToString("C")</td>
                                        <td>@item.TotalPrice.ToString("C")</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-right">Subtotal:</th>
                                    <th>@Model.Amount.ToString("C")</th>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-right">Tax (@Model.TaxRate%):</th>
                                    <th>@Model.TaxAmount.ToString("C")</th>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-right">Total:</th>
                                    <th>@Model.TotalAmount.ToString("C")</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
            </div>
            <div class="card-body">
                @if (client != null)
                {
                    <div class="text-center mb-3">
                        @if (!string.IsNullOrEmpty(client.LogoUrl))
                        {
                            <img src="@client.LogoUrl" alt="@client.CompanyName Logo" class="img-fluid rounded mb-3" style="max-height: 80px;" />
                        }
                        <h5>@client.CompanyName</h5>
                    </div>
                    
                    <dl class="row">
                        <dt class="col-sm-4">Contact</dt>
                        <dd class="col-sm-8">@client.ContactName</dd>
                        
                        <dt class="col-sm-4">Email</dt>
                        <dd class="col-sm-8">
                            <a href="mailto:@client.ContactEmail">@client.ContactEmail</a>
                        </dd>
                        
                        <dt class="col-sm-4">Phone</dt>
                        <dd class="col-sm-8">
                            <a href="tel:@client.ContactPhone">@client.ContactPhone</a>
                        </dd>
                    </dl>
                    
                    <div class="mt-3">
                        <a asp-controller="Clients" asp-action="Details" asp-route-id="@client.Id" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View Client
                        </a>
                    </div>
                }
                else
                {
                    <div class="alert alert-warning">
                        <p class="mb-0">Client information not available.</p>
                    </div>
                }
            </div>
        </div>
        
        @if (project != null)
        {
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Project Information</h6>
                </div>
                <div class="card-body">
                    <h5>@project.Name</h5>
                    <p class="text-muted">@(project.Description.Length > 100 ? project.Description.Substring(0, 100) + "..." : project.Description)</p>
                    
                    <div class="mt-3">
                        <a asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View Project
                        </a>
                    </div>
                </div>
            </div>
        }
        
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Payments</h6>
                <a asp-action="AddPayment" asp-route-id="@Model.Id" class="btn btn-sm btn-warning">
                    <i class="fas fa-plus"></i> Add Payment
                </a>
            </div>
            <div class="card-body">
                @if (payments == null || !payments.Any())
                {
                    <div class="alert alert-info">
                        <p class="mb-0">No payments have been recorded for this invoice yet.</p>
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var payment in payments)
                                {
                                    <tr>
                                        <td>@payment.PaymentDate.ToString("yyyy-MM-dd")</td>
                                        <td>@payment.Amount.ToString("C")</td>
                                        <td>@payment.PaymentMethod</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th class="text-right">Total Paid:</th>
                                    <th colspan="2">@totalPaid.ToString("C")</th>
                                </tr>
                                <tr>
                                    <th class="text-right">Balance:</th>
                                    <th colspan="2">
                                        @if (balance <= 0)
                                        {
                                            <span class="text-success">@balance.ToString("C")</span>
                                        }
                                        else
                                        {
                                            <span class="text-danger">@balance.ToString("C")</span>
                                        }
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
