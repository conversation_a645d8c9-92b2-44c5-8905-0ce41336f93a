@model Technoloway.Web.Areas.Admin.ViewModels.InvoiceStatsViewModel

@{
    ViewData["Title"] = "Invoices";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Invoice Management</h1>
            <p class="text-muted mb-0">Manage your billing and payment tracking</p>
        </div>
        <div class="d-flex gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterInvoices('all')">
                    <i class="fas fa-list me-1"></i>All
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="filterInvoices('paid')">
                    <i class="fas fa-check-circle me-1"></i>Paid
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterInvoices('pending')">
                    <i class="fas fa-clock me-1"></i>Pending
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="filterInvoices('partially-paid')">
                    <i class="fas fa-coins me-1"></i>Partial
                </button>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="filterInvoices('overdue')">
                    <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                </button>
            </div>
            <a asp-action="Analytics" class="btn-modern-admin secondary">
                <i class="fas fa-chart-pie"></i>
                Analytics
            </a>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Create Invoice
            </a>
        </div>
    </div>

    <!-- Invoice Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Invoices</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.Invoices.Count()</h3>
                            <span class="text-muted small">$@Model.Invoices.Sum(i => i.TotalAmount).ToString("N2")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Payments Received</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.InvoicesWithPayments</h3>
                            <span class="text-muted small">$@Model.TotalPaymentsReceived.ToString("N2")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">
                            Partially Paid
                            <i class="fas fa-info-circle text-muted ms-1"
                               title="Amount shown is the remaining balance still owed on partially paid invoices"
                               data-bs-toggle="tooltip"
                               data-bs-placement="top"
                               style="font-size: 0.75rem; cursor: help;"></i>
                        </p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.Invoices.Count(i => i.Status == "Partially Paid")</h3>
                            <span class="text-muted small">$@Model.PartiallyPaidBalance.ToString("N2") remaining</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Pending</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.Invoices.Count(i => i.Status == "Pending")</h3>
                            <span class="text-muted small">$@Model.Invoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount).ToString("N2")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card danger h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon danger me-3">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Overdue</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.Invoices.Count(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow))</h3>
                            <span class="text-muted small">$@Model.OverdueBalance.ToString("N2")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balance Card -->
        <div class="col-xl-2 col-lg-4 col-md-6">
            <div class="admin-stat-card secondary h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon secondary me-3">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Outstanding Balance</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.InvoicesWithBalance</h3>
                            <span class="text-muted small">$@Model.TotalOutstandingBalance.ToString("N2")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Invoices</h5>
                <p class="text-muted mb-0 small">@Model.Invoices.Count() total invoices</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Invoices.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Invoice</th>
                                <th class="border-0 fw-semibold">Client</th>
                                <th class="border-0 fw-semibold">Project</th>
                                <th class="border-0 fw-semibold">Dates</th>
                                <th class="border-0 fw-semibold">Amount</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Invoices)
                            {
                                var statusClass = item.Status.ToLower().Replace(" ", "-");
                                var isOverdue = item.Status != "Paid" && item.DueDate < DateTime.UtcNow;
                                <tr class="invoice-row" data-status="@statusClass" data-overdue="@isOverdue.ToString().ToLower()"
                                    data-client="@(item.Client?.CompanyName ?? "")" data-amount="@item.TotalAmount">
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                <i class="fas fa-file-invoice"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">
                                                    <a asp-action="Details" asp-route-id="@item.Id" class="text-decoration-none">
                                                        @item.InvoiceNumber
                                                    </a>
                                                </div>
                                                <div class="text-muted small">Invoice</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (item.Client != null)
                                        {
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-building me-2 text-muted"></i>
                                                <a asp-controller="Clients" asp-action="Details" asp-route-id="@item.ClientId"
                                                   class="text-decoration-none fw-semibold">
                                                    @item.Client.CompanyName
                                                </a>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-building me-2"></i>No client assigned
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (item.Project != null)
                                        {
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-project-diagram me-2 text-muted"></i>
                                                <a asp-controller="Projects" asp-action="Details" asp-route-id="@item.ProjectId"
                                                   class="text-decoration-none">
                                                    @item.Project.Name
                                                </a>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-project-diagram me-2"></i>No project
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex flex-column">
                                            <div class="small">
                                                <i class="fas fa-calendar me-1 text-muted"></i>
                                                <span class="text-muted">Issued:</span> @item.IssueDate.ToString("MMM dd, yyyy")
                                            </div>
                                            <div class="small">
                                                @{
                                                    var daysUntilDue = (item.DueDate - DateTime.UtcNow).Days;
                                                    var dueDateClass = "text-muted";
                                                    var dueDateIcon = "fa-calendar-check";

                                                    if (item.Status == "Paid")
                                                    {
                                                        dueDateClass = "text-success";
                                                        dueDateIcon = "fa-check-circle";
                                                    }
                                                    else if (item.DueDate < DateTime.UtcNow)
                                                    {
                                                        dueDateClass = "text-danger";
                                                        dueDateIcon = "fa-exclamation-triangle";
                                                    }
                                                    else if (daysUntilDue <= 7)
                                                    {
                                                        dueDateClass = "text-warning";
                                                        dueDateIcon = "fa-clock";
                                                    }
                                                }
                                                <i class="fas @dueDateIcon me-1 @dueDateClass"></i>
                                                <span class="@dueDateClass">Due: @item.DueDate.ToString("MMM dd, yyyy")</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="fw-semibold text-gray-800">@item.TotalAmount.ToString("C")</div>
                                        <div class="text-muted small">Total Amount</div>
                                    </td>
                                    <td class="border-0">
                                        @switch (item.Status)
                                        {
                                            case "Pending":
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>Pending
                                                </span>
                                                break;
                                            case "Paid":
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Paid
                                                </span>
                                                break;
                                            case "Overdue":
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                                </span>
                                                break;
                                            case "Cancelled":
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-times me-1"></i>Cancelled
                                                </span>
                                                break;
                                            default:
                                                <span class="badge bg-info">
                                                    <i class="fas fa-info me-1"></i>@item.Status
                                                </span>
                                                break;
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Invoice">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Items" asp-route-id="@item.Id"
                                               class="btn btn-outline-success btn-sm" title="Invoice Items">
                                                <i class="fas fa-list"></i>
                                            </a>
                                            <a asp-action="Payments" asp-route-id="@item.Id"
                                               class="btn btn-outline-warning btn-sm" title="Payments">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Invoice"
                                               onclick="return confirm('Are you sure you want to delete this invoice?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No invoices found</h5>
                    <p class="text-muted">Create your first invoice to start billing clients.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Invoice
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "desc"]], // Sort by Dates column in descending order
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6] } // Disable sorting for Invoice and Actions columns
                ]
            });

            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add invoice statistics calculations
        function updateInvoiceStats() {
            const totalInvoices = @Model.Invoices.Count();
            const invoicesWithPayments = @Model.InvoicesWithPayments;
            const partiallyPaidInvoices = @Model.Invoices.Count(i => i.Status == "Partially Paid");
            const pendingInvoices = @Model.Invoices.Count(i => i.Status == "Pending");
            const overdueInvoices = @Model.Invoices.Count(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow));

            const totalAmount = @Model.Invoices.Sum(i => i.TotalAmount);
            const pendingAmount = @Model.Invoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount);

            // Use calculated amounts from ViewModel
            const totalPaymentsReceived = @Model.TotalPaymentsReceived;
            const partiallyPaidBalance = @Model.PartiallyPaidBalance;
            const overdueBalance = @Model.OverdueBalance;
            const totalOutstandingBalance = @Model.TotalOutstandingBalance;
            const invoicesWithBalance = @Model.InvoicesWithBalance;

            if (totalInvoices > 0) {
                const paymentsReceivedPercentage = Math.round((invoicesWithPayments / totalInvoices) * 100);
                const partiallyPaidPercentage = Math.round((partiallyPaidInvoices / totalInvoices) * 100);
                const pendingPercentage = Math.round((pendingInvoices / totalInvoices) * 100);
                const overduePercentage = Math.round((overdueInvoices / totalInvoices) * 100);

                const paymentsAmountPercentage = Math.round((totalPaymentsReceived / totalAmount) * 100);
                const partiallyPaidBalancePercentage = Math.round((partiallyPaidBalance / totalAmount) * 100);
                const pendingAmountPercentage = Math.round((pendingAmount / totalAmount) * 100);
                const overdueBalancePercentage = Math.round((overdueBalance / totalAmount) * 100);
                const outstandingBalancePercentage = Math.round((totalOutstandingBalance / totalAmount) * 100);
                const balanceInvoicesPercentage = Math.round((invoicesWithBalance / totalInvoices) * 100);

                console.log(`Invoice Statistics:`);
                console.log(`Total: ${totalInvoices} invoices ($${totalAmount.toLocaleString()})`);
                console.log(`Payments Received: ${invoicesWithPayments} invoices (${paymentsReceivedPercentage}%) - Total: $${totalPaymentsReceived.toLocaleString()} (${paymentsAmountPercentage}%)`);
                console.log(`Partially Paid: ${partiallyPaidInvoices} invoices (${partiallyPaidPercentage}%) - Balance: $${partiallyPaidBalance.toLocaleString()} (${partiallyPaidBalancePercentage}%)`);
                console.log(`Pending: ${pendingInvoices} invoices (${pendingPercentage}%) - $${pendingAmount.toLocaleString()} (${pendingAmountPercentage}%)`);
                console.log(`Overdue: ${overdueInvoices} invoices (${overduePercentage}%) - Balance: $${overdueBalance.toLocaleString()} (${overdueBalancePercentage}%)`);
                console.log(`Outstanding Balance: ${invoicesWithBalance} invoices (${balanceInvoicesPercentage}%) - Total: $${totalOutstandingBalance.toLocaleString()} (${outstandingBalancePercentage}%)`);
            }
        }

        updateInvoiceStats();

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const invoiceNumber = $(this).closest('tr').find('.fw-semibold a').text();
            if (!confirm(`Are you sure you want to delete invoice "${invoiceNumber}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add status badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add invoice icon hover effects
        $('.fa-file-invoice').parent().hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );

        // Add amount hover effects
        $('.fw-semibold:contains("$")').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
                $(this).css('color', 'var(--admin-success)');
            },
            function() {
                $(this).css('transform', 'scale(1)');
                $(this).css('color', '');
            }
        );

        // Add date warning highlighting
        function highlightOverdueDates() {
            $('.text-danger').each(function() {
                if ($(this).text().includes('Due:')) {
                    $(this).addClass('fw-bold');
                    $(this).parent().addClass('bg-danger bg-opacity-10 rounded p-1');
                }
            });

            $('.text-warning').each(function() {
                if ($(this).text().includes('Due:')) {
                    $(this).addClass('fw-bold');
                    $(this).parent().addClass('bg-warning bg-opacity-10 rounded p-1');
                }
            });
        }

        highlightOverdueDates();

        // Add client/project link hover effects
        $('a[asp-controller="Clients"], a[asp-controller="Projects"]').hover(
            function() {
                $(this).css('transform', 'translateX(3px)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'translateX(0)');
            }
        );

        // Invoice filter functionality
        function filterInvoices(status) {
            const rows = document.querySelectorAll('.invoice-row');
            const buttons = document.querySelectorAll('.btn-group button');

            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Filter rows
            rows.forEach(row => {
                let show = false;

                switch(status) {
                    case 'all':
                        show = true;
                        break;
                    case 'paid':
                        show = row.dataset.status === 'paid';
                        break;
                    case 'pending':
                        show = row.dataset.status === 'pending';
                        break;
                    case 'partially-paid':
                        show = row.dataset.status === 'partially-paid';
                        break;
                    case 'overdue':
                        show = row.dataset.overdue === 'true' || row.dataset.status === 'overdue';
                        break;
                }

                row.style.display = show ? '' : 'none';
            });

            // Update stats display
            updateFilteredStats(status);
        }

        // Update statistics based on filtered results
        function updateFilteredStats(status) {
            const visibleRows = document.querySelectorAll('.invoice-row[style=""], .invoice-row:not([style])');
            const totalVisible = visibleRows.length;

            // Update the table info if needed
            console.log(`Showing ${totalVisible} invoices for filter: ${status}`);
        }

        // Make filterInvoices globally available
        window.filterInvoices = filterInvoices;
    </script>
}

@section Styles {
    <style>
        /* Enhanced stat card layout for amounts */
        .admin-stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #5a5c69;
            line-height: 1;
        }

        .stat-amount {
            font-size: 0.85rem;
            font-weight: 600;
            color: #858796;
            white-space: nowrap;
        }

        .d-flex.align-items-baseline.gap-2 {
            flex-wrap: wrap;
            gap: 0.5rem !important;
        }

        .text-muted.small {
            font-size: 0.85rem !important;
            font-weight: 600;
            color: #858796 !important;
            white-space: nowrap;
        }

        @@media (max-width: 576px) {
            .admin-stat-number {
                font-size: 1.5rem;
            }

            .text-muted.small {
                font-size: 0.75rem !important;
            }

            .d-flex.align-items-baseline.gap-2 {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 0.25rem !important;
            }
        }

        @@media (max-width: 768px) {
            .admin-stat-number {
                font-size: 1.75rem;
            }
        }

        /* Enhanced hover effects for stat cards with amounts */
        .admin-stat-card:hover .text-muted.small {
            color: #5a5c69 !important;
            transform: scale(1.05);
            transition: all 0.3s ease;
        }

        .admin-stat-card:hover .admin-stat-number {
            color: #4e73df;
            transition: all 0.3s ease;
        }

        /* Info card style for partially paid invoices */
        .admin-stat-icon.info {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
        }

        /* Custom column class for 6 cards in a row */
        .col-xl-2 {
            flex: 0 0 auto;
            width: 16.666667%; /* 100% / 6 = 16.666667% */
        }

        @@media (max-width: 1199.98px) {
            .col-xl-2 {
                width: 33.333333%; /* 3 cards per row on lg screens */
            }
        }

        @@media (max-width: 991.98px) {
            .col-xl-2 {
                width: 50%; /* 2 cards per row on md screens */
            }
        }

        @@media (max-width: 767.98px) {
            .col-xl-2 {
                width: 100%; /* 1 card per row on sm screens */
            }
        }
    </style>
}
