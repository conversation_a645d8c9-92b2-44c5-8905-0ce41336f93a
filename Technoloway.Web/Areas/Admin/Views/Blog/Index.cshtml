@model IEnumerable<Technoloway.Core.Entities.BlogPost>

@{
    ViewData["Title"] = "Blog Posts";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Blog Posts</h1>
            <p class="text-muted mb-0">Manage your blog content and publications</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Post
            </a>
        </div>
    </div>

    <!-- Blog Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-blog"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Posts</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Published</p>
                        <h3 class="admin-stat-number">@Model.Count(p => p.IsPublished)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Drafts</p>
                        <h3 class="admin-stat-number">@Model.Count(p => !p.IsPublished)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">This Month</p>
                        <h3 class="admin-stat-number">@Model.Count(p => p.CreatedAt.Month == DateTime.Now.Month && p.CreatedAt.Year == DateTime.Now.Year)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Posts Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Blog Posts</h5>
                <p class="text-muted mb-0 small">@Model.Count() total posts</p>
            </div>
            <div class="d-flex gap-2">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterBlogPosts('all')">
                        <i class="fas fa-list me-1"></i>All
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="filterBlogPosts('published')">
                        <i class="fas fa-check me-1"></i>Published
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="filterBlogPosts('draft')">
                        <i class="fas fa-edit me-1"></i>Drafts
                    </button>
                </div>
                <button class="btn btn-outline-secondary btn-sm" onclick="exportBlogData()">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Post</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Categories</th>
                                <th class="border-0 fw-semibold">Created</th>
                                <th class="border-0 fw-semibold">Published</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr class="blog-post-row" data-status="@(item.IsPublished ? "published" : "draft")"
                                    data-categories="@(item.Categories ?? "")" data-title="@item.Title">
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                <i class="fas fa-blog"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Title</div>
                                                <div class="text-muted small">@item.Excerpt?.Substring(0, Math.Min(item.Excerpt?.Length ?? 0, 50))...</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (item.IsPublished)
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Published
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-edit me-1"></i>Draft
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.Categories))
                                        {
                                            var categories = item.Categories.Split(',');
                                            foreach (var category in categories.Take(2))
                                            {
                                                <span class="badge bg-light text-dark me-1">@category.Trim()</span>
                                            }
                                            @if (categories.Length > 2)
                                            {
                                                <span class="text-muted small">+@(categories.Length - 2) more</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">No categories</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <span class="text-muted">@item.CreatedAt.ToString("MMM dd, yyyy")</span>
                                    </td>
                                    <td class="border-0">
                                        @if (item.PublishedAt.HasValue)
                                        {
                                            <span class="text-muted">@item.PublishedAt.Value.ToString("MMM dd, yyyy")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Not published</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (item.IsPublished)
                                            {
                                                <a href="/Blog/Details/@item.Slug" target="_blank"
                                                   class="btn btn-outline-success btn-sm" title="View Live">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            }
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete"
                                               onclick="return confirm('Are you sure you want to delete this post?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-blog text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No blog posts found</h5>
                    <p class="text-muted">Create your first blog post to get started.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Post
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "desc"]], // Sort by Created date in descending order
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5] } // Disable sorting for Post and Actions columns
                ]
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add blog statistics calculations
        function updateBlogStats() {
            const totalPosts = @Model.Count();
            const publishedPosts = @Model.Count(p => p.IsPublished);
            const draftPosts = @Model.Count(p => !p.IsPublished);

            if (totalPosts > 0) {
                const publishedPercentage = Math.round((publishedPosts / totalPosts) * 100);
                console.log(`Published posts: ${publishedPercentage}%`);
                console.log(`Draft posts: ${draftPosts}`);
            }
        }

        updateBlogStats();

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const postTitle = $(this).closest('tr').find('.fw-semibold').text();
            if (!confirm(`Are you sure you want to delete the blog post "${postTitle}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Blog post filter functionality
        function filterBlogPosts(status) {
            const rows = document.querySelectorAll('.blog-post-row');
            const buttons = document.querySelectorAll('.btn-group button');

            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Filter rows
            rows.forEach(row => {
                let show = false;

                switch(status) {
                    case 'all':
                        show = true;
                        break;
                    case 'published':
                        show = row.dataset.status === 'published';
                        break;
                    case 'draft':
                        show = row.dataset.status === 'draft';
                        break;
                }

                row.style.display = show ? '' : 'none';
            });

            // Update stats display
            updateBlogFilteredStats(status);
        }

        // Update statistics based on filtered results
        function updateBlogFilteredStats(status) {
            const visibleRows = document.querySelectorAll('.blog-post-row[style=""], .blog-post-row:not([style])');
            const totalVisible = visibleRows.length;

            console.log(`Showing ${totalVisible} blog posts for filter: ${status}`);
        }

        // Export blog data functionality
        function exportBlogData() {
            const posts = @Html.Raw(Json.Serialize(Model.Select(p => new {
                Title = p.Title,
                Status = p.IsPublished ? "Published" : "Draft",
                Categories = p.Categories,
                Created = p.CreatedAt.ToString("yyyy-MM-dd"),
                Published = p.PublishedAt?.ToString("yyyy-MM-dd") ?? "Not Published"
            })));

            const csvContent = [
                ['Title', 'Status', 'Categories', 'Created', 'Published'],
                ...posts.map(post => [
                    post.Title,
                    post.Status,
                    post.Categories || 'No categories',
                    post.Created,
                    post.Published
                ])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'blog-posts-' + new Date().toISOString().split('T')[0] + '.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Make functions globally available
        window.filterBlogPosts = filterBlogPosts;
        window.exportBlogData = exportBlogData;
    </script>
}
