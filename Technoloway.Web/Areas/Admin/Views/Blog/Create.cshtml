@model Technoloway.Web.Areas.Admin.Models.BlogPostViewModel

@{
    ViewData["Title"] = "Create Blog Post";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Create Blog Post</h1>
            <p class="text-muted mb-0">Create a new blog post for your website</p>
        </div>
        <div class="d-flex gap-2">
            <a asp-action="Index" class="btn-modern-admin secondary">
                <i class="fas fa-arrow-left"></i>
                Back to List
            </a>
        </div>
    </div>

    <!-- Create Form Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">Post Details</h5>
                <p class="text-muted mb-0 small">Fill in the information below to create your blog post</p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="previewPost()">
                    <i class="fas fa-eye me-1"></i>
                    Preview
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="saveDraft()">
                    <i class="fas fa-save me-1"></i>
                    Save Draft
                </button>
            </div>
        </div>
        <div class="card-body p-4">
        <form asp-action="Create" method="post" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content Section -->
                    <div class="admin-card mb-4">
                        <div class="card-header bg-white border-bottom p-3">
                            <h6 class="mb-0 fw-bold text-gray-800">
                                <i class="fas fa-edit me-2 text-primary"></i>Content Details
                            </h6>
                        </div>
                        <div class="card-body p-4">
                            <div class="form-group mb-4">
                                <label asp-for="Title" class="form-label fw-semibold">
                                    <i class="fas fa-heading me-1"></i>Post Title
                                </label>
                                <input asp-for="Title" class="form-control form-control-lg"
                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                       placeholder="Enter an engaging title for your blog post" />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>

                            <div class="form-group mb-4">
                                <label asp-for="Slug" class="form-label fw-semibold">
                                    <i class="fas fa-link me-1"></i>URL Slug
                                </label>
                                <input asp-for="Slug" class="form-control"
                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                       placeholder="Leave empty to generate automatically" />
                                <span asp-validation-for="Slug" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>URL-friendly version of the title (e.g., "my-blog-post")
                                </small>
                            </div>

                            <div class="form-group mb-4">
                                <label asp-for="Excerpt" class="form-label fw-semibold">
                                    <i class="fas fa-quote-left me-1"></i>Excerpt
                                </label>
                                <textarea asp-for="Excerpt" class="form-control" rows="3"
                                          style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                          placeholder="Write a compelling summary of your post..."></textarea>
                                <span asp-validation-for="Excerpt" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>A short summary displayed in post listings
                                </small>
                            </div>

                            <div class="form-group mb-4">
                                <label asp-for="Categories" class="form-label fw-semibold">
                                    <i class="fas fa-tags me-1"></i>Categories
                                </label>
                                <input asp-for="Categories" class="form-control"
                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                       placeholder="Technology, Web Development, Design" />
                                <span asp-validation-for="Categories" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>Comma-separated list of categories
                                </small>
                            </div>

                            <div class="form-group mb-4">
                                <label asp-for="FeaturedImageFile" class="form-label fw-semibold">
                                    <i class="fas fa-image me-1"></i>Featured Image
                                </label>
                                <div class="upload-area p-4 text-center border rounded"
                                     style="border: 2px dashed var(--admin-border); background: var(--admin-bg);">
                                    <input asp-for="FeaturedImageFile" type="file" class="form-control"
                                           accept="image/*" id="featuredImageInput" style="display: none;" />
                                    <div id="uploadPrompt">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2">Click to upload or drag and drop</p>
                                        <small class="text-muted">JPG, PNG, GIF, WebP up to 5MB</small>
                                        <br>
                                        <button type="button" class="btn-modern-admin secondary mt-3" onclick="document.getElementById('featuredImageInput').click()">
                                            <i class="fas fa-upload me-1"></i>Choose File
                                        </button>
                                    </div>
                                </div>
                                <span asp-validation-for="FeaturedImageFile" class="text-danger"></span>

                                <!-- Image Preview -->
                                <div id="imagePreview" class="mt-3" style="display: none;">
                                    <div class="position-relative d-inline-block">
                                        <img id="previewImg" src="" alt="Preview" class="img-thumbnail"
                                             style="max-width: 300px; max-height: 200px; border-radius: var(--admin-radius);" />
                                        <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-2"
                                                onclick="clearImagePreview()" style="border-radius: 50%;">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Publishing Section -->
                    <div class="admin-card mb-4">
                        <div class="card-header bg-white border-bottom p-3">
                            <h6 class="mb-0 fw-bold text-gray-800">
                                <i class="fas fa-rocket me-2 text-success"></i>Publishing
                            </h6>
                        </div>
                        <div class="card-body p-4">
                            <div class="form-check mb-4">
                                <input asp-for="IsPublished" class="form-check-input" style="transform: scale(1.2);" />
                                <label asp-for="IsPublished" class="form-check-label fw-semibold">
                                    <i class="fas fa-globe me-1"></i>Publish immediately
                                </label>
                            </div>
                            <button type="submit" class="btn-modern-admin primary w-100">
                                <i class="fas fa-save me-2"></i>Save Post
                            </button>
                        </div>
                    </div>

                    <!-- SEO Section -->
                    <div class="admin-card mb-4">
                        <div class="card-header bg-white border-bottom p-3">
                            <h6 class="mb-0 fw-bold text-gray-800">
                                <i class="fas fa-search me-2 text-info"></i>SEO Settings
                            </h6>
                        </div>
                        <div class="card-body p-4">
                            <div class="form-group mb-3">
                                <label asp-for="MetaTitle" class="form-label fw-semibold">Meta Title</label>
                                <input asp-for="MetaTitle" class="form-control"
                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);" />
                                <span asp-validation-for="MetaTitle" class="text-danger"></span>
                            </div>
                            <div class="form-group mb-3">
                                <label asp-for="MetaDescription" class="form-label fw-semibold">Meta Description</label>
                                <textarea asp-for="MetaDescription" class="form-control" rows="3"
                                          style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"></textarea>
                                <span asp-validation-for="MetaDescription" class="text-danger"></span>
                            </div>
                            <div class="form-group mb-3">
                                <label asp-for="MetaKeywords" class="form-label fw-semibold">Meta Keywords</label>
                                <input asp-for="MetaKeywords" class="form-control"
                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);" />
                                <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Editor Section -->
            <div class="admin-card mb-4">
                <div class="card-header bg-white border-bottom p-3">
                    <h6 class="mb-0 fw-bold text-gray-800">
                        <i class="fas fa-file-alt me-2 text-warning"></i>Post Content
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="form-group mb-4">
                        <label asp-for="Content" class="form-label fw-semibold">
                            <i class="fas fa-pen me-1"></i>Content
                        </label>
                        <textarea asp-for="Content" id="editor" class="form-control" rows="15"
                                  style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"></textarea>
                        <span asp-validation-for="Content" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a asp-action="Index" class="btn-modern-admin secondary">
                        <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                        <i class="fas fa-save me-2"></i>Save as Draft
                    </button>
                    <button type="submit" class="btn-modern-admin primary">
                        <i class="fas fa-rocket me-2"></i>Publish Post
                    </button>
                </div>
            </div>
        </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="https://cdn.ckeditor.com/ckeditor5/38.0.1/classic/ckeditor.js"></script>
    <script>
        let editor;

        ClassicEditor
            .create(document.querySelector('#editor'), {
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'link', '|',
                    'bulletedList', 'numberedList', '|',
                    'outdent', 'indent', '|',
                    'imageUpload', 'blockQuote', 'insertTable', '|',
                    'undo', 'redo'
                ]
            })
            .then(newEditor => {
                editor = newEditor;
            })
            .catch(error => {
                console.error(error);
            });

        // Enhanced image preview functionality
        document.getElementById('featuredImageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    this.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    document.getElementById('uploadPrompt').style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });

        function clearImagePreview() {
            document.getElementById('featuredImageInput').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('uploadPrompt').style.display = 'block';
            document.getElementById('previewImg').src = '';
        }

        // Auto-generate slug from title
        document.querySelector('input[name="Title"]').addEventListener('input', function(e) {
            const slugInput = document.querySelector('input[name="Slug"]');
            if (!slugInput.value) {
                const slug = e.target.value
                    .toLowerCase()
                    .replace(/[^a-z0-9 -]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                slugInput.value = slug;
            }
        });

        // Preview functionality
        function previewPost() {
            const title = document.querySelector('input[name="Title"]').value;
            const content = editor.getData();

            if (!title || !content) {
                alert('Please fill in the title and content to preview');
                return;
            }

            // Open preview in new window
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <html>
                    <head>
                        <title>Preview: ${title}</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    </head>
                    <body>
                        <div class="container mt-4">
                            <h1>${title}</h1>
                            <div class="content">${content}</div>
                        </div>
                    </body>
                </html>
            `);
        }

        // Save draft functionality
        function saveDraft() {
            const form = document.querySelector('form');
            const publishCheckbox = document.querySelector('input[name="IsPublished"]');

            // Temporarily uncheck publish
            const wasChecked = publishCheckbox.checked;
            publishCheckbox.checked = false;

            // Submit form
            form.submit();
        }

        // Drag and drop functionality for image upload
        const uploadArea = document.querySelector('.upload-area');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.backgroundColor = 'var(--admin-primary)';
            this.style.opacity = '0.1';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.backgroundColor = 'var(--admin-bg)';
            this.style.opacity = '1';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.backgroundColor = 'var(--admin-bg)';
            this.style.opacity = '1';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('featuredImageInput').files = files;
                document.getElementById('featuredImageInput').dispatchEvent(new Event('change'));
            }
        });
    </script>
}
