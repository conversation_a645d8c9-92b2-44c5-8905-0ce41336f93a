@model Technoloway.Web.Areas.Admin.Models.BlogPostViewModel

@{
    ViewData["Title"] = "Edit Blog Post";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Blog Post</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Post Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" method="post" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />

            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label asp-for="Title" class="control-label"></label>
                        <input asp-for="Title" class="form-control" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Slug" class="control-label"></label>
                        <input asp-for="Slug" class="form-control" placeholder="Leave empty to generate automatically" />
                        <span asp-validation-for="Slug" class="text-danger"></span>
                        <small class="form-text text-muted">URL-friendly version of the title (e.g., "my-blog-post")</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Excerpt" class="control-label"></label>
                        <textarea asp-for="Excerpt" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Excerpt" class="text-danger"></span>
                        <small class="form-text text-muted">A short summary of the post (displayed in listings)</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Categories" class="control-label"></label>
                        <input asp-for="Categories" class="form-control" placeholder="Technology, Web Development, Design" />
                        <span asp-validation-for="Categories" class="text-danger"></span>
                        <small class="form-text text-muted">Comma-separated list of categories</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="FeaturedImageFile" class="control-label">Featured Image</label>
                        <input asp-for="FeaturedImageFile" type="file" class="form-control" accept="image/*" id="featuredImageInput" />
                        <span asp-validation-for="FeaturedImageFile" class="text-danger"></span>
                        <small class="form-text text-muted">Upload an image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.</small>

                        <!-- Current Image Preview -->
                        @if (!string.IsNullOrEmpty(Model.FeaturedImageUrl))
                        {
                            <div id="currentImage" class="mt-3">
                                <label class="form-label">Current Featured Image:</label>
                                <div>
                                    <img src="@Model.FeaturedImageUrl" alt="Current featured image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;" />
                                </div>
                            </div>
                        }

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <label class="form-label">New Featured Image Preview:</label>
                            <div>
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 300px; max-height: 200px;" />
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="clearImagePreview()">Remove New Image</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Publishing</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input asp-for="IsPublished" class="form-check-input" />
                                <label asp-for="IsPublished" class="form-check-label">Published</label>
                            </div>

                            @if (Model.PublishedAt.HasValue)
                            {
                                <div class="mb-3">
                                    <small class="text-muted">Published on: @Model.PublishedAt.Value.ToString("yyyy-MM-dd HH:mm")</small>
                                </div>
                            }

                            <button type="submit" class="btn btn-primary btn-block">Update Post</button>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">SEO</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="MetaTitle" class="control-label"></label>
                                <input asp-for="MetaTitle" class="form-control" />
                                <span asp-validation-for="MetaTitle" class="text-danger"></span>
                            </div>
                            <div class="form-group mb-3">
                                <label asp-for="MetaDescription" class="control-label"></label>
                                <textarea asp-for="MetaDescription" class="form-control" rows="3"></textarea>
                                <span asp-validation-for="MetaDescription" class="text-danger"></span>
                            </div>
                            <div class="form-group mb-3">
                                <label asp-for="MetaKeywords" class="control-label"></label>
                                <input asp-for="MetaKeywords" class="form-control" />
                                <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Content" class="control-label"></label>
                <textarea asp-for="Content" id="editor" class="form-control" rows="15"></textarea>
                <span asp-validation-for="Content" class="text-danger"></span>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Save</button>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="https://cdn.ckeditor.com/ckeditor5/38.0.1/classic/ckeditor.js"></script>
    <script>
        ClassicEditor
            .create(document.querySelector('#editor'))
            .catch(error => {
                console.error(error);
            });

        // Image preview functionality
        document.getElementById('featuredImageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        function clearImagePreview() {
            document.getElementById('featuredImageInput').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('previewImg').src = '';
        }
    </script>
}
