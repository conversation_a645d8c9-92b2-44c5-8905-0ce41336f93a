@model Technoloway.Core.Entities.BlogPost

@{
    ViewData["Title"] = "Blog Post Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Blog Post Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Post Content</h6>
            </div>
            <div class="card-body">
                <h2 class="mb-3">@Model.Title</h2>

                @if (!string.IsNullOrEmpty(Model.FeaturedImageUrl))
                {
                    <div class="mb-4">
                        <img src="@Model.FeaturedImageUrl" class="img-fluid rounded" alt="@Model.Title" />
                    </div>
                }

                <div class="mb-4">
                    <div class="content">
                        @Html.Raw(Model.Content)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Post Information</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Status</dt>
                    <dd class="col-sm-8">
                        @if (Model.IsPublished)
                        {
                            <span class="badge bg-success">Published</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Draft</span>
                        }
                    </dd>

                    <dt class="col-sm-4">Created</dt>
                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm")</dd>

                    @if (Model.PublishedAt.HasValue)
                    {
                        <dt class="col-sm-4">Published</dt>
                        <dd class="col-sm-8">@Model.PublishedAt.Value.ToString("yyyy-MM-dd HH:mm")</dd>
                    }

                    <dt class="col-sm-4">Updated</dt>
                    <dd class="col-sm-8">@Model.UpdatedAt</dd>

                    <dt class="col-sm-4">Slug</dt>
                    <dd class="col-sm-8">@Model.Slug</dd>

                    @if (!string.IsNullOrEmpty(Model.Categories))
                    {
                        <dt class="col-sm-4">Categories</dt>
                        <dd class="col-sm-8">
                            @foreach (var category in Model.Categories.Split(','))
                            {
                                <span class="badge bg-primary me-1">@category.Trim()</span>
                            }
                        </dd>
                    }
                </dl>

                @if (Model.IsPublished)
                {
                    <div class="mt-3">
                        <a href="/Blog/Details/@Model.Slug" target="_blank" class="btn btn-success btn-sm">
                            <i class="fas fa-external-link-alt"></i> View on Site
                        </a>
                    </div>
                }
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Meta Title</dt>
                    <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.MetaTitle) ? "-" : Model.MetaTitle)</dd>

                    <dt class="col-sm-4">Meta Description</dt>
                    <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.MetaDescription) ? "-" : Model.MetaDescription)</dd>

                    <dt class="col-sm-4">Meta Keywords</dt>
                    <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.MetaKeywords) ? "-" : Model.MetaKeywords)</dd>
                </dl>
            </div>
        </div>
    </div>
</div>
