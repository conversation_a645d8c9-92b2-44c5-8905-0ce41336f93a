@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Edit Hero Section";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-edit me-2"></i>
                Edit Hero Section
            </h1>
            <p class="admin-page-subtitle">Update hero section content and slideshow settings</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to List
            </a>
        </div>
    </div>
</div>

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<form asp-action="Edit" method="post" class="needs-validation" novalidate>
    <input type="hidden" asp-for="Id" />

    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label asp-for="Title" class="form-label required"></label>
                            <input asp-for="Title" class="form-control" />
                            <span asp-validation-for="Title" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="PageName" class="form-label required"></label>
                            <select asp-for="PageName" class="form-select">
                                <option value="Home">Home</option>
                                <option value="About">About Us</option>
                                <option value="Services">Services</option>
                                <option value="Projects">Projects/Portfolio</option>
                                <option value="Technologies">Technologies</option>
                                <option value="Blog">Blog</option>
                                <option value="Careers">Careers</option>
                                <option value="Contact">Contact Us</option>
                            </select>
                            <span asp-validation-for="PageName" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="MetaDescription" class="form-label"></label>
                            <input asp-for="MetaDescription" class="form-control" />
                            <span asp-validation-for="MetaDescription" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="MetaKeywords" class="form-label"></label>
                            <input asp-for="MetaKeywords" class="form-control" />
                            <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                            <div class="form-text">Comma-separated keywords for SEO</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Content -->
            <div class="admin-card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-edit me-2"></i>Hero Content
                    </h5>
                    <div class="mb-3">
                        <label asp-for="MainTitle" class="form-label required"></label>
                        <textarea asp-for="MainTitle" class="form-control ckeditor" rows="2"></textarea>
                        <span asp-validation-for="MainTitle" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="MainSubtitle" class="form-label required"></label>
                        <textarea asp-for="MainSubtitle" class="form-control ckeditor" rows="2"></textarea>
                        <span asp-validation-for="MainSubtitle" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="MainDescription" class="form-label"></label>
                        <textarea asp-for="MainDescription" class="form-control ckeditor" rows="3"></textarea>
                        <span asp-validation-for="MainDescription" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="admin-card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-mouse-pointer me-2"></i>Call-to-Action Buttons
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label asp-for="PrimaryButtonText" class="form-label"></label>
                            <input asp-for="PrimaryButtonText" class="form-control" />
                            <span asp-validation-for="PrimaryButtonText" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="PrimaryButtonUrl" class="form-label"></label>
                            <input asp-for="PrimaryButtonUrl" class="form-control" />
                            <span asp-validation-for="PrimaryButtonUrl" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="SecondaryButtonText" class="form-label"></label>
                            <input asp-for="SecondaryButtonText" class="form-control" />
                            <span asp-validation-for="SecondaryButtonText" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="SecondaryButtonUrl" class="form-label"></label>
                            <input asp-for="SecondaryButtonUrl" class="form-control" />
                            <span asp-validation-for="SecondaryButtonUrl" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slides Section -->
            <div class="admin-card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-images me-2"></i>Hero Slides
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addSlide()">
                            <i class="fas fa-plus me-1"></i>Add Slide
                        </button>
                    </div>
                    <div id="slides-container">
                        @for (int i = 0; i < Model.Slides.Count; i++)
                        {
                            <div class="slide-item border rounded p-3 mb-3" data-slide-index="@i">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Slide @(i + 1)</h6>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSlide(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <input type="hidden" asp-for="Slides[i].Id" />

                                <div class="mb-3">
                                    <label asp-for="Slides[i].Content" class="form-label required">Content</label>
                                    <textarea asp-for="Slides[i].Content" class="form-control ckeditor-full" rows="4"></textarea>
                                    <span asp-validation-for="Slides[i].Content" class="text-danger"></span>
                                </div>

                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <label asp-for="Slides[i].MediaType" class="form-label required">Media Type</label>
                                        <select asp-for="Slides[i].MediaType" class="form-select media-type-select" data-slide-index="@i">
                                            <option value="image">Image</option>
                                            <option value="video">Video</option>
                                        </select>
                                        <span asp-validation-for="Slides[i].MediaType" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- Image Fields -->
                                        <div class="media-fields image-fields" data-media-type="image" data-slide-index="@i" style="@(Model.Slides[i].MediaType == "video" ? "display: none;" : "")">
                                            <label asp-for="Slides[i].ImageUrl" class="form-label">Image URL</label>
                                            <div class="input-group">
                                                <input asp-for="Slides[i].ImageUrl" class="form-control" />
                                                <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[@i].ImageUrl">
                                                    <i class="fas fa-upload"></i>
                                                </button>
                                            </div>
                                            <span asp-validation-for="Slides[i].ImageUrl" class="text-danger"></span>
                                        </div>
                                        <!-- Video Fields -->
                                        <div class="media-fields video-fields" data-media-type="video" data-slide-index="@i" style="@(Model.Slides[i].MediaType == "image" ? "display: none;" : "")">
                                            <label asp-for="Slides[i].VideoUrl" class="form-label">Video URL</label>
                                            <div class="input-group">
                                                <input asp-for="Slides[i].VideoUrl" class="form-control" />
                                                <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[@i].VideoUrl">
                                                    <i class="fas fa-upload"></i>
                                                </button>
                                            </div>
                                            <span asp-validation-for="Slides[i].VideoUrl" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label asp-for="Slides[i].MediaAlt" class="form-label">Alt Text</label>
                                        <input asp-for="Slides[i].MediaAlt" class="form-control" />
                                        <span asp-validation-for="Slides[i].MediaAlt" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- Video Settings (only show for video) -->
                                <div class="video-settings" data-slide-index="@i" style="@(Model.Slides[i].MediaType == "image" ? "display: none;" : "")">
                                    <div class="row g-2 mt-2">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input asp-for="Slides[i].VideoAutoPlay" class="form-check-input" />
                                                <label asp-for="Slides[i].VideoAutoPlay" class="form-check-label">Auto Play</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input asp-for="Slides[i].VideoMuted" class="form-check-input" />
                                                <label asp-for="Slides[i].VideoMuted" class="form-check-label">Muted</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input asp-for="Slides[i].VideoLoop" class="form-check-input" />
                                                <label asp-for="Slides[i].VideoLoop" class="form-check-label">Loop</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input asp-for="Slides[i].VideoControls" class="form-check-input" />
                                                <label asp-for="Slides[i].VideoControls" class="form-check-label">Controls</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-2 mt-2">
                                    <div class="col-md-3">
                                        <label asp-for="Slides[i].ButtonText" class="form-label">Button Text</label>
                                        <input asp-for="Slides[i].ButtonText" class="form-control" />
                                        <span asp-validation-for="Slides[i].ButtonText" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <label asp-for="Slides[i].ButtonUrl" class="form-label">Button URL</label>
                                        <input asp-for="Slides[i].ButtonUrl" class="form-control" />
                                        <span asp-validation-for="Slides[i].ButtonUrl" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-2">
                                        <label asp-for="Slides[i].DisplayOrder" class="form-label">Order</label>
                                        <input asp-for="Slides[i].DisplayOrder" class="form-control" type="number" min="1" />
                                        <span asp-validation-for="Slides[i].DisplayOrder" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-2">
                                        <label asp-for="Slides[i].AnimationType" class="form-label">Animation</label>
                                        <select asp-for="Slides[i].AnimationType" class="form-select">
                                            <option value="fade">Fade</option>
                                            <option value="slide">Slide</option>
                                            <option value="zoom">Zoom</option>
                                        </select>
                                        <span asp-validation-for="Slides[i].AnimationType" class="text-danger"></span>
                                    </div>
                                    <div class="col-md-2">
                                        <label asp-for="Slides[i].Duration" class="form-label">Duration (ms)</label>
                                        <input asp-for="Slides[i].Duration" class="form-control" type="number" min="1000" max="30000" step="1000" />
                                        <span asp-validation-for="Slides[i].Duration" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="mt-2">
                                    <div class="form-check">
                                        <input asp-for="Slides[i].IsActive" class="form-check-input" />
                                        <label asp-for="Slides[i].IsActive" class="form-check-label">Active</label>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Settings -->
            <div class="admin-card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-cogs me-2"></i>Settings
                    </h5>

                    <div class="mb-3">
                        <label asp-for="SlideshowSpeed" class="form-label">Slideshow Speed (ms)</label>
                        <input asp-for="SlideshowSpeed" class="form-control" type="number" min="1000" max="30000" step="1000" />
                        <span asp-validation-for="SlideshowSpeed" class="text-danger"></span>
                    </div>

                    <div class="row g-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="EnableSlideshow" class="form-check-input" />
                                <label asp-for="EnableSlideshow" class="form-check-label">Enable Slideshow</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="AutoPlay" class="form-check-input" />
                                <label asp-for="AutoPlay" class="form-check-label">Auto Play</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="ShowDots" class="form-check-input" />
                                <label asp-for="ShowDots" class="form-check-label">Show Dots</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="ShowArrows" class="form-check-input" />
                                <label asp-for="ShowArrows" class="form-check-label">Show Arrows</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="EnableFloatingElements" class="form-check-input" />
                                <label asp-for="EnableFloatingElements" class="form-check-label">Floating Elements</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">Active</label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <label asp-for="FloatingElementsConfig" class="form-label">Floating Elements Config</label>
                        <textarea asp-for="FloatingElementsConfig" class="form-control" rows="4"></textarea>
                        <span asp-validation-for="FloatingElementsConfig" class="text-danger"></span>
                        <div class="form-text">JSON configuration</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="admin-card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Save Changes
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                            <i class="fas fa-eye me-1"></i>Preview
                        </a>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        let slideIndex = @Model.Slides.Count;

        function addSlide() {
            const container = document.getElementById('slides-container');
            const slideHtml = `
                <div class="slide-item border rounded p-3 mb-3" data-slide-index="${slideIndex}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Slide ${slideIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSlide(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <input type="hidden" name="Slides[${slideIndex}].Id" value="0" />

                    <div class="mb-3">
                        <label class="form-label required">Content</label>
                        <textarea name="Slides[${slideIndex}].Content" class="form-control ckeditor-full" rows="4" required></textarea>
                    </div>

                    <div class="row g-2">
                        <div class="col-md-3">
                            <label class="form-label required">Media Type</label>
                            <select name="Slides[${slideIndex}].MediaType" class="form-select media-type-select" data-slide-index="${slideIndex}">
                                <option value="image">Image</option>
                                <option value="video">Video</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <!-- Image Fields -->
                            <div class="media-fields image-fields" data-media-type="image" data-slide-index="${slideIndex}">
                                <label class="form-label">Image URL</label>
                                <div class="input-group">
                                    <input name="Slides[${slideIndex}].ImageUrl" class="form-control" />
                                    <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[${slideIndex}].ImageUrl">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- Video Fields -->
                            <div class="media-fields video-fields" data-media-type="video" data-slide-index="${slideIndex}" style="display: none;">
                                <label class="form-label">Video URL</label>
                                <div class="input-group">
                                    <input name="Slides[${slideIndex}].VideoUrl" class="form-control" />
                                    <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[${slideIndex}].VideoUrl">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Alt Text</label>
                            <input name="Slides[${slideIndex}].MediaAlt" class="form-control" />
                        </div>
                    </div>

                    <!-- Video Settings (only show for video) -->
                    <div class="video-settings" data-slide-index="${slideIndex}" style="display: none;">
                        <div class="row g-2 mt-2">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoAutoPlay" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Auto Play</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoMuted" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Muted</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoLoop" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Loop</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoControls" class="form-check-input" type="checkbox" />
                                    <label class="form-check-label">Controls</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-2 mt-2">
                        <div class="col-md-3">
                            <label class="form-label">Button Text</label>
                            <input name="Slides[${slideIndex}].ButtonText" class="form-control" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Button URL</label>
                            <input name="Slides[${slideIndex}].ButtonUrl" class="form-control" />
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Order</label>
                            <input name="Slides[${slideIndex}].DisplayOrder" class="form-control" type="number" min="1" value="${slideIndex + 1}" />
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Animation</label>
                            <select name="Slides[${slideIndex}].AnimationType" class="form-select">
                                <option value="fade">Fade</option>
                                <option value="slide">Slide</option>
                                <option value="zoom">Zoom</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Duration (ms)</label>
                            <input name="Slides[${slideIndex}].Duration" class="form-control" type="number" min="1000" max="30000" step="1000" value="5000" />
                        </div>
                    </div>

                    <div class="mt-2">
                        <div class="form-check">
                            <input name="Slides[${slideIndex}].IsActive" class="form-check-input" type="checkbox" checked />
                            <label class="form-check-label">Active</label>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', slideHtml);
            slideIndex++;
            updateSlideNumbers();
            initializeMediaTypeHandlers();

            // Initialize CKEditor for newly added textareas
            const newSlide = container.lastElementChild;
            newSlide.querySelectorAll('.ckeditor-full').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                                'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', '|',
                                'alignment', '|',
                                'link', 'insertImage', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', 'horizontalLine', '|',
                                'specialCharacters', '|',
                                'undo', 'redo'
                            ],
                            shouldNotGroupWhenFull: true
                        },
                        fontSize: {
                            options: [
                                9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                            ]
                        },
                        fontFamily: {
                            options: [
                                'default',
                                'Arial, Helvetica, sans-serif',
                                'Courier New, Courier, monospace',
                                'Georgia, serif',
                                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                                'Tahoma, Geneva, sans-serif',
                                'Times New Roman, Times, serif',
                                'Trebuchet MS, Helvetica, sans-serif',
                                'Verdana, Geneva, sans-serif',
                                'Inter, sans-serif',
                                'Poppins, sans-serif',
                                'Lato, sans-serif'
                            ]
                        },
                        fontColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        fontBackgroundColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        image: {
                            toolbar: [
                                'imageTextAlternative',
                                'imageStyle:inline',
                                'imageStyle:block',
                                'imageStyle:side'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('Full CKEditor initialized for new slide:', textarea.name);
                    })
                    .catch(error => {
                        console.error('Full CKEditor initialization error:', error);
                    });
            });
        }

        function removeSlide(button) {
            const slideItem = button.closest('.slide-item');
            slideItem.remove();
            updateSlideNumbers();
        }

        function updateSlideNumbers() {
            const slides = document.querySelectorAll('.slide-item');
            slides.forEach((slide, index) => {
                const header = slide.querySelector('h6');
                header.textContent = `Slide ${index + 1}`;
            });
        }

        function initializeMediaTypeHandlers() {
            // Handle media type changes
            document.querySelectorAll('.media-type-select').forEach(select => {
                select.addEventListener('change', function() {
                    const slideIndex = this.dataset.slideIndex;
                    const selectedType = this.value;
                    const slideContainer = this.closest('.slide-item');

                    // Hide all media fields
                    slideContainer.querySelectorAll('.media-fields').forEach(field => {
                        field.style.display = 'none';
                    });

                    // Show selected media type fields
                    const targetFields = slideContainer.querySelector(`[data-media-type="${selectedType}"]`);
                    if (targetFields) {
                        targetFields.style.display = 'block';
                    }

                    // Show/hide video settings
                    const videoSettings = slideContainer.querySelector(`.video-settings[data-slide-index="${slideIndex}"]`);
                    if (videoSettings) {
                        videoSettings.style.display = selectedType === 'video' ? 'block' : 'none';
                    }

                    // Update hidden MediaType field
                    const hiddenMediaType = slideContainer.querySelector(`input[name="Slides[${slideIndex}].MediaType"]`);
                    if (hiddenMediaType) {
                        hiddenMediaType.value = selectedType;
                    }
                });
            });
        }

        function initializeFileUpload() {
            // Handle file upload buttons
            document.querySelectorAll('.upload-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetField = this.dataset.target;
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = targetField.includes('Video') ? 'video/*' : 'image/*';

                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (file) {
                            // Create FormData for file upload
                            const formData = new FormData();
                            formData.append('file', file);

                            // Show loading state
                            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                            btn.disabled = true;

                            // Upload file
                            fetch('/Admin/Settings/UploadFile', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Update the input field with the uploaded file URL
                                    const targetInput = document.querySelector(`input[name="${targetField}"]`);
                                    if (targetInput) {
                                        targetInput.value = data.url;
                                    }
                                } else {
                                    alert('Upload failed: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Upload error:', error);
                                alert('Upload failed. Please try again.');
                            })
                            .finally(() => {
                                // Reset button state
                                btn.innerHTML = '<i class="fas fa-upload"></i>';
                                btn.disabled = false;
                            });
                        }
                    };

                    input.click();
                });
            });
        }

        // Initialize CKEditor
        function initializeCKEditor() {
            // Initialize basic CKEditor for main content fields
            document.querySelectorAll('.ckeditor').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'bold', 'italic', 'underline', '|',
                                'link', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('CKEditor initialized for:', textarea.name);
                    })
                    .catch(error => {
                        console.error('CKEditor initialization error:', error);
                    });
            });

            // Initialize full-featured CKEditor for slide content fields
            document.querySelectorAll('.ckeditor-full').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                                'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', '|',
                                'alignment', '|',
                                'link', 'insertImage', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', 'horizontalLine', '|',
                                'specialCharacters', '|',
                                'undo', 'redo'
                            ],
                            shouldNotGroupWhenFull: true
                        },
                        fontSize: {
                            options: [
                                9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                            ]
                        },
                        fontFamily: {
                            options: [
                                'default',
                                'Arial, Helvetica, sans-serif',
                                'Courier New, Courier, monospace',
                                'Georgia, serif',
                                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                                'Tahoma, Geneva, sans-serif',
                                'Times New Roman, Times, serif',
                                'Trebuchet MS, Helvetica, sans-serif',
                                'Verdana, Geneva, sans-serif',
                                'Inter, sans-serif',
                                'Poppins, sans-serif',
                                'Lato, sans-serif'
                            ]
                        },
                        fontColor: {
                            colors: [
                                {
                                    color: 'hsl(0, 0%, 0%)',
                                    label: 'Black'
                                },
                                {
                                    color: 'hsl(0, 0%, 30%)',
                                    label: 'Dim grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 60%)',
                                    label: 'Grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 90%)',
                                    label: 'Light grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 100%)',
                                    label: 'White',
                                    hasBorder: true
                                },
                                {
                                    color: 'hsl(0, 75%, 60%)',
                                    label: 'Red'
                                },
                                {
                                    color: 'hsl(30, 75%, 60%)',
                                    label: 'Orange'
                                },
                                {
                                    color: 'hsl(60, 75%, 60%)',
                                    label: 'Yellow'
                                },
                                {
                                    color: 'hsl(90, 75%, 60%)',
                                    label: 'Light green'
                                },
                                {
                                    color: 'hsl(120, 75%, 60%)',
                                    label: 'Green'
                                },
                                {
                                    color: 'hsl(150, 75%, 60%)',
                                    label: 'Aquamarine'
                                },
                                {
                                    color: 'hsl(180, 75%, 60%)',
                                    label: 'Turquoise'
                                },
                                {
                                    color: 'hsl(210, 75%, 60%)',
                                    label: 'Light blue'
                                },
                                {
                                    color: 'hsl(240, 75%, 60%)',
                                    label: 'Blue'
                                },
                                {
                                    color: 'hsl(270, 75%, 60%)',
                                    label: 'Purple'
                                }
                            ]
                        },
                        fontBackgroundColor: {
                            colors: [
                                {
                                    color: 'hsl(0, 0%, 100%)',
                                    label: 'White',
                                    hasBorder: true
                                },
                                {
                                    color: 'hsl(0, 0%, 90%)',
                                    label: 'Light grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 60%)',
                                    label: 'Grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 30%)',
                                    label: 'Dim grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 0%)',
                                    label: 'Black'
                                },
                                {
                                    color: 'hsl(0, 75%, 60%)',
                                    label: 'Red'
                                },
                                {
                                    color: 'hsl(30, 75%, 60%)',
                                    label: 'Orange'
                                },
                                {
                                    color: 'hsl(60, 75%, 60%)',
                                    label: 'Yellow'
                                },
                                {
                                    color: 'hsl(90, 75%, 60%)',
                                    label: 'Light green'
                                },
                                {
                                    color: 'hsl(120, 75%, 60%)',
                                    label: 'Green'
                                },
                                {
                                    color: 'hsl(150, 75%, 60%)',
                                    label: 'Aquamarine'
                                },
                                {
                                    color: 'hsl(180, 75%, 60%)',
                                    label: 'Turquoise'
                                },
                                {
                                    color: 'hsl(210, 75%, 60%)',
                                    label: 'Light blue'
                                },
                                {
                                    color: 'hsl(240, 75%, 60%)',
                                    label: 'Blue'
                                },
                                {
                                    color: 'hsl(270, 75%, 60%)',
                                    label: 'Purple'
                                }
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        image: {
                            toolbar: [
                                'imageTextAlternative',
                                'imageStyle:inline',
                                'imageStyle:block',
                                'imageStyle:side'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('Full CKEditor initialized for:', textarea.name);
                    })
                    .catch(error => {
                        console.error('Full CKEditor initialization error:', error);
                    });
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeMediaTypeHandlers();
            initializeFileUpload();
            initializeCKEditor();
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>

    <style>
        .admin-content-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 12px 12px 0 0 !important;
            padding: 1rem 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .slide-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6 !important;
        }

        .form-label.required::after {
            content: " *";
            color: #dc3545;
        }

        .btn-group .btn {
            margin-right: 0.25rem;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }
    </style>
}
