@model IEnumerable<Technoloway.Core.Entities.HeroSlide>

@{
    ViewData["Title"] = "Manage Slides";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-images me-2"></i>
                Manage Slides
            </h1>
            <p class="admin-page-subtitle">@ViewBag.HeroSectionTitle - Slideshow Management</p>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Hero Sections
            </a>
        </div>
    </div>
</div>

@Html.AntiForgeryToken()

@if (Model.Any())
{
    <div class="row g-3">
        @foreach (var slide in Model)
        {
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="admin-card slide-card h-100">
                    <div class="card-body p-3">
                        <!-- Slide Header -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="slide-order">
                                <span class="badge badge-modern-slide">@slide.DisplayOrder</span>
                            </div>
                            <div class="slide-status">
                                <div class="form-check form-switch">
                                    <input class="form-check-input slide-status-toggle" type="checkbox"
                                           @(slide.IsActive ? "checked" : "")
                                           data-id="@slide.Id"
                                           data-hero-section-id="@slide.HeroSectionId"
                                           title="Toggle Active Status">
                                </div>
                            </div>
                        </div>

                        <!-- Media Preview -->
                        <div class="media-preview mb-3">
                            @if (slide.MediaType == "image" && !string.IsNullOrEmpty(slide.ImageUrl))
                            {
                                <div class="image-preview">
                                    <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="img-fluid rounded" style="max-height: 120px; width: 100%; object-fit: cover;">
                                </div>
                            }
                            else if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                            {
                                <div class="video-preview">
                                    <div class="video-placeholder bg-dark text-white d-flex align-items-center justify-content-center rounded" style="height: 120px;">
                                        <i class="fas fa-play-circle fa-3x"></i>
                                    </div>
                                    <small class="text-muted">Video: @slide.VideoUrl</small>
                                </div>
                            }
                            else
                            {
                                <div class="no-media bg-light d-flex align-items-center justify-content-center rounded" style="height: 120px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            }
                        </div>

                        <!-- Content Preview -->
                        <div class="content-preview mb-3">
                            <div class="content-text">
                                @Html.Raw(slide.Content.Length > 100 ? slide.Content.Substring(0, 100) + "..." : slide.Content)
                            </div>
                        </div>

                        <!-- Slide Info -->
                        <div class="slide-info mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Duration: @slide.Duration ms
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-magic me-1"></i>
                                        @(slide.AnimationType ?? "fade")
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="btn-group w-100" role="group">
                                <a asp-action="Edit" asp-route-id="@slide.HeroSectionId"
                                   class="btn btn-outline-primary btn-sm" title="Edit Hero Section">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@slide.HeroSectionId"
                                   class="btn btn-outline-info btn-sm" title="Preview Hero Section">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-danger btn-sm delete-slide-btn"
                                        data-id="@slide.Id" title="Delete Slide">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="admin-card">
        <div class="card-body">
            <div class="empty-state text-center py-5">
                <div class="empty-state-icon mb-3">
                    <i class="fas fa-images fa-3x text-muted"></i>
                </div>
                <h3 class="empty-state-title">No Slides Found</h3>
                <p class="empty-state-description text-muted mb-4">
                    This hero section doesn't have any slides yet. Add slides to create a dynamic slideshow.
                </p>
                <button class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Add First Slide
                </button>
            </div>
        </div>
    </div>
}

<style>
    .slide-card {
        transition: all 0.3s ease;
        border: 1px solid var(--admin-border);
        border-radius: 12px;
    }

    .slide-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .media-preview img {
        transition: transform 0.3s ease;
    }

    .slide-card:hover .media-preview img {
        transform: scale(1.05);
    }

    .content-preview {
        background: rgba(248, 249, 250, 0.8);
        padding: 0.75rem;
        border-radius: 8px;
        border-left: 3px solid var(--admin-primary);
    }

    .content-text {
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .action-buttons .btn-group .btn {
        flex: 1;
        border-radius: 0;
    }

    .action-buttons .btn-group .btn:first-child {
        border-radius: 6px 0 0 6px;
    }

    .action-buttons .btn-group .btn:last-child {
        border-radius: 0 6px 6px 0;
    }

    .action-buttons .btn-group .btn:hover {
        z-index: 2;
    }

    /* Modern badge styling for slides */
    .badge-modern-slide {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        font-size: 0.7rem;
        font-weight: 600;
        padding: 0.3rem 0.6rem;
        border-radius: 50%;
        min-width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(240, 147, 251, 0.4);
    }

    .empty-state-icon {
        opacity: 0.5;
    }

    .empty-state-title {
        color: #495057;
        font-weight: 600;
    }

    .empty-state-description {
        max-width: 400px;
        margin: 0 auto;
    }

    /* Slide status toggle styling */
    .slide-status-toggle {
        cursor: pointer;
        transform: scale(1.1);
    }

    .slide-status-toggle:checked {
        background-color: #10b981 !important;
        border-color: #10b981 !important;
    }

    .slide-status-toggle:focus {
        border-color: #10b981;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
    }

    /* Loading state for status toggle */
    .slide-status-toggle.loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Success animation */
    .slide-status-toggle.success {
        animation: successPulse 0.6s ease-in-out;
    }

    @@keyframes successPulse {
        0% { transform: scale(1.1); }
        50% { transform: scale(1.3); }
        100% { transform: scale(1.1); }
    }

    @@media (max-width: 768px) {
        .action-buttons .btn-group {
            flex-direction: column;
        }

        .action-buttons .btn-group .btn {
            border-radius: 6px !important;
            margin-bottom: 0.25rem;
        }

        .action-buttons .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Slide status toggle functionality
            $('.slide-status-toggle').on('change', function() {
                const toggle = $(this);
                const slideId = toggle.data('id');
                const isChecked = toggle.is(':checked');

                // Add loading state
                toggle.addClass('loading');

                $.ajax({
                    url: '@Url.Action("ToggleSlideActive", "HeroSections")',
                    type: 'POST',
                    data: { id: slideId },
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update toggle state to match server response
                            toggle.prop('checked', response.isActive);

                            // Add success animation
                            toggle.addClass('success');
                            setTimeout(() => toggle.removeClass('success'), 600);

                            // Show success message
                            showToast('success', `Slide ${response.isActive ? 'activated' : 'deactivated'} successfully`);
                        } else {
                            // Revert toggle state on error
                            toggle.prop('checked', !isChecked);
                            showToast('error', response.message || 'Error updating status');
                        }
                    },
                    error: function() {
                        // Revert toggle state on error
                        toggle.prop('checked', !isChecked);
                        showToast('error', 'Error updating slide status');
                    },
                    complete: function() {
                        // Remove loading state
                        toggle.removeClass('loading');
                    }
                });
            });

            // Delete slide functionality
            $('.delete-slide-btn').on('click', function() {
                const slideId = $(this).data('id');
                const slideCard = $(this).closest('.col-xl-4');

                if (confirm('Are you sure you want to permanently delete this slide? This action cannot be undone.')) {
                    $.ajax({
                        url: '@Url.Action("DeleteSlide", "HeroSections")',
                        type: 'POST',
                        data: { id: slideId },
                        headers: {
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // Remove the slide card with animation
                                slideCard.fadeOut(300, function() {
                                    $(this).remove();

                                    // Check if no slides left
                                    if ($('.slide-card').length === 0) {
                                        location.reload(); // Reload to show empty state
                                    }
                                });

                                showToast('success', response.message);
                            } else {
                                showToast('error', response.message || 'Error deleting slide');
                            }
                        },
                        error: function() {
                            showToast('error', 'Error deleting slide');
                        }
                    });
                }
            });
        });

        // Toast notification function
        function showToast(type, message) {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'info' ? 'info' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            // Create toast container if it doesn't exist
            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
            }

            const $toast = $(toastHtml);
            $('#toast-container').append($toast);

            const toast = new bootstrap.Toast($toast[0]);
            toast.show();

            // Remove toast element after it's hidden
            $toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
