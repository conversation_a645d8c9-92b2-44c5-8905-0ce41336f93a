@model IEnumerable<Technoloway.Core.Entities.HeroSection>
@using Technoloway.Core.Entities

@{
    ViewData["Title"] = "Hero Sections";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-image me-2"></i>
                Hero Sections
            </h1>
            <p class="admin-page-subtitle">Manage homepage hero sections and slideshow content</p>
        </div>
        <div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create New Hero Section
            </a>
        </div>
    </div>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@Html.AntiForgeryToken()

@if (Model.Any())
{
    <div class="row g-3">
        @foreach (var item in Model)
        {
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="admin-card hero-section-card h-100">
                    <div class="card-body p-3">
                        <!-- Header with Title and Status -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="hero-title">
                                <span class="badge badge-modern">@item.PageName</span>
                            </div>
                            <div class="hero-status">
                                <div class="form-check form-switch">
                                    <input class="form-check-input status-toggle" type="checkbox"
                                           @(item.IsActive ? "checked" : "")
                                           data-id="@item.Id"
                                           title="Toggle Active Status">
                                </div>
                            </div>
                        </div>

                        <!-- Hero Preview Section -->
                        <div class="hero-preview mb-3">
                            @if (item.Slides.Any(s => !s.IsDeleted && !string.IsNullOrEmpty(s.ImageUrl)))
                            {
                                var firstSlideWithImage = item.Slides.Where(s => !s.IsDeleted && !string.IsNullOrEmpty(s.ImageUrl)).FirstOrDefault();
                                <div class="hero-image-preview">
                                    <img src="@firstSlideWithImage.ImageUrl" alt="@firstSlideWithImage.MediaAlt" class="img-fluid rounded" style="max-height: 120px; width: 100%; object-fit: cover;">
                                    <div class="slide-count-overlay">
                                        <span class="badge badge-overlay">
                                            <i class="fas fa-images me-1"></i>
                                            @item.Slides.Count(s => !s.IsDeleted)
                                        </span>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="no-hero-preview bg-light d-flex align-items-center justify-content-center rounded" style="height: 120px;">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                        <div class="small text-muted">
                                            @item.Slides.Count(s => !s.IsDeleted) slides
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Content Preview -->
                        <div class="content-preview mb-3">
                            <div class="hero-title-text fw-bold mb-1">@item.Title</div>
                            <div class="hero-main-title text-primary small mb-1">@Html.Raw(item.MainTitle)</div>
                            @if (!string.IsNullOrEmpty(item.MainSubtitle))
                            {
                                <div class="hero-subtitle text-muted small">
                                    @(item.MainSubtitle.Length > 60 ? item.MainSubtitle.Substring(0, 60) + "..." : item.MainSubtitle)
                                </div>
                            }
                        </div>

                        <!-- Hero Info -->
                        <div class="hero-info mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-@(item.EnableSlideshow ? "play" : "stop") me-1"></i>
                                        @(item.EnableSlideshow ? "Auto" : "Manual")
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        @(item.EnableSlideshow ? $"{item.SlideshowSpeed}ms" : "Static")
                                    </small>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    @item.LastModified.ToString("MMM dd, HH:mm")
                                </small>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-info btn-sm slides-btn"
                                        data-id="@item.Id" title="Manage Slides">
                                    <i class="fas fa-images"></i>
                                </button>
                                <a asp-action="Details" asp-route-id="@item.Id"
                                   class="btn btn-outline-info btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@item.Id"
                                   class="btn btn-outline-primary btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Delete" asp-route-id="@item.Id"
                                   class="btn btn-outline-danger btn-sm" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="admin-card">
        <div class="card-body">
            <div class="empty-state text-center py-5">
                <div class="empty-state-icon mb-3">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                <h3 class="empty-state-title">No Hero Sections Found</h3>
                <p class="empty-state-description text-muted mb-4">
                    Create your first hero section to manage homepage slideshow content and call-to-action elements.
                </p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create First Hero Section
                </a>
            </div>
        </div>
    </div>
}

@section Styles {
<style>
    .hero-section-card {
        transition: all 0.3s ease;
        border: 1px solid var(--admin-border);
        border-radius: 12px;
        height: 100%;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .hero-section-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        border-color: rgba(79, 70, 229, 0.2);
    }

    .hero-preview {
        position: relative;
    }

    .hero-image-preview {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
    }

    .hero-image-preview img {
        transition: transform 0.3s ease;
    }

    .hero-section-card:hover .hero-image-preview img {
        transform: scale(1.05);
    }

    .slide-count-overlay {
        position: absolute;
        top: 8px;
        right: 8px;
    }

    .no-hero-preview {
        border: 2px dashed #e2e8f0;
        background: #f8fafc !important;
    }

    .content-preview {
        background: rgba(248, 249, 250, 0.8);
        padding: 0.75rem;
        border-radius: 8px;
        border-left: 3px solid var(--admin-primary);
    }

    .hero-title-text {
        color: #2d3748;
        font-size: 0.9rem;
    }

    .hero-main-title {
        font-weight: 600;
        line-height: 1.3;
    }

    .hero-subtitle {
        line-height: 1.4;
    }

    .status-toggle {
        cursor: pointer;
        transform: scale(1.1);
    }

    .status-toggle:checked {
        background-color: var(--admin-success);
        border-color: var(--admin-success);
    }

    .action-buttons .btn-group .btn {
        flex: 1;
        border-radius: 0;
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
        transition: all 0.2s ease;
    }

    .action-buttons .btn-group .btn:first-child {
        border-radius: 6px 0 0 6px;
    }

    .action-buttons .btn-group .btn:last-child {
        border-radius: 0 6px 6px 0;
    }

    .action-buttons .btn-group .btn:hover {
        z-index: 2;
    }

    /* Icon styling for small buttons */
    .action-buttons .btn i {
        font-size: 0.75rem;
    }

    /* Modern badge styling */
    .badge-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 0.7rem;
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    .badge-overlay {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 0.65rem;
        font-weight: 500;
        padding: 0.2rem 0.4rem;
        border-radius: 8px;
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Status toggle enhanced styling */
    .form-check-input:checked {
        background-color: #10b981 !important;
        border-color: #10b981 !important;
    }

    .form-check-input:focus {
        border-color: #10b981;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
    }

    /* Slides button styling */
    .slides-btn {
        transition: all 0.2s ease;
    }

    .slides-btn:hover {
        transform: scale(1.02);
    }

    .empty-state-icon {
        opacity: 0.5;
    }

    .empty-state-title {
        color: #495057;
        font-weight: 600;
    }

    .empty-state-description {
        max-width: 400px;
        margin: 0 auto;
    }

    /* Loading state for status toggle */
    .status-toggle.loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Success animation */
    .status-toggle.success {
        animation: successPulse 0.6s ease-in-out;
    }

    @@keyframes successPulse {
        0% { transform: scale(1.2); }
        50% { transform: scale(1.4); }
        100% { transform: scale(1.2); }
    }

    @@media (max-width: 768px) {
        .col-xl-4 {
            margin-bottom: 1rem;
        }

        .action-buttons .btn-group {
            flex-direction: column;
        }

        .action-buttons .btn-group .btn {
            border-radius: 6px !important;
            margin-bottom: 0.25rem;
        }

        .action-buttons .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
</style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Status toggle functionality
            $('.status-toggle').on('change', function() {
                const toggle = $(this);
                const heroSectionId = toggle.data('id');
                const isChecked = toggle.is(':checked');

                // Add loading state
                toggle.addClass('loading');

                $.ajax({
                    url: '@Url.Action("ToggleActive", "HeroSections")',
                    type: 'POST',
                    data: { id: heroSectionId },
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update toggle state to match server response
                            toggle.prop('checked', response.isActive);

                            // Add success animation
                            toggle.addClass('success');
                            setTimeout(() => toggle.removeClass('success'), 600);

                            // Show success message
                            showToast('success', `Hero section ${response.isActive ? 'activated' : 'deactivated'} successfully`);
                        } else {
                            // Revert toggle state on error
                            toggle.prop('checked', !isChecked);
                            showToast('error', response.message || 'Error updating status');
                        }
                    },
                    error: function() {
                        // Revert toggle state on error
                        toggle.prop('checked', !isChecked);
                        showToast('error', 'Error updating hero section status');
                    },
                    complete: function() {
                        // Remove loading state
                        toggle.removeClass('loading');
                    }
                });
            });

            // Slides button functionality
            $('.slides-btn').on('click', function() {
                const heroSectionId = $(this).data('id');
                window.location.href = '@Url.Action("Slides", "HeroSections")/' + heroSectionId;
            });
        });

        // Toast notification function
        function showToast(type, message) {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            // Create toast container if it doesn't exist
            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
            }

            const $toast = $(toastHtml);
            $('#toast-container').append($toast);

            const toast = new bootstrap.Toast($toast[0]);
            toast.show();

            // Remove toast element after it's hidden
            $toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
