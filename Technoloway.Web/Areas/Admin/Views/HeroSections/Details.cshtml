@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Hero Section Details";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<!-- Page Header -->
<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-eye me-2"></i>
                @Model.Title
            </h1>
            <p class="admin-page-subtitle">Hero section details and configuration</p>
        </div>
        <div class="d-flex gap-2">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back
            </a>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>Edit
            </a>
        </div>
    </div>
</div>

<!-- Status Badges -->
<div class="d-flex flex-wrap gap-2 mb-4">
    @if (Model.IsActive)
    {
        <span class="badge bg-success fs-6">
            <i class="fas fa-check-circle me-1"></i>Active
        </span>
    }
    else
    {
        <span class="badge bg-secondary fs-6">
            <i class="fas fa-pause-circle me-1"></i>Inactive
        </span>
    }
    <span class="badge bg-info fs-6">
        <i class="fas fa-file-alt me-1"></i>@Model.PageName Page
    </span>
    @if (Model.EnableSlideshow)
    {
        <span class="badge bg-primary fs-6">
            <i class="fas fa-images me-1"></i>Slideshow
        </span>
    }
    @if (Model.Slides.Any())
    {
        <span class="badge bg-warning fs-6">
            <i class="fas fa-layer-group me-1"></i>@Model.Slides.Count Slides
        </span>
    }
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="admin-card mb-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Title</label>
                            <div class="form-control-plaintext">@Model.Title</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Page Assignment</label>
                            <div class="form-control-plaintext">@Model.PageName</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Meta Description</label>
                            <div class="form-control-plaintext">@(Model.MetaDescription ?? "Not set")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Meta Keywords</label>
                            <div class="form-control-plaintext">@(Model.MetaKeywords ?? "Not set")</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hero Content -->
        <div class="admin-card mb-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-edit me-2"></i>Hero Content
                </h5>
                <div class="mb-3">
                    <label class="form-label fw-semibold">Main Title</label>
                    <div class="border rounded p-3 bg-light">
                        @Html.Raw(Model.MainTitle ?? "<em class='text-muted'>Not set</em>")
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-semibold">Main Subtitle</label>
                    <div class="border rounded p-3 bg-light">
                        @Html.Raw(Model.MainSubtitle ?? "<em class='text-muted'>Not set</em>")
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-semibold">Main Description</label>
                    <div class="border rounded p-3 bg-light">
                        @Html.Raw(Model.MainDescription ?? "<em class='text-muted'>Not set</em>")
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-semibold">Call-to-Action Buttons</label>
                    <div class="d-flex gap-2 flex-wrap">
                        @if (!string.IsNullOrEmpty(Model.PrimaryButtonText))
                        {
                            <a href="@Model.PrimaryButtonUrl" class="btn btn-primary btn-sm">
                                @Model.PrimaryButtonText
                            </a>
                        }
                        @if (!string.IsNullOrEmpty(Model.SecondaryButtonText))
                        {
                            <a href="@Model.SecondaryButtonUrl" class="btn btn-secondary btn-sm">
                                @Model.SecondaryButtonText
                            </a>
                        }
                        @if (string.IsNullOrEmpty(Model.PrimaryButtonText) && string.IsNullOrEmpty(Model.SecondaryButtonText))
                        {
                            <em class="text-muted">No buttons configured</em>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Hero Slides -->
        @if (Model.Slides.Any())
        {
            <div class="admin-card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-images me-2"></i>Hero Slides (@Model.Slides.Count)
                    </h5>
                    <div class="row g-3">
                        @foreach (var slide in Model.Slides.OrderBy(s => s.DisplayOrder))
                        {
                            <div class="col-md-6">
                                <div class="border rounded p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="mb-0">Slide @slide.DisplayOrder</h6>
                                        <div class="d-flex gap-1">
                                            @if (slide.IsActive)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Inactive</span>
                                            }
                                            <span class="badge bg-info">@slide.MediaType</span>
                                        </div>
                                    </div>

                                    @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                                    {
                                        <video class="w-100 mb-2" style="max-height: 120px; object-fit: cover;" controls>
                                            <source src="@slide.VideoUrl" type="video/mp4">
                                        </video>
                                    }
                                    else if (!string.IsNullOrEmpty(slide.ImageUrl))
                                    {
                                        <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="w-100 mb-2" style="max-height: 120px; object-fit: cover;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center mb-2" style="height: 120px;">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                    }

                                    <div class="small text-muted" style="max-height: 60px; overflow: hidden;">
                                        @Html.Raw(slide.Content)
                                    </div>

                                    @if (!string.IsNullOrEmpty(slide.ButtonText))
                                    {
                                        <div class="mt-2">
                                            <a href="@slide.ButtonUrl" class="btn btn-primary btn-sm">
                                                @slide.ButtonText
                                            </a>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Configuration Settings -->
        <div class="admin-card mb-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-cogs me-2"></i>Settings
                </h5>
                <div class="row g-2 text-center">
                    <div class="col-6">
                        <div class="p-2 border rounded">
                            <div class="h5 text-primary mb-1">@Model.Slides.Count</div>
                            <small class="text-muted">Total Slides</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="p-2 border rounded">
                            <div class="h5 text-success mb-1">@Model.Slides.Count(s => s.IsActive)</div>
                            <small class="text-muted">Active Slides</small>
                        </div>
                    </div>
                </div>

                <hr class="my-3">

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold">Slideshow</span>
                    @if (Model.EnableSlideshow)
                    {
                        <span class="badge bg-success">Enabled</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Disabled</span>
                    }
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold">Auto Play</span>
                    @if (Model.AutoPlay)
                    {
                        <span class="badge bg-success">Yes</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">No</span>
                    }
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold">Navigation Dots</span>
                    @if (Model.ShowDots)
                    {
                        <span class="badge bg-success">Visible</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Hidden</span>
                    }
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold">Navigation Arrows</span>
                    @if (Model.ShowArrows)
                    {
                        <span class="badge bg-success">Visible</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Hidden</span>
                    }
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold">Speed</span>
                    <span class="badge bg-info">@Model.SlideshowSpeed ms</span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-semibold">Floating Elements</span>
                    @if (Model.EnableFloatingElements)
                    {
                        <span class="badge bg-success">Enabled</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Disabled</span>
                    }
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="admin-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="d-grid gap-2">
                    <a asp-action="Slides" asp-route-id="@Model.Id" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-images me-1"></i>Manage Slides
                    </a>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit Hero Section
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>




