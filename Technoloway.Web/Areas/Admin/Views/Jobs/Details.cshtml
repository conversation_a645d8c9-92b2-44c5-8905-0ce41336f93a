@model Technoloway.Core.Entities.JobListing

@{
    ViewData["Title"] = "Job Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Job Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Applications" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-users fa-sm text-white-50"></i> Applications
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Job Description</h6>
            </div>
            <div class="card-body">
                <h2 class="mb-3">@Model.Title</h2>
                
                <div class="mb-4">
                    <h5>Description</h5>
                    <div class="content">
                        @Html.Raw(Model.Description)
                    </div>
                </div>
                
                <div class="mb-4">
                    <h5>Requirements</h5>
                    <div class="content">
                        @Html.Raw(Model.Requirements)
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Job Information</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Status</dt>
                    <dd class="col-sm-8">
                        @if (Model.IsActive)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Inactive</span>
                        }
                        @if (Model.ExpiresAt.HasValue && Model.ExpiresAt < DateTime.UtcNow)
                        {
                            <span class="badge bg-danger">Expired</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-4">Created</dt>
                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd")</dd>
                    
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <dt class="col-sm-4">Updated</dt>
                        <dd class="col-sm-8">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd")</dd>
                    }
                    
                    @if (Model.ExpiresAt.HasValue)
                    {
                        <dt class="col-sm-4">Expires</dt>
                        <dd class="col-sm-8">
                            @Model.ExpiresAt.Value.ToString("yyyy-MM-dd")
                            @if (Model.ExpiresAt < DateTime.UtcNow)
                            {
                                <span class="text-danger">(Expired)</span>
                            }
                        </dd>
                    }
                    
                    <dt class="col-sm-4">Location</dt>
                    <dd class="col-sm-8">
                        @if (Model.IsRemote)
                        {
                            <span>Remote</span>
                        }
                        else
                        {
                            <span>@Model.Location</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-4">Type</dt>
                    <dd class="col-sm-8">@Model.EmploymentType</dd>
                    
                    <dt class="col-sm-4">Salary</dt>
                    <dd class="col-sm-8">
                        @if (Model.SalaryMin.HasValue && Model.SalaryMax.HasValue)
                        {
                            <span>@Model.SalaryMin.Value.ToString("C0") - @Model.SalaryMax.Value.ToString("C0") @Model.SalaryCurrency</span>
                        }
                        else if (Model.SalaryMin.HasValue)
                        {
                            <span>From @Model.SalaryMin.Value.ToString("C0") @Model.SalaryCurrency</span>
                        }
                        else if (Model.SalaryMax.HasValue)
                        {
                            <span>Up to @Model.SalaryMax.Value.ToString("C0") @Model.SalaryCurrency</span>
                        }
                        else
                        {
                            <span>Not specified</span>
                        }
                    </dd>
                </dl>
                
                @if (Model.IsActive)
                {
                    <div class="mt-3">
                        <a href="/Careers/Details/@Model.Id" target="_blank" class="btn btn-success btn-sm">
                            <i class="fas fa-external-link-alt"></i> View on Site
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
