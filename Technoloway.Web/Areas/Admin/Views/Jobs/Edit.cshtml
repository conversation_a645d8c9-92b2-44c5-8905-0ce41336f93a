@model Technoloway.Core.Entities.JobListing

@{
    ViewData["Title"] = "Edit Job Listing";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Job Listing</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Job Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label asp-for="Title" class="control-label"></label>
                        <input asp-for="Title" class="form-control" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Description" class="control-label"></label>
                        <textarea asp-for="Description" id="description-editor" class="form-control" rows="10"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Requirements" class="control-label"></label>
                        <textarea asp-for="Requirements" id="requirements-editor" class="form-control" rows="10"></textarea>
                        <span asp-validation-for="Requirements" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Job Status</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">Active</label>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="ExpiresAt" class="control-label">Expiration Date</label>
                                <input asp-for="ExpiresAt" type="date" class="form-control" value="@(Model.ExpiresAt.HasValue ? Model.ExpiresAt.Value.ToString("yyyy-MM-dd") : "")" />
                                <span asp-validation-for="ExpiresAt" class="text-danger"></span>
                                <small class="form-text text-muted">Leave blank for no expiration</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Job Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="Location" class="control-label"></label>
                                <input asp-for="Location" class="form-control" />
                                <span asp-validation-for="Location" class="text-danger"></span>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input asp-for="IsRemote" class="form-check-input" />
                                <label asp-for="IsRemote" class="form-check-label">Remote Position</label>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="EmploymentType" class="control-label">Employment Type</label>
                                <select asp-for="EmploymentType" class="form-control">
                                    <option value="Full-time">Full-time</option>
                                    <option value="Part-time">Part-time</option>
                                    <option value="Contract">Contract</option>
                                    <option value="Temporary">Temporary</option>
                                    <option value="Internship">Internship</option>
                                </select>
                                <span asp-validation-for="EmploymentType" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Salary Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label asp-for="SalaryMin" class="control-label">Minimum Salary</label>
                                <input asp-for="SalaryMin" type="number" step="1000" class="form-control" />
                                <span asp-validation-for="SalaryMin" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="SalaryMax" class="control-label">Maximum Salary</label>
                                <input asp-for="SalaryMax" type="number" step="1000" class="form-control" />
                                <span asp-validation-for="SalaryMax" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="SalaryCurrency" class="control-label">Currency</label>
                                <select asp-for="SalaryCurrency" class="form-control">
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="GBP">GBP</option>
                                    <option value="CAD">CAD</option>
                                    <option value="AUD">AUD</option>
                                </select>
                                <span asp-validation-for="SalaryCurrency" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Save</button>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="https://cdn.ckeditor.com/ckeditor5/38.0.1/classic/ckeditor.js"></script>
    <script>
        ClassicEditor
            .create(document.querySelector('#description-editor'))
            .catch(error => {
                console.error(error);
            });
            
        ClassicEditor
            .create(document.querySelector('#requirements-editor'))
            .catch(error => {
                console.error(error);
            });
    </script>
}
