@model Technoloway.Core.Entities.JobListing

@{
    ViewData["Title"] = "Delete Job";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Job</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5>Are you sure you want to delete this job listing?</h5>
            <p>This action will mark the job as deleted but will not permanently remove it from the database.</p>
        </div>
        
        <div class="mb-4">
            <h4>@Model.Title</h4>
            <p class="text-muted">
                @if (Model.IsActive)
                {
                    <span class="badge bg-success">Active</span>
                }
                else
                {
                    <span class="badge bg-secondary">Inactive</span>
                }
                @if (Model.ExpiresAt.HasValue && Model.ExpiresAt < DateTime.UtcNow)
                {
                    <span class="badge bg-danger">Expired</span>
                }
                <span class="ms-2">Created: @Model.CreatedAt.ToString("yyyy-MM-dd")</span>
                @if (Model.ExpiresAt.HasValue)
                {
                    <span class="ms-2">Expires: @Model.ExpiresAt.Value.ToString("yyyy-MM-dd")</span>
                }
            </p>
            
            <p>
                <strong>Location:</strong> 
                @if (Model.IsRemote)
                {
                    <span>Remote</span>
                }
                else
                {
                    <span>@Model.Location</span>
                }
            </p>
            <p><strong>Type:</strong> @Model.EmploymentType</p>
        </div>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Delete</button>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
