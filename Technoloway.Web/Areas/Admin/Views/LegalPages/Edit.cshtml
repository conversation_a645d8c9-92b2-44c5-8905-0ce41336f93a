@model Technoloway.Web.Areas.Admin.Models.LegalPageViewModel

@{
    ViewData["Title"] = "Edit Legal Page";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Edit Legal Page</h1>
                    <p class="text-muted">Update legal page content and settings</p>
                </div>
                <div>
                    <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>

            <!-- Form Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>Edit "@Model.Title"
                    </h6>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Title" class="form-label">Page Title</label>
                                    <input asp-for="Title" class="form-control" placeholder="e.g., Terms of Service" />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Slug" class="form-label">URL Slug</label>
                                    <input asp-for="Slug" class="form-control" placeholder="e.g., terms-of-service" />
                                    <small class="form-text text-muted">This will be the URL: /@Model.Slug</small>
                                    <span asp-validation-for="Slug" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    <label asp-for="MetaDescription" class="form-label">Meta Description</label>
                                    <input asp-for="MetaDescription" class="form-control" placeholder="Brief description for search engines" />
                                    <span asp-validation-for="MetaDescription" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">Status</label>
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label">
                                            Active (visible to users)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Main Content -->
                        <div class="admin-card mb-4">
                            <div class="card-header bg-white border-bottom p-3">
                                <h6 class="mb-0 fw-bold text-gray-800">
                                    <i class="fas fa-file-alt me-2 text-warning"></i>Main Content
                                </h6>
                            </div>
                            <div class="card-body p-4">
                                <div class="form-group mb-4">
                                    <label asp-for="Content" class="form-label fw-semibold">
                                        <i class="fas fa-pen me-1"></i>Content
                                    </label>
                                    <textarea asp-for="Content" id="editor" class="form-control" rows="15"
                                              style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                              placeholder="Enter the main content of the legal page..."></textarea>
                                    <span asp-validation-for="Content" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Sections -->
                        <div class="admin-card mb-4">
                            <div class="card-header bg-white border-bottom p-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 fw-bold text-gray-800">
                                        <i class="fas fa-list me-2 text-info"></i>Content Sections
                                    </h6>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addSection()">
                                        <i class="fas fa-plus me-1"></i>Add Section
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-4">
                                <div id="sectionsContainer">
                                @if (Model.Sections != null && Model.Sections.Any())
                                {
                                    @for (int i = 0; i < Model.Sections.Count; i++)
                                    {
                                        <div class="section-item border rounded p-3 mb-3" data-index="@i">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Section @(i + 1)</h6>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSection(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>

                                            <input asp-for="Sections[i].Id" type="hidden" />

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label class="form-label">Section Title</label>
                                                        <input asp-for="Sections[i].Title" class="form-control" placeholder="Section title" />
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label class="form-label">Icon Class</label>
                                                        <input asp-for="Sections[i].IconClass" class="form-control" placeholder="fas fa-shield-alt" />
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label class="form-label">Display Order</label>
                                                        <input asp-for="Sections[i].DisplayOrder" type="number" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group mb-3">
                                                <label class="form-label">Section Content</label>
                                                <textarea asp-for="Sections[i].Content" class="form-control section-content" rows="4"
                                                          placeholder="Enter section content..."></textarea>
                                            </div>

                                            <div class="form-check">
                                                <input asp-for="Sections[i].IsActive" type="checkbox" class="form-check-input" />
                                                <label class="form-check-label">Active</label>
                                                <input asp-for="Sections[i].IsActive" type="hidden" value="false" />
                                            </div>
                                        </div>
                                    }
                                }
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Legal Page
                            </button>
                            <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-info ms-2">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                            <a href="@Url.Action("Index")" class="btn btn-secondary ms-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.ckeditor.com/ckeditor5/38.0.1/classic/ckeditor.js"></script>
    <script>
        let sectionIndex = @(Model.Sections?.Count ?? 0);
        let mainEditor;
        let sectionEditors = {};

        $(document).ready(function() {
            // Initialize CKEditor for main content
            ClassicEditor
                .create(document.querySelector('#editor'), {
                    toolbar: [
                        'heading', '|',
                        'bold', 'italic', 'link', '|',
                        'bulletedList', 'numberedList', '|',
                        'outdent', 'indent', '|',
                        'blockQuote', 'insertTable', '|',
                        'undo', 'redo'
                    ]
                })
                .then(editor => {
                    mainEditor = editor;
                })
                .catch(error => {
                    console.error(error);
                });

            // Initialize CKEditor for existing sections
            $('.section-content').each(function() {
                const textarea = this;
                const name = $(textarea).attr('name');
                if (name) {
                    ClassicEditor
                        .create(textarea, {
                            toolbar: [
                                'bold', 'italic', 'link', '|',
                                'bulletedList', 'numberedList', '|',
                                'undo', 'redo'
                            ]
                        })
                        .then(editor => {
                            sectionEditors[name] = editor;
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }
            });

            // Auto-generate slug from title
            $('#Title').on('input', function() {
                const title = $(this).val();
                const slug = title.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                $('#Slug').val(slug);
            });
        });

        function addSection() {
            const sectionHtml = `
                <div class="section-item border rounded p-3 mb-3" data-index="${sectionIndex}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Section ${sectionIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSection(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <input name="Sections[${sectionIndex}].Id" type="hidden" value="0" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Section Title</label>
                                <input name="Sections[${sectionIndex}].Title" class="form-control" placeholder="Section title" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label class="form-label">Icon Class</label>
                                <input name="Sections[${sectionIndex}].IconClass" class="form-control" placeholder="fas fa-shield-alt" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label class="form-label">Display Order</label>
                                <input name="Sections[${sectionIndex}].DisplayOrder" type="number" class="form-control" value="${sectionIndex + 1}" />
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Section Content</label>
                        <textarea name="Sections[${sectionIndex}].Content" class="form-control section-content" rows="4"
                                  placeholder="Enter section content..."></textarea>
                    </div>

                    <div class="form-check">
                        <input name="Sections[${sectionIndex}].IsActive" type="checkbox" class="form-check-input" checked />
                        <label class="form-check-label">Active</label>
                        <input name="Sections[${sectionIndex}].IsActive" type="hidden" value="false" />
                    </div>
                </div>
            `;

            $('#sectionsContainer').append(sectionHtml);

            // Initialize CKEditor for the new section content
            const newTextarea = document.querySelector(`textarea[name="Sections[${sectionIndex}].Content"]`);
            if (newTextarea) {
                ClassicEditor
                    .create(newTextarea, {
                        toolbar: [
                            'bold', 'italic', 'link', '|',
                            'bulletedList', 'numberedList', '|',
                            'undo', 'redo'
                        ]
                    })
                    .then(editor => {
                        sectionEditors[`Sections[${sectionIndex}].Content`] = editor;
                    })
                    .catch(error => {
                        console.error(error);
                    });
            }

            sectionIndex++;
        }

        function removeSection(button) {
            const sectionItem = $(button).closest('.section-item');
            const index = sectionItem.data('index');

            // Remove CKEditor instance
            const textarea = sectionItem.find('textarea');
            if (textarea.length) {
                const name = textarea.attr('name');
                if (name && sectionEditors[name]) {
                    sectionEditors[name].destroy();
                    delete sectionEditors[name];
                }
            }

            // Remove the section
            sectionItem.remove();

            // Reindex remaining sections
            reindexSections();
        }

        function reindexSections() {
            $('#sectionsContainer .section-item').each(function(index) {
                $(this).data('index', index);
                $(this).find('h6').text(`Section ${index + 1}`);

                // Update input names
                $(this).find('input, textarea').each(function() {
                    const name = $(this).attr('name');
                    if (name && name.includes('Sections[')) {
                        const newName = name.replace(/Sections\[\d+\]/, `Sections[${index}]`);
                        $(this).attr('name', newName);
                    }
                });

                // Update display order
                $(this).find('input[name$=".DisplayOrder"]').val(index + 1);
            });

            sectionIndex = $('#sectionsContainer .section-item').length;
        }
    </script>
}

@section Styles {
    <style>
        :root {
            --admin-primary: #4e73df;
            --admin-secondary: #858796;
            --admin-success: #1cc88a;
            --admin-info: #36b9cc;
            --admin-warning: #f6c23e;
            --admin-danger: #e74a3b;
            --admin-light: #f8f9fc;
            --admin-dark: #5a5c69;
            --admin-bg: #f8f9fc;
            --admin-border: #e3e6f0;
            --admin-radius: 0.35rem;
        }

        .admin-card {
            background: white;
            border: 1px solid var(--admin-border);
            border-radius: var(--admin-radius);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
        }

        .admin-card .card-header {
            background: white;
            border-bottom: 1px solid var(--admin-border);
            border-radius: var(--admin-radius) var(--admin-radius) 0 0;
        }

        .section-item {
            background-color: #f8f9fc;
            border: 1px solid var(--admin-border);
            border-radius: var(--admin-radius);
            transition: all 0.3s ease;
        }

        .section-item:hover {
            background-color: #eaecf4;
            box-shadow: 0 0.15rem 1rem 0 rgba(58, 59, 69, 0.1);
        }

        .form-label {
            font-weight: 600;
            color: var(--admin-dark);
        }

        .form-label.fw-semibold {
            font-weight: 600;
            color: var(--admin-dark);
        }

        .form-control {
            border: 2px solid var(--admin-border);
            border-radius: var(--admin-radius);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .btn-primary {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
        }

        .btn-outline-primary {
            color: var(--admin-primary);
            border-color: var(--admin-primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
        }

        .text-gray-800 {
            color: var(--admin-dark) !important;
        }

        .text-primary {
            color: var(--admin-primary) !important;
        }

        .text-warning {
            color: var(--admin-warning) !important;
        }

        .text-info {
            color: var(--admin-info) !important;
        }
    </style>
}
