<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Technoloway Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Enhanced Modern Sidebar -->
        <div class="modern-sidebar" id="sidebar-wrapper">
            <!-- Logo Section -->
            <div class="sidebar-header">
                <a class="sidebar-brand" asp-area="Admin" asp-controller="Home" asp-action="Index">
                    <div class="brand-icon">
                        <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">Technoloway</span>
                        <span class="brand-subtitle">Admin Panel</span>
                    </div>
                </a>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Home" asp-action="Index" data-page="dashboard">
                        <div class="nav-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="nav-text">Dashboard</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Content Management</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="HeroSections" asp-action="Index" data-page="hero-sections">
                        <div class="nav-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <span class="nav-text">Hero Sections</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="AboutPages" asp-action="Index" data-page="about-pages">
                        <div class="nav-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <span class="nav-text">About Pages</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Services" asp-action="Index" data-page="services">
                        <div class="nav-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <span class="nav-text">Services</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Projects" asp-action="Index" data-page="projects">
                        <div class="nav-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <span class="nav-text">Projects</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Technologies" asp-action="Index" data-page="technologies">
                        <div class="nav-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <span class="nav-text">Technologies</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="TeamMembers" asp-action="Index" data-page="team">
                        <div class="nav-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-text">Team Members</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Blog" asp-action="Index" data-page="blog">
                        <div class="nav-icon">
                            <i class="fas fa-blog"></i>
                        </div>
                        <span class="nav-text">Blog</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="LegalPages" asp-action="Index" data-page="legal-pages">
                        <div class="nav-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <span class="nav-text">Legal Pages</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Business</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Jobs" asp-action="Index" data-page="jobs">
                        <div class="nav-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <span class="nav-text">Jobs</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Clients" asp-action="Index" data-page="clients">
                        <div class="nav-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <span class="nav-text">Clients</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Invoices" asp-action="Index" data-page="invoices">
                        <div class="nav-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <span class="nav-text">Invoices</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Testimonials" asp-action="Index" data-page="testimonials">
                        <div class="nav-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <span class="nav-text">Testimonials</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="ContactForms" asp-action="Index" data-page="contact">
                        <div class="nav-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <span class="nav-text">Contact Forms</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a class="nav-item" asp-area="Admin" asp-controller="Chatbot" asp-action="Intents" data-page="chatbot">
                        <div class="nav-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <span class="nav-text">Chatbot</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Settings" asp-action="Index" data-page="settings">
                        <div class="nav-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="nav-text">Settings</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" asp-area="Admin" asp-controller="Users" asp-action="Index" data-page="users">
                        <div class="nav-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="nav-text">Users & Roles</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Administrator</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Modern Top Navigation Bar -->
            <nav class="modern-topbar">
                <div class="topbar-container">
                    <!-- Left Section -->
                    <div class="topbar-left">
                        <button class="sidebar-toggle-btn" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Global Search -->
                        <div class="global-search">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" class="search-input" placeholder="Search projects, clients, invoices..." />
                                <div class="search-results" id="searchResults"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Section -->
                    <div class="topbar-right">
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" title="Add New Project" data-bs-toggle="modal" data-bs-target="#quickAddModal">
                                <i class="fas fa-plus"></i>
                            </button>
                            <a class="quick-action-btn" asp-area="" asp-controller="Home" asp-action="Index" target="_blank" title="View Site">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>

                        <!-- Notifications -->
                        <div class="notifications-dropdown">
                            <button class="notification-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <div class="dropdown-menu notification-menu">
                                <div class="notification-header">
                                    <h6>Notifications</h6>
                                    <span class="mark-all-read">Mark all as read</span>
                                </div>
                                <div class="notification-list">
                                    <div class="notification-item unread">
                                        <div class="notification-icon bg-primary">
                                            <i class="fas fa-file-invoice"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-text">New invoice payment received</p>
                                            <span class="notification-time">2 minutes ago</span>
                                        </div>
                                    </div>
                                    <div class="notification-item unread">
                                        <div class="notification-icon bg-success">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-text">New client registered</p>
                                            <span class="notification-time">1 hour ago</span>
                                        </div>
                                    </div>
                                    <div class="notification-item">
                                        <div class="notification-icon bg-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-text">Invoice overdue reminder</p>
                                            <span class="notification-time">3 hours ago</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="notification-footer">
                                    <a href="#" class="view-all-notifications">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="user-profile-dropdown">
                            <button class="user-profile-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar">
                                    <img src="https://ui-avatars.com/api/?name=Admin+User&background=4f46e5&color=fff" alt="Admin User" />
                                </div>
                                <div class="user-info">
                                    <span class="user-name">Admin User</span>
                                    <span class="user-role">Administrator</span>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu user-menu">
                                <div class="user-menu-header">
                                    <div class="user-avatar-large">
                                        <img src="https://ui-avatars.com/api/?name=Admin+User&background=4f46e5&color=fff" alt="Admin User" />
                                    </div>
                                    <div class="user-details">
                                        <h6>Admin User</h6>
                                        <p><EMAIL></p>
                                    </div>
                                </div>
                                <div class="user-menu-items">
                                    <a class="user-menu-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                        <i class="fas fa-user"></i>
                                        <span>My Profile</span>
                                    </a>
                                    <a class="user-menu-item" href="#">
                                        <i class="fas fa-cog"></i>
                                        <span>Settings</span>
                                    </a>
                                    <a class="user-menu-item" href="#">
                                        <i class="fas fa-question-circle"></i>
                                        <span>Help & Support</span>
                                    </a>
                                    <div class="user-menu-divider"></div>
                                    <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                                        <button type="submit" class="user-menu-item logout-btn">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Logout</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="main-content">
                @RenderBody()
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script src="~/js/admin.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
