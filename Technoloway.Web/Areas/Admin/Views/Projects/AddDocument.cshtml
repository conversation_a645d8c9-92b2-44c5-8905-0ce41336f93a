@model Technoloway.Core.Entities.ProjectDocument

@{
    ViewData["Title"] = "Upload Document";
    Layout = "_AdminLayout";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Upload Document for @(project != null ? project.Name : "Project")</h1>
    <div>
        <a asp-action="Documents" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-file fa-sm text-white-50"></i> View Documents
        </a>
        <a asp-action="Details" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Project
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Document Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="AddDocument" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="ProjectId" />
            
            <div class="form-group mb-3">
                <label asp-for="FileName" class="control-label">File Name</label>
                <input asp-for="FileName" class="form-control" required />
                <span asp-validation-for="FileName" class="text-danger"></span>
                <small class="form-text text-muted">Name of the file including extension (e.g., project-proposal.pdf)</small>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="FileUrl" class="control-label">File URL</label>
                <input asp-for="FileUrl" class="form-control" required />
                <span asp-validation-for="FileUrl" class="text-danger"></span>
                <small class="form-text text-muted">URL to the uploaded file</small>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="FileType" class="control-label">File Type</label>
                        <select asp-for="FileType" class="form-control" required>
                            <option value="">-- Select File Type --</option>
                            <option value="PDF">PDF</option>
                            <option value="Word Document">Word Document</option>
                            <option value="Excel Spreadsheet">Excel Spreadsheet</option>
                            <option value="PowerPoint Presentation">PowerPoint Presentation</option>
                            <option value="Image">Image</option>
                            <option value="Text">Text</option>
                            <option value="ZIP Archive">ZIP Archive</option>
                            <option value="Other">Other</option>
                        </select>
                        <span asp-validation-for="FileType" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="FileSize" class="control-label">File Size (KB)</label>
                        <input asp-for="FileSize" type="number" class="form-control" required />
                        <span asp-validation-for="FileSize" class="text-danger"></span>
                        <small class="form-text text-muted">Size of the file in kilobytes</small>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <p class="mb-0">
                    <i class="fas fa-info-circle"></i> 
                    Note: This form only records document metadata. You need to upload the actual file to a storage service 
                    (like AWS S3, Azure Blob Storage, etc.) and provide the URL here.
                </p>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Upload</button>
                <a asp-action="Documents" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
