@model IEnumerable<Technoloway.Core.Entities.ProjectDocument>

@{
    ViewData["Title"] = "Project Documents";
    Layout = "_AdminLayout";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Documents for @(project != null ? project.Name : "Project")</h1>
    <div>
        <a asp-action="AddDocument" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-upload fa-sm text-white-50"></i> Upload Document
        </a>
        <a asp-action="Details" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Project
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Projects
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Project Documents</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No documents have been uploaded for this project yet.</p>
            </div>
            
            <div class="text-center mt-3">
                <a asp-action="AddDocument" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-success">
                    <i class="fas fa-upload"></i> Upload First Document
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Uploaded On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <a href="@item.FileUrl" target="_blank">
                                        @item.FileName
                                    </a>
                                </td>
                                <td>@item.FileType</td>
                                <td>@(item.FileSize / 1024) KB</td>
                                <td>@item.UploadedByName</td>
                                <td>@item.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="@item.FileUrl" target="_blank" class="btn btn-info btn-sm">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#<EMAIL>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="<EMAIL>" tabindex="-1" role="dialog" aria-labelledby="<EMAIL>" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="<EMAIL>">Confirm Delete</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to delete this document?</p>
                                                    <p><strong>File Name:</strong> @item.FileName</p>
                                                    <p><strong>Type:</strong> @item.FileType</p>
                                                    <p><strong>Size:</strong> @(item.FileSize / 1024) KB</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <form asp-action="DeleteDocument" asp-route-id="@item.Id" method="post">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "order": [[4, "desc"]] // Sort by uploaded date (column 4) in descending order
            });
        });
    </script>
}
