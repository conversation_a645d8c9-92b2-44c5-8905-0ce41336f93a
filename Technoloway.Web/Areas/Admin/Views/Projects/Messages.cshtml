@model IEnumerable<Technoloway.Core.Entities.Message>

@{
    ViewData["Title"] = "Project Messages";
    Layout = "_AdminLayout";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Messages for @(project != null ? project.Name : "Project")</h1>
    <div>
        <a asp-action="AddMessage" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-comment fa-sm text-white-50"></i> Add Message
        </a>
        <a asp-action="Details" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Project
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Projects
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Project Messages</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No messages have been added to this project yet.</p>
            </div>
            
            <div class="text-center mt-3">
                <a asp-action="AddMessage" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-warning">
                    <i class="fas fa-comment"></i> Add First Message
                </a>
            </div>
        }
        else
        {
            <div class="mb-4">
                <a asp-action="AddMessage" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-warning">
                    <i class="fas fa-comment"></i> Add New Message
                </a>
            </div>
            
            <div class="messages-container">
                @foreach (var message in Model.OrderByDescending(m => m.CreatedAt))
                {
                    <div class="card mb-3 @(message.SenderRole == "Admin" ? "border-primary" : "border-success")">
                        <div class="card-header @(message.SenderRole == "Admin" ? "bg-primary" : "bg-success") text-white d-flex justify-content-between">
                            <div>
                                <strong>@message.SenderName</strong>
                                <span class="badge @(message.SenderRole == "Admin" ? "bg-info" : "bg-warning") ml-2">@message.SenderRole</span>
                            </div>
                            <div>
                                @message.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@message.Content</p>
                            
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    @if (message.IsRead)
                                    {
                                        <small class="text-muted">
                                            <i class="fas fa-check-double"></i> Read @(message.ReadAt.HasValue ? message.ReadAt.Value.ToString("yyyy-MM-dd HH:mm") : "")
                                        </small>
                                    }
                                    else
                                    {
                                        <small class="text-muted">
                                            <i class="fas fa-check"></i> Unread
                                        </small>
                                    }
                                </div>
                                <div>
                                    <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#<EMAIL>">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delete Modal -->
                    <div class="modal fade" id="<EMAIL>" tabindex="-1" role="dialog" aria-labelledby="<EMAIL>" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="<EMAIL>">Confirm Delete</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this message?</p>
                                    <p><strong>From:</strong> @message.SenderName (@message.SenderRole)</p>
                                    <p><strong>Date:</strong> @message.CreatedAt.ToString("yyyy-MM-dd HH:mm")</p>
                                    <p><strong>Message:</strong> @message.Content</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                    <form asp-action="DeleteMessage" asp-route-id="@message.Id" method="post">
                                        <button type="submit" class="btn btn-danger">Delete</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>

@section Styles {
    <style>
        .messages-container {
            max-height: 800px;
            overflow-y: auto;
        }
    </style>
}
