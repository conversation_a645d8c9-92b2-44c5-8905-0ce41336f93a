@model IEnumerable<Technoloway.Core.Entities.Project>

@{
    ViewData["Title"] = "Projects";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Projects Management</h1>
            <p class="text-muted mb-0">Manage your project portfolio and client deliverables</p>
        </div>
        <div class="d-flex gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterProjects('all')">
                    <i class="fas fa-list me-1"></i>All
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="filterProjects('in-progress')">
                    <i class="fas fa-clock me-1"></i>In Progress
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="filterProjects('completed')">
                    <i class="fas fa-check me-1"></i>Completed
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterProjects('featured')">
                    <i class="fas fa-star me-1"></i>Featured
                </button>
            </div>
            <button class="btn-modern-admin secondary" onclick="showProjectTimeline()">
                <i class="fas fa-calendar"></i>
                Timeline
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Project
            </a>
        </div>
    </div>

    <!-- Projects Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Projects</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">In Progress</p>
                        <h3 class="admin-stat-number">@Model.Count(p => p.CompletionDate > DateTime.UtcNow)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Completed</p>
                        <h3 class="admin-stat-number">@Model.Count(p => p.CompletionDate <= DateTime.UtcNow)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Featured</p>
                        <h3 class="admin-stat-number">@Model.Count(p => p.IsFeatured)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Projects</h5>
                <p class="text-muted mb-0 small">@Model.Count() total projects</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Project</th>
                                <th class="border-0 fw-semibold">Client</th>
                                <th class="border-0 fw-semibold">Service</th>
                                <th class="border-0 fw-semibold">Completion</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Featured</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                var status = item.CompletionDate > DateTime.UtcNow ? "in-progress" : "completed";
                                <tr class="project-row" data-status="@status" data-featured="@item.IsFeatured.ToString().ToLower()"
                                    data-service="@(item.Service?.Name ?? "")" data-client="@(item.Client?.CompanyName ?? item.ClientName)">
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                                {
                                                    <img src="@item.ImageUrl" alt="@item.Name" style="width: 32px; height: 32px; object-fit: cover; border-radius: 50%;" />
                                                }
                                                else
                                                {
                                                    <i class="fas fa-project-diagram text-white"></i>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Name</div>
                                                <div class="text-muted small">@item.Description?.Substring(0, Math.Min(item.Description?.Length ?? 0, 50))...</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (item.Client != null)
                                        {
                                            <a asp-controller="Clients" asp-action="Details" asp-route-id="@item.ClientId"
                                               class="text-decoration-none fw-semibold">
                                                @item.Client.CompanyName
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">@item.ClientName</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (item.Service != null)
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-cog me-1"></i>@item.Service.Name
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">No service</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <span class="text-muted">@item.CompletionDate.ToString("MMM dd, yyyy")</span>
                                    </td>
                                    <td class="border-0">
                                        @if (item.CompletionDate > DateTime.UtcNow)
                                        {
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>In Progress
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Completed
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (item.IsFeatured)
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        }
                                        else
                                        {
                                            <button class="btn btn-outline-secondary btn-sm" title="Mark as Featured"
                                                    onclick="toggleFeatured(@item.Id, true)">
                                                <i class="fas fa-star"></i>
                                            </button>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Project">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Documents" asp-route-id="@item.Id"
                                               class="btn btn-outline-success btn-sm" title="Documents">
                                                <i class="fas fa-file"></i>
                                            </a>
                                            <a asp-action="Messages" asp-route-id="@item.Id"
                                               class="btn btn-outline-warning btn-sm" title="Messages">
                                                <i class="fas fa-comments"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Project"
                                               onclick="return confirm('Are you sure you want to delete this project?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-project-diagram text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No projects found</h5>
                    <p class="text-muted">Create your first project to get started.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Project
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "desc"]], // Sort by completion date (column 3) in descending order
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5, 6] } // Disable sorting for Project, Featured, and Actions columns
                ]
            });
        });

        // Function to toggle featured status
        function toggleFeatured(projectId, isFeatured) {
            if (confirm(`Are you sure you want to ${isFeatured ? 'feature' : 'unfeature'} this project?`)) {
                // Here you would make an AJAX call to update the featured status
                // For now, we'll just reload the page
                // $.post('/Admin/Projects/ToggleFeatured', { id: projectId, isFeatured: isFeatured })
                //     .done(function() {
                //         location.reload();
                //     });

                // Temporary: Just show an alert
                alert(`Project ${isFeatured ? 'featured' : 'unfeatured'} successfully! (This would normally update via AJAX)`);
            }
        }

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add progress indicators for project status
        function updateProjectProgress() {
            const totalProjects = @Model.Count();
            const inProgress = @Model.Count(p => p.CompletionDate > DateTime.UtcNow);
            const completed = @Model.Count(p => p.CompletionDate <= DateTime.UtcNow);

            if (totalProjects > 0) {
                const completionRate = Math.round((completed / totalProjects) * 100);
                console.log(`Project completion rate: ${completionRate}%`);
            }
        }

        updateProjectProgress();

        // Project filter functionality
        function filterProjects(status) {
            const rows = document.querySelectorAll('.project-row');
            const buttons = document.querySelectorAll('.btn-group button');

            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Filter rows
            rows.forEach(row => {
                let show = false;

                switch(status) {
                    case 'all':
                        show = true;
                        break;
                    case 'in-progress':
                        show = row.dataset.status === 'in-progress';
                        break;
                    case 'completed':
                        show = row.dataset.status === 'completed';
                        break;
                    case 'featured':
                        show = row.dataset.featured === 'true';
                        break;
                }

                row.style.display = show ? '' : 'none';
            });

            // Update stats display
            updateProjectFilteredStats(status);
        }

        // Update statistics based on filtered results
        function updateProjectFilteredStats(status) {
            const visibleRows = document.querySelectorAll('.project-row[style=""], .project-row:not([style])');
            const totalVisible = visibleRows.length;

            console.log(`Showing ${totalVisible} projects for filter: ${status}`);
        }

        // Project timeline functionality
        function showProjectTimeline() {
            alert('Project Timeline view would be implemented here. This could show a Gantt chart or timeline view of all projects.');
        }

        // Make functions globally available
        window.filterProjects = filterProjects;
        window.showProjectTimeline = showProjectTimeline;
    </script>
}
