@model Technoloway.Core.Entities.Project

@{
    ViewData["Title"] = "Delete Project";
    Layout = "_AdminLayout";
    var documentCount = ViewBag.DocumentCount as int? ?? 0;
    var messageCount = ViewBag.MessageCount as int? ?? 0;
    var invoiceCount = ViewBag.InvoiceCount as int? ?? 0;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Project</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5>Are you sure you want to delete this project?</h5>
            <p>This action will mark the project as deleted but will not permanently remove it from the database.</p>
            
            @if (documentCount > 0 || messageCount > 0 || invoiceCount > 0)
            {
                <div class="mt-3">
                    <p><strong>Warning:</strong> This project has associated data that will be affected:</p>
                    <ul>
                        @if (documentCount > 0)
                        {
                            <li>@documentCount documents</li>
                        }
                        @if (messageCount > 0)
                        {
                            <li>@messageCount messages</li>
                        }
                        @if (invoiceCount > 0)
                        {
                            <li>@invoiceCount invoices</li>
                        }
                    </ul>
                </div>
            }
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h4>@Model.Name</h4>
                    <p>
                        <strong>Client:</strong> 
                        @if (Model.Client != null)
                        {
                            @Model.Client.CompanyName
                        }
                        else
                        {
                            @Model.ClientName
                        }
                        <br />
                        <strong>Service:</strong> 
                        @if (Model.Service != null)
                        {
                            @Model.Service.Name
                        }
                        else
                        {
                            <span>-</span>
                        }
                        <br />
                        <strong>Completion Date:</strong> @Model.CompletionDate.ToString("yyyy-MM-dd")
                    </p>
                </div>
            </div>
            
            <div class="col-md-6">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <div class="text-center mb-4">
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-thumbnail" style="max-height: 150px;" />
                    </div>
                }
                
                <p>
                    <strong>Description:</strong><br />
                    @Model.Description
                </p>
            </div>
        </div>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Delete</button>
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
