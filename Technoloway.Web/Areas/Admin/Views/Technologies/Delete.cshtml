@model Technoloway.Core.Entities.Technology

@{
    ViewData["Title"] = "Delete Technology";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Technology</h1>
    <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4 border-danger">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Warning!</strong> Are you sure you want to delete this technology? This action cannot be undone.
        </div>
        
        <div class="row">
            <div class="col-md-3 text-center">
                @if (!string.IsNullOrEmpty(Model.IconUrl))
                {
                    <img src="@Model.IconUrl" alt="@Model.Name" class="img-fluid mb-3" style="max-height: 80px; max-width: 80px; object-fit: contain;" />
                }
                else
                {
                    <div class="text-muted mb-3">
                        <i class="fas fa-microchip fa-3x"></i>
                    </div>
                }
            </div>
            <div class="col-md-9">
                <h4 class="mb-3">@Model.Name</h4>
                <p class="text-muted mb-3">@Model.Description</p>
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Display Order:</strong></td>
                        <td>@Model.DisplayOrder</td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>@Model.CreatedAt.ToString("MMM dd, yyyy")</td>
                    </tr>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td>@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</td>
                        </tr>
                    }
                </table>
            </div>
        </div>
        
        <hr>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <div class="d-flex justify-content-between">
                <div>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete Technology
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Impact Information</h6>
    </div>
    <div class="card-body">
        <p class="text-muted mb-0">
            <i class="fas fa-info-circle"></i>
            Deleting this technology will remove it from the system. If this technology is associated with any projects, 
            those associations will also be removed. This is a soft delete operation, so the data can be recovered if needed.
        </p>
    </div>
</div>
