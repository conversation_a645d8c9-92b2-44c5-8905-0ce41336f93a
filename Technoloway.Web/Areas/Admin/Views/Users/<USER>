@model Technoloway.Web.Areas.Admin.Models.CreateUserViewModel

@{
    ViewData["Title"] = "Create New User";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Create New User</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-plus me-2"></i>New User Information
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <div class="form-group mb-3">
                        <label asp-for="Email" class="form-label">Email Address *</label>
                        <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                        <small class="form-text text-muted">This will also be used as the username.</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Password" class="form-label">Password *</label>
                                <input asp-for="Password" class="form-control" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">Confirm Password *</label>
                                <input asp-for="ConfirmPassword" class="form-control" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="EmailConfirmed" class="form-check-input" type="checkbox" />
                        <label asp-for="EmailConfirmed" class="form-check-label">
                            Email Confirmed
                        </label>
                        <small class="form-text text-muted d-block">
                            Check this if the user's email should be marked as confirmed immediately.
                        </small>
                        <span asp-validation-for="EmailConfirmed" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Assign Roles</label>
                        <div class="row">
                            @foreach (var role in ViewBag.Roles as List<Microsoft.AspNetCore.Identity.IdentityRole> ?? new List<Microsoft.AspNetCore.Identity.IdentityRole>())
                            {
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input type="checkbox" name="SelectedRoles" value="@role.Name" class="form-check-input" id="<EMAIL>" />
                                        <label class="form-check-label" for="<EMAIL>">
                                            @role.Name
                                        </label>
                                    </div>
                                </div>
                            }
                        </div>
                        <small class="form-text text-muted">Select the roles to assign to this user.</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create User
                        </button>
                        <a asp-action="Index" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>Password Requirements
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>At least 6 characters long</li>
                    <li>Maximum 100 characters</li>
                    <li>Must contain at least one uppercase letter</li>
                    <li>Must contain at least one lowercase letter</li>
                    <li>Must contain at least one digit</li>
                    <li>Must contain at least one special character</li>
                </ul>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-shield-alt me-2"></i>Role Descriptions
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li><strong>SuperAdmin:</strong> Full system access</li>
                    <li><strong>Admin:</strong> Administrative access</li>
                    <li><strong>Client:</strong> Client portal access</li>
                </ul>
                <div class="alert alert-warning mt-2">
                    <small><i class="fas fa-exclamation-triangle me-1"></i>
                    Be careful when assigning SuperAdmin role.</small>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
