@model Technoloway.Web.Areas.Admin.Models.UserDetailsViewModel

@{
    ViewData["Title"] = "User Details";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">User Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary shadow-sm me-2">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit User
        </a>
        <a asp-action="Index" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user me-2"></i>User Information: @Model.Email
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">User ID:</th>
                            <td><code>@Model.Id</code></td>
                        </tr>
                        <tr>
                            <th>Username:</th>
                            <td>@Model.UserName</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>
                                @Model.Email
                                @if (Model.EmailConfirmed)
                                {
                                    <span class="badge bg-success ms-2">
                                        <i class="fas fa-check me-1"></i>Confirmed
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-warning text-dark ms-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Unconfirmed
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Phone Number:</th>
                            <td>
                                @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                                {
                                    @Model.PhoneNumber
                                    @if (Model.PhoneNumberConfirmed)
                                    {
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check me-1"></i>Confirmed
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning text-dark ms-2">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Unconfirmed
                                        </span>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">Not provided</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Account Status:</th>
                            <td>
                                @if (Model.IsLockedOut)
                                {
                                    <span class="badge bg-danger">
                                        <i class="fas fa-lock me-1"></i>Locked Out
                                    </span>
                                    <small class="text-muted d-block">Until: @Model.LockoutEnd?.ToString("MMM dd, yyyy 'at' h:mm tt")</small>
                                }
                                else
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-unlock me-1"></i>Active
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Two Factor Authentication:</th>
                            <td>
                                @if (Model.TwoFactorEnabled)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-shield-alt me-1"></i>Enabled
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-shield me-1"></i>Disabled
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Lockout Enabled:</th>
                            <td>
                                @if (Model.LockoutEnabled)
                                {
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Yes
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>No
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Failed Login Attempts:</th>
                            <td>
                                @if (Model.AccessFailedCount > 0)
                                {
                                    <span class="badge bg-danger">@Model.AccessFailedCount</span>
                                }
                                else
                                {
                                    <span class="badge bg-success">0</span>
                                }
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        @if (Model.Claims.Any())
        {
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-key me-2"></i>User Claims
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead>
                                <tr>
                                    <th>Claim Type</th>
                                    <th>Claim Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var claim in Model.Claims)
                                {
                                    <tr>
                                        <td><code>@claim.Type</code></td>
                                        <td>@claim.Value</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    </div>
    
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users-cog me-2"></i>Assigned Roles
                </h6>
            </div>
            <div class="card-body">
                @if (Model.Roles.Any())
                {
                    @foreach (var role in Model.Roles)
                    {
                        <span class="badge bg-primary me-1 mb-2 p-2">
                            <i class="fas fa-user-tag me-1"></i>@role
                        </span>
                    }
                }
                else
                {
                    <p class="text-muted">No roles assigned to this user.</p>
                }
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit User
                    </a>
                    <a asp-action="ResetPassword" asp-route-id="@Model.Id" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </a>
                    
                    @if (Model.IsLockedOut)
                    {
                        <form asp-action="UnlockUser" method="post">
                            <input type="hidden" name="id" value="@Model.Id" />
                            <button type="submit" class="btn btn-success w-100" 
                                    onclick="return confirm('Are you sure you want to unlock this user?')">
                                <i class="fas fa-unlock me-2"></i>Unlock User
                            </button>
                        </form>
                    }
                    else
                    {
                        <form asp-action="LockUser" method="post">
                            <input type="hidden" name="id" value="@Model.Id" />
                            <button type="submit" class="btn btn-warning w-100" 
                                    onclick="return confirm('Are you sure you want to lock this user?')"
                                    @(Model.Email == User.Identity!.Name ? "disabled" : "")>
                                <i class="fas fa-lock me-2"></i>Lock User
                            </button>
                        </form>
                    }
                    
                    @if (Model.Email != User.Identity!.Name)
                    {
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </a>
                    }
                </div>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-2"></i>Security Information
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li><strong>Account Security:</strong> @(Model.TwoFactorEnabled ? "Enhanced with 2FA" : "Basic password only")</li>
                    <li><strong>Lockout Policy:</strong> @(Model.LockoutEnabled ? "Enabled" : "Disabled")</li>
                    <li><strong>Email Verification:</strong> @(Model.EmailConfirmed ? "Verified" : "Pending")</li>
                    @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                    {
                        <li><strong>Phone Verification:</strong> @(Model.PhoneNumberConfirmed ? "Verified" : "Pending")</li>
                    }
                </ul>
            </div>
        </div>
    </div>
</div>
