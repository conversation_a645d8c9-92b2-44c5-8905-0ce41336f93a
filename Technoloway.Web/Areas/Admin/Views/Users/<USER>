@model IEnumerable<Technoloway.Web.Areas.Admin.Models.UserViewModel>

@{
    ViewData["Title"] = "Users & Roles Management";
}

<div class="container-fluid p-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Users & Roles Management</h1>
            <p class="text-muted mb-0">Manage system users, roles, and permissions</p>
        </div>
        <div class="d-flex gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="filterByRole('all')">
                    <i class="fas fa-list me-1"></i>All Users
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="filterByRole('SuperAdmin')">
                    <i class="fas fa-crown me-1"></i>SuperAdmin
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="filterByRole('Admin')">
                    <i class="fas fa-user-shield me-1"></i>Admin
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="filterByRole('Client')">
                    <i class="fas fa-user me-1"></i>Client
                </button>
            </div>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Create User
            </a>
        </div>
    </div>

    <!-- User Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Users</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-unlock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Users</p>
                        <h3 class="admin-stat-number">@Model.Count(u => !u.IsLockedOut)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Locked Users</p>
                        <h3 class="admin-stat-number">@Model.Count(u => u.IsLockedOut)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Confirmed</p>
                        <h3 class="admin-stat-number">@Model.Count(u => u.EmailConfirmed)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">System Users</h5>
                <p class="text-muted mb-0 small">@Model.Count() registered users in the system</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="dataTable">
                    <thead>
                        <tr>
                            <th class="border-0 fw-semibold">User</th>
                            <th class="border-0 fw-semibold">Roles</th>
                            <th class="border-0 fw-semibold">Status</th>
                            <th class="border-0 fw-semibold">Security</th>
                            <th class="border-0 fw-semibold">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model)
                        {
                            <tr class="user-row" data-roles="@string.Join(",", user.Roles)" data-status="@(user.IsLockedOut ? "locked" : "active")">
                                <td class="border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="admin-stat-icon me-3" style="width: 50px; height: 50px;">
                                            <i class="fas @(user.Roles.Contains("SuperAdmin") ? "fa-crown" :
                                                           user.Roles.Contains("Admin") ? "fa-user-shield" :
                                                           "fa-user")"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold text-gray-800">
                                                @user.Email
                                                @if (user.Email == User.Identity!.Name)
                                                {
                                                    <span class="badge bg-info text-white ms-2">
                                                        <i class="fas fa-user-circle me-1"></i>You
                                                    </span>
                                                }
                                            </div>
                                            <div class="text-muted small">
                                                <i class="fas fa-at me-1"></i>@user.UserName
                                            </div>
                                            <div class="text-muted small">
                                                @if (user.EmailConfirmed)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>Email Confirmed
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>Email Unconfirmed
                                                    </span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="border-0">
                                    <div class="d-flex flex-wrap gap-1">
                                        @if (user.Roles.Any())
                                        {
                                            @foreach (var role in user.Roles)
                                            {
                                                <span class="badge @(role switch {
                                                    "SuperAdmin" => "bg-danger",
                                                    "Admin" => "bg-primary",
                                                    "Client" => "bg-success",
                                                    _ => "bg-secondary"
                                                })">
                                                    <i class="fas @(role switch {
                                                        "SuperAdmin" => "fa-crown",
                                                        "Admin" => "fa-user-shield",
                                                        "Client" => "fa-user",
                                                        _ => "fa-tag"
                                                    }) me-1"></i>@role
                                                </span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-user-slash me-1"></i>No roles assigned
                                            </span>
                                        }
                                    </div>
                                </td>
                                <td class="border-0">
                                    <div class="d-flex flex-column gap-1">
                                        @if (user.IsLockedOut)
                                        {
                                            <span class="badge bg-danger">
                                                <i class="fas fa-lock me-1"></i>Locked Out
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-unlock me-1"></i>Active
                                            </span>
                                        }
                                    </div>
                                </td>
                                <td class="border-0">
                                    <div class="d-flex flex-column gap-1">
                                        @if (user.AccessFailedCount > 0)
                                        {
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                @user.AccessFailedCount Failed Attempts
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-shield-alt me-1"></i>No Failed Attempts
                                            </span>
                                        }
                                    </div>
                                </td>
                                <td class="border-0">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <a asp-action="Details" asp-route-id="@user.Id"
                                           class="btn btn-outline-info btn-sm" title="View User Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@user.Id"
                                           class="btn btn-outline-primary btn-sm" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="ResetPassword" asp-route-id="@user.Id"
                                           class="btn btn-outline-secondary btn-sm" title="Reset Password">
                                            <i class="fas fa-key"></i>
                                        </a>

                                        @if (user.IsLockedOut)
                                        {
                                            <form asp-action="UnlockUser" method="post" style="display: inline;">
                                                <input type="hidden" name="id" value="@user.Id" />
                                                <button type="submit" class="btn btn-outline-success btn-sm"
                                                        title="Unlock User"
                                                        onclick="return confirm('Are you sure you want to unlock this user?')">
                                                    <i class="fas fa-unlock"></i>
                                                </button>
                                            </form>
                                        }
                                        else
                                        {
                                            <form asp-action="LockUser" method="post" style="display: inline;">
                                                <input type="hidden" name="id" value="@user.Id" />
                                                <button type="submit" class="btn btn-outline-warning btn-sm"
                                                        title="Lock User"
                                                        onclick="return confirm('Are you sure you want to lock this user?')"
                                                        @(user.Email == User.Identity!.Name ? "disabled" : "")>
                                                    <i class="fas fa-lock"></i>
                                                </button>
                                            </form>
                                        }

                                        @if (user.Email != User.Identity!.Name)
                                        {
                                            <a asp-action="Delete" asp-route-id="@user.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete User"
                                               onclick="return confirm('Are you sure you want to delete this user?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Information Cards -->
    <div class="row g-4 mt-4">
        <div class="col-md-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h6 class="mb-0 fw-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>User Management Tips
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="d-flex flex-column gap-3">
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon info me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div>
                                <strong>Email Confirmation:</strong>
                                <p class="text-muted mb-0 small">Users with unconfirmed emails may have limited access to certain features.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon success me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-user-tag"></i>
                            </div>
                            <div>
                                <strong>Role Assignment:</strong>
                                <p class="text-muted mb-0 small">Assign appropriate roles to control user permissions and access levels.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon warning me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div>
                                <strong>User Lockout:</strong>
                                <p class="text-muted mb-0 small">Lock users to temporarily disable their access without deleting their account.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-key"></i>
                            </div>
                            <div>
                                <strong>Password Reset:</strong>
                                <p class="text-muted mb-0 small">Reset passwords for users who forgot their credentials or need security updates.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h6 class="mb-0 fw-bold text-warning">
                        <i class="fas fa-shield-alt me-2"></i>Security & Role Information
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="d-flex flex-column gap-3">
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: #dc3545;">
                                <i class="fas fa-crown text-white"></i>
                            </div>
                            <div>
                                <strong>SuperAdmin:</strong>
                                <p class="text-muted mb-0 small">Has full system access and cannot be locked or deleted by other users.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div>
                                <strong>Admin:</strong>
                                <p class="text-muted mb-0 small">Can manage most system features including users, content, and settings.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon success me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <strong>Client:</strong>
                                <p class="text-muted mb-0 small">Limited access to client portal features and their own account information.</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start">
                            <div class="admin-stat-icon warning me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div>
                                <strong>Self-Protection:</strong>
                                <p class="text-muted mb-0 small">You cannot delete or lock your own account for security reasons.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 25,
                "order": [[0, "asc"]],
                "responsive": true,
                "columnDefs": [
                    { "orderable": false, "targets": [4] } // Disable sorting for Actions column
                ]
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Enhanced filter function
        function filterByRole(role) {
            const rows = document.querySelectorAll('.user-row');

            rows.forEach(row => {
                const userRoles = row.dataset.roles.toLowerCase();

                if (role === 'all') {
                    row.style.display = '';
                } else {
                    row.style.display = userRoles.includes(role.toLowerCase()) ? '' : 'none';
                }
            });

            // Update button states
            document.querySelectorAll('.btn-group button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Add user statistics calculations
        function updateUserStats() {
            const totalUsers = @Model.Count();
            const activeUsers = @Model.Count(u => !u.IsLockedOut);
            const lockedUsers = @Model.Count(u => u.IsLockedOut);
            const confirmedUsers = @Model.Count(u => u.EmailConfirmed);

            if (totalUsers > 0) {
                const activePercentage = Math.round((activeUsers / totalUsers) * 100);
                const confirmedPercentage = Math.round((confirmedUsers / totalUsers) * 100);
                console.log(`Active users: ${activePercentage}%`);
                console.log(`Email confirmed: ${confirmedPercentage}%`);
                console.log(`Locked users: ${lockedUsers}`);
            }
        }

        updateUserStats();

        // Add user icon hover effects
        $('.admin-stat-icon').hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );

        // Add badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const userEmail = $(this).closest('tr').find('.fw-semibold').text().trim();
            if (!confirm(`Are you sure you want to delete the user "${userEmail}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add crown icon special effects for SuperAdmin
        $('.fa-crown').parent().hover(
            function() {
                $(this).css('transform', 'rotate(15deg) scale(1.2)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );

        // Add role-based row highlighting
        $('.user-row').each(function() {
            const roles = $(this).data('roles');
            if (roles && roles.includes('SuperAdmin')) {
                $(this).addClass('table-danger');
            } else if (roles && roles.includes('Admin')) {
                $(this).addClass('table-primary');
            }
        });

        // Add status-based visual indicators
        $('.user-row').each(function() {
            const status = $(this).data('status');
            if (status === 'locked') {
                $(this).find('td').first().prepend('<div class="position-absolute top-0 start-0 bg-danger" style="width: 4px; height: 100%;"></div>');
            }
        });
    </script>
}
