@model IEnumerable<Technoloway.Core.Entities.Invoice>

@{
    ViewData["Title"] = "Client Invoices";
    Layout = "_AdminLayout";
    var client = ViewBag.Client as Technoloway.Core.Entities.Client;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Invoices for @(client != null ? client.CompanyName : "Client")</h1>
    <div>
        <a asp-area="Admin" asp-controller="Invoices" asp-action="Create" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Invoice
        </a>
        <a asp-action="Details" asp-route-id="@(client != null ? client.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Client
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Clients
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">All Invoices</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No invoices have been created for this client yet.</p>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.InvoiceNumber</td>
                                <td>@item.IssueDate.ToString("yyyy-MM-dd")</td>
                                <td>@item.DueDate.ToString("yyyy-MM-dd")</td>
                                <td>@item.TotalAmount.ToString("C")</td>
                                <td>
                                    @switch (item.Status)
                                    {
                                        case "Pending":
                                            <span class="badge bg-warning">Pending</span>
                                            break;
                                        case "Paid":
                                            <span class="badge bg-success">Paid</span>
                                            break;
                                        case "Overdue":
                                            <span class="badge bg-danger">Overdue</span>
                                            break;
                                        case "Cancelled":
                                            <span class="badge bg-secondary">Cancelled</span>
                                            break;
                                        default:
                                            <span class="badge bg-info">@item.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a asp-area="Admin" asp-controller="Invoices" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Invoices" asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Invoices" asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });
    </script>
}
