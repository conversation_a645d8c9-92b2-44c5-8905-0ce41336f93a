@model Technoloway.Web.Areas.Admin.ViewModels.ClientReportsViewModel

@{
    ViewData["Title"] = "Client Reports";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Client Analytics & Reports</h1>
            <p class="text-muted mb-0">Comprehensive insights into your client relationships and business performance</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary" onclick="exportReports()">
                <i class="fas fa-download"></i>
                Export PDF
            </button>
            <button class="btn-modern-admin secondary" onclick="refreshReports()">
                <i class="fas fa-sync"></i>
                Refresh
            </button>
            <a asp-action="Index" class="btn-modern-admin primary">
                <i class="fas fa-arrow-left"></i>
                Back to Clients
            </a>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Clients</p>
                        <h3 class="admin-stat-number mb-0">@Model.TotalClients</h3>
                        <small class="@(Model.MonthOverMonthGrowth >= 0 ? "text-success" : "text-danger")">
                            @(Model.MonthOverMonthGrowth >= 0 ? "+" : "")@Model.MonthOverMonthGrowth.ToString("F1")% MoM growth
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Clients</p>
                        <h3 class="admin-stat-number mb-0">@Model.ActiveClients</h3>
                        <small class="text-success">@Model.ClientRetentionRate.ToString("F1")% retention rate</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Revenue</p>
                        <h3 class="admin-stat-number mb-0">@Model.TotalClientRevenue.ToString("C0")</h3>
                        <small class="text-warning">@Model.AverageRevenuePerClient.ToString("C0") avg per client</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Projects</p>
                        <h3 class="admin-stat-number mb-0">@Model.TotalProjects</h3>
                        <small class="text-info">@Model.AverageProjectsPerClient.ToString("F1") avg per client</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Engagement Metrics Row -->
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-3">
                    <h6 class="mb-0 fw-bold text-gray-800">Project Engagement</h6>
                </div>
                <div class="card-body text-center p-4">
                    <div class="progress-circle" data-percentage="@Model.ProjectEngagementRate">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage">@Model.ProjectEngagementRate.ToString("F0")%</span>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">@Model.ClientsWithProjects of @Model.TotalClients clients have projects</p>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-3">
                    <h6 class="mb-0 fw-bold text-gray-800">Invoice Engagement</h6>
                </div>
                <div class="card-body text-center p-4">
                    <div class="progress-circle" data-percentage="@Model.InvoiceEngagementRate">
                        <div class="progress-circle-inner">
                            <span class="progress-percentage">@Model.InvoiceEngagementRate.ToString("F0")%</span>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">@Model.ClientsWithInvoices of @Model.TotalClients clients have invoices</p>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-3">
                    <h6 class="mb-0 fw-bold text-gray-800">Outstanding Balance</h6>
                </div>
                <div class="card-body text-center p-4">
                    <div class="display-4 fw-bold text-warning mb-2">@Model.TotalOutstandingAmount.ToString("C0")</div>
                    <p class="text-muted mb-0">Total unpaid invoices</p>
                    <small class="text-muted">Requires attention</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Monthly Client Growth Chart -->
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                    <div>
                        <h5 class="mb-0 fw-bold text-gray-800">Monthly Client Acquisition</h5>
                        <p class="text-muted mb-0 small">New clients acquired over the last 12 months</p>
                    </div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm active">12M</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">6M</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">3M</button>
                    </div>
                </div>
                <div class="card-body p-4">
                    <canvas id="monthlyClientChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Country Distribution -->
        <div class="col-lg-4">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold text-gray-800">Geographic Distribution</h5>
                    <p class="text-muted mb-0 small">Clients by country</p>
                </div>
                <div class="card-body p-4">
                    <canvas id="countryChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Clients Tables Row -->
    <div class="row g-4 mb-4">
        <!-- Top Clients by Revenue -->
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold text-gray-800">Top Clients by Revenue</h5>
                    <p class="text-muted mb-0 small">Highest value clients</p>
                </div>
                <div class="card-body p-0">
                    @if (Model.TopClientsByRevenue.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Client</th>
                                        <th class="border-0">Revenue</th>
                                        <th class="border-0">Projects</th>
                                        <th class="border-0">Invoices</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var client in Model.TopClientsByRevenue.Take(10))
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="fw-semibold">@client.ClientName</div>
                                            </td>
                                            <td class="border-0">
                                                <span class="fw-bold text-success">@client.TotalRevenue.ToString("C0")</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-primary">@client.ProjectCount</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-info">@client.InvoiceCount</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar text-muted mb-3" style="font-size: 2rem;"></i>
                            <p class="text-muted">No revenue data available</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Top Clients by Projects -->
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold text-gray-800">Top Clients by Projects</h5>
                    <p class="text-muted mb-0 small">Most active project clients</p>
                </div>
                <div class="card-body p-0">
                    @if (Model.TopClientsByProjects.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Client</th>
                                        <th class="border-0">Projects</th>
                                        <th class="border-0">Completed</th>
                                        <th class="border-0">Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var client in Model.TopClientsByProjects.Take(10))
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="fw-semibold">@client.ClientName</div>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-primary">@client.ProjectCount</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-success">@client.CompletedProjects</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="fw-bold text-success">@client.TotalRevenue.ToString("C0")</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-project-diagram text-muted mb-3" style="font-size: 2rem;"></i>
                            <p class="text-muted">No project data available</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Clients -->
    <div class="row g-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold text-gray-800">Recent Client Activity</h5>
                    <p class="text-muted mb-0 small">Latest client acquisitions and updates</p>
                </div>
                <div class="card-body p-0">
                    @if (Model.RecentClients.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Company</th>
                                        <th class="border-0">Contact</th>
                                        <th class="border-0">Location</th>
                                        <th class="border-0">Added</th>
                                        <th class="border-0">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var client in Model.RecentClients)
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(client.LogoUrl))
                                                    {
                                                        <img src="@client.LogoUrl" alt="@client.CompanyName" class="rounded me-3" style="width: 32px; height: 32px; object-fit: cover;">
                                                    }
                                                    else
                                                    {
                                                        <div class="admin-stat-icon me-3" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                            <i class="fas fa-building"></i>
                                                        </div>
                                                    }
                                                    <div>
                                                        <div class="fw-semibold">@client.CompanyName</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="border-0">
                                                <div>@client.ContactName</div>
                                                <small class="text-muted">@client.ContactEmail</small>
                                            </td>
                                            <td class="border-0">
                                                @if (!string.IsNullOrEmpty(client.City) && !string.IsNullOrEmpty(client.Country))
                                                {
                                                    <span>@client.City, @client.Country</span>
                                                }
                                                else if (!string.IsNullOrEmpty(client.Country))
                                                {
                                                    <span>@client.Country</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not specified</span>
                                                }
                                            </td>
                                            <td class="border-0">
                                                <span class="text-muted">@client.CreatedAt.ToString("MMM dd, yyyy")</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">No recent client activity</h5>
                            <p class="text-muted">Client activity will appear here as it happens.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            initializeCharts();
            initializeProgressCircles();
        });

        function initializeCharts() {
            // Monthly Client Growth Chart
            const monthlyCtx = document.getElementById('monthlyClientChart').getContext('2d');
            const monthlyData = @Html.Raw(Json.Serialize(Model.MonthlyClientGrowth));

            new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: monthlyData.map(d => d.month),
                    datasets: [{
                        label: 'New Clients',
                        data: monthlyData.map(d => d.newClients),
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'Revenue',
                        data: monthlyData.map(d => d.totalRevenue),
                        borderColor: '#1cc88a',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'New Clients'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Revenue ($)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // Country Distribution Chart
            const countryCtx = document.getElementById('countryChart').getContext('2d');
            const countryData = @Html.Raw(Json.Serialize(Model.CountryDistribution));

            new Chart(countryCtx, {
                type: 'doughnut',
                data: {
                    labels: countryData.map(d => d.country),
                    datasets: [{
                        data: countryData.map(d => d.clientCount),
                        backgroundColor: [
                            '#4e73df',
                            '#1cc88a',
                            '#36b9cc',
                            '#f6c23e',
                            '#e74a3b',
                            '#858796',
                            '#5a5c69',
                            '#6f42c1'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initializeProgressCircles() {
            $('.progress-circle').each(function() {
                const percentage = $(this).data('percentage');
                const circumference = 2 * Math.PI * 45; // radius = 45
                const offset = circumference - (percentage / 100) * circumference;

                $(this).html(`
                    <svg width="120" height="120" class="progress-ring">
                        <circle cx="60" cy="60" r="45" stroke="#e3e6f0" stroke-width="8" fill="transparent"/>
                        <circle cx="60" cy="60" r="45" stroke="#4e73df" stroke-width="8" fill="transparent"
                                stroke-dasharray="${circumference}" stroke-dashoffset="${offset}"
                                stroke-linecap="round" transform="rotate(-90 60 60)"/>
                    </svg>
                    <div class="progress-circle-inner">
                        <span class="progress-percentage">${percentage.toFixed(0)}%</span>
                    </div>
                `);
            });
        }

        function exportReports() {
            // Implement PDF export functionality
            alert('PDF export functionality will be implemented soon!');
        }

        function refreshReports() {
            window.location.reload();
        }
    </script>
}

@section Styles {
    <style>
        .progress-circle {
            position: relative;
            display: inline-block;
        }

        .progress-circle-inner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4e73df;
        }

        .progress-ring {
            transition: stroke-dashoffset 0.5s ease-in-out;
        }



        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .card-header {
            border-bottom: 1px solid #e3e6f0 !important;
        }

        .btn-group .btn {
            border-color: #d1d3e2;
        }

        .btn-group .btn.active {
            background-color: #4e73df;
            border-color: #4e73df;
            color: white;
        }
    </style>
}
