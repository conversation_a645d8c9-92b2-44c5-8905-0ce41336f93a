@model IEnumerable<Technoloway.Core.Entities.AboutPage>

@{
    ViewData["Title"] = "About Pages";
    ViewData["PageTitle"] = "About Pages";
    ViewData["PageDescription"] = "Manage your website's about page content";
}

@section Styles {
    <style>
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .action-buttons .btn {
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
        }

        .table-responsive {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
            padding: 1rem 0.75rem;
        }

        .table td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.35);
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-2">
                    <i class="fas fa-info-circle me-2"></i>About Pages
                </h1>
                <p class="mb-0 opacity-75">Manage your website's about page content and sections</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a asp-action="Create" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>Create New About Page
                </a>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- About Pages Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Last Modified</th>
                                <th>Modified By</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var aboutPage in Model)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-info-circle text-primary"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold">@aboutPage.Title</h6>
                                                @if (!string.IsNullOrEmpty(aboutPage.MetaDescription))
                                                {
                                                    <small class="text-muted">@aboutPage.MetaDescription</small>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if (aboutPage.IsActive)
                                        {
                                            <span class="status-badge status-active">
                                                <i class="fas fa-check-circle me-1"></i>Active
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="status-badge status-inactive">
                                                <i class="fas fa-times-circle me-1"></i>Inactive
                                            </span>
                                        }
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium">@aboutPage.LastModified.ToString("MMM dd, yyyy")</div>
                                            <small class="text-muted">@aboutPage.LastModified.ToString("hh:mm tt")</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            @(aboutPage.ModifiedBy ?? "System")
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="action-buttons">
                                            <a asp-action="Details" asp-route-id="@aboutPage.Id" 
                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@aboutPage.Id" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(@aboutPage.Id, '@aboutPage.Title')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-info-circle fa-4x text-muted opacity-50"></i>
                    </div>
                    <h4 class="text-muted mb-3">No About Pages Found</h4>
                    <p class="text-muted mb-4">Get started by creating your first about page to manage your website's about content.</p>
                    <a asp-action="Create" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Create Your First About Page
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the about page "<span id="deleteItemName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
