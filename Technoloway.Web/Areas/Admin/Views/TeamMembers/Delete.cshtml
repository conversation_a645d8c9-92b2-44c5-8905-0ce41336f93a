@model Technoloway.Core.Entities.TeamMember

@{
    ViewData["Title"] = "Delete Team Member";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Team Member</h1>
    <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4 border-danger">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Warning!</strong> Are you sure you want to delete this team member? This action cannot be undone.
        </div>
        
        <div class="row">
            <div class="col-md-3 text-center">
                @if (!string.IsNullOrEmpty(Model.PhotoUrl))
                {
                    <img src="@Model.PhotoUrl" alt="@Model.Name" class="img-fluid rounded-circle mb-3" style="max-height: 120px; max-width: 120px; object-fit: cover;" />
                }
                else
                {
                    <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto mb-3" style="height: 120px; width: 120px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                }
            </div>
            <div class="col-md-9">
                <h4 class="mb-2">@Model.Name</h4>
                <h6 class="text-muted mb-3">@Model.Position</h6>
                
                @if (!string.IsNullOrEmpty(Model.Bio))
                {
                    <p class="mb-3">@Model.Bio</p>
                }
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>
                            @if (!string.IsNullOrEmpty(Model.Email))
                            {
                                @Model.Email
                            }
                            else
                            {
                                <span class="text-muted">Not provided</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Display Order:</strong></td>
                        <td>@Model.DisplayOrder</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Inactive</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>@Model.CreatedAt.ToString("MMM dd, yyyy")</td>
                    </tr>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td>@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</td>
                        </tr>
                    }
                </table>
                
                @if (!string.IsNullOrEmpty(Model.LinkedInUrl) || !string.IsNullOrEmpty(Model.TwitterUrl) || !string.IsNullOrEmpty(Model.GithubUrl))
                {
                    <div class="mt-3">
                        <strong>Social Links:</strong>
                        <div class="mt-1">
                            @if (!string.IsNullOrEmpty(Model.LinkedInUrl))
                            {
                                <a href="@Model.LinkedInUrl" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fab fa-linkedin"></i> LinkedIn
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.TwitterUrl))
                            {
                                <a href="@Model.TwitterUrl" target="_blank" class="btn btn-sm btn-outline-info me-1">
                                    <i class="fab fa-twitter"></i> Twitter
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.GithubUrl))
                            {
                                <a href="@Model.GithubUrl" target="_blank" class="btn btn-sm btn-outline-dark">
                                    <i class="fab fa-github"></i> GitHub
                                </a>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
        
        <hr>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <div class="d-flex justify-content-between">
                <div>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Yes, Delete Team Member
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Impact Information</h6>
    </div>
    <div class="card-body">
        <p class="text-muted mb-0">
            <i class="fas fa-info-circle"></i>
            Deleting this team member will remove them from the system and they will no longer appear on the public website. 
            This is a soft delete operation, so the data can be recovered if needed.
        </p>
    </div>
</div>
