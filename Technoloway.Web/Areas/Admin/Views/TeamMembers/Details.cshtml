@model Technoloway.Core.Entities.TeamMember

@{
    ViewData["Title"] = "Team Member Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Team Member Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">@Model.Name - @Model.Position</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        @if (!string.IsNullOrEmpty(Model.PhotoUrl))
                        {
                            <img src="@Model.PhotoUrl" alt="@Model.Name" class="img-fluid rounded-circle mb-3" style="max-height: 150px; max-width: 150px; object-fit: cover;" />
                        }
                        else
                        {
                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto mb-3" style="height: 150px; width: 150px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                            </div>
                        }
                        
                        <div class="social-links">
                            @if (!string.IsNullOrEmpty(Model.LinkedInUrl))
                            {
                                <a href="@Model.LinkedInUrl" target="_blank" class="btn btn-sm btn-outline-primary me-1" title="LinkedIn">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.TwitterUrl))
                            {
                                <a href="@Model.TwitterUrl" target="_blank" class="btn btn-sm btn-outline-info me-1" title="Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.GithubUrl))
                            {
                                <a href="@Model.GithubUrl" target="_blank" class="btn btn-sm btn-outline-dark me-1" title="GitHub">
                                    <i class="fab fa-github"></i>
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.Email))
                            {
                                <a href="mailto:@Model.Email" class="btn btn-sm btn-outline-success" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            }
                        </div>
                    </div>
                    <div class="col-md-9">
                        <h4 class="mb-2">@Model.Name</h4>
                        <h6 class="text-muted mb-3">@Model.Position</h6>
                        
                        @if (!string.IsNullOrEmpty(Model.Bio))
                        {
                            <p class="mb-4">@Model.Bio</p>
                        }
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Email:</strong> 
                                @if (!string.IsNullOrEmpty(Model.Email))
                                {
                                    <a href="mailto:@Model.Email">@Model.Email</a>
                                }
                                else
                                {
                                    <span class="text-muted">Not provided</span>
                                }
                            </div>
                            <div class="col-sm-6">
                                <strong>Status:</strong> 
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Inactive</span>
                                }
                            </div>
                        </div>
                        
                        <div class="row mt-2">
                            <div class="col-sm-6">
                                <strong>Display Order:</strong> @Model.DisplayOrder
                            </div>
                            <div class="col-sm-6">
                                <strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                        
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div class="row mt-2">
                                <div class="col-sm-6">
                                    <strong>Last Updated:</strong> @Model.UpdatedAt.Value.ToString("MMM dd, yyyy")
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Team Member
                    </a>
                    <form asp-action="ToggleStatus" asp-route-id="@Model.Id" method="post">
                        @Html.AntiForgeryToken()
                        <button type="submit" class="btn @(Model.IsActive ? "btn-warning" : "btn-success") w-100"
                                onclick="return confirm('@(Model.IsActive ? "Deactivate" : "Activate") this team member?')">
                            <i class="fas @(Model.IsActive ? "fa-pause" : "fa-play")"></i> @(Model.IsActive ? "Deactivate" : "Activate")
                        </button>
                    </form>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Team Member
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-list"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Technical Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>@Model.Id</td>
                    </tr>
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td>@Model.Name</td>
                    </tr>
                    <tr>
                        <td><strong>Position:</strong></td>
                        <td>@Model.Position</td>
                    </tr>
                    <tr>
                        <td><strong>Display Order:</strong></td>
                        <td>@Model.DisplayOrder</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>@(Model.IsActive ? "Active" : "Inactive")</td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                    </tr>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>@Model.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")</td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </div>
</div>
