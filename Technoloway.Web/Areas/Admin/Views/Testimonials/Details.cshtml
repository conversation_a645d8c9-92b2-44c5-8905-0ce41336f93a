@model Technoloway.Core.Entities.Testimonial

@{
    ViewData["Title"] = "Testimonial Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Testimonial Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Testimonial Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Client Name:</strong> @Model.ClientName</p>
                        <p><strong>Title:</strong> @Model.ClientTitle</p>
                        <p><strong>Company:</strong> @Model.ClientCompany</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Rating:</strong>
                            @for (int i = 0; i < Model.Rating; i++)
                            {
                                <i class="fas fa-star text-warning"></i>
                            }
                            @for (int i = Model.Rating; i < 5; i++)
                            {
                                <i class="far fa-star text-muted"></i>
                            }
                            (@Model.Rating/5)
                        </p>
                        <p><strong>Display Order:</strong> @Model.DisplayOrder</p>
                        <p><strong>Status:</strong>
                            <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                                @(Model.IsActive ? "Active" : "Inactive")
                            </span>
                        </p>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.ClientPhotoUrl))
                {
                    <div class="mb-3">
                        <p><strong>Photo URL:</strong></p>
                        <a href="@Model.ClientPhotoUrl" target="_blank">@Model.ClientPhotoUrl</a>
                    </div>
                }

                <div class="mb-3">
                    <p><strong>Testimonial Content:</strong></p>
                    <div class="border p-3 bg-light rounded">
                        <p class="mb-0">@Model.Content</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Created:</strong> @Model.CreatedAt.ToString("d")</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Last Updated:</strong> @Model.UpdatedAt?.ToString("d")</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Website Preview</h6>
            </div>
            <div class="card-body">
                <div class="testimonial-preview text-center">
                    <div class="mb-3">
                        @if (!string.IsNullOrEmpty(Model.ClientPhotoUrl))
                        {
                            <img src="@Model.ClientPhotoUrl" alt="@Model.ClientName"
                                 class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;" />
                        }
                        else
                        {
                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto"
                                 style="width: 100px; height: 100px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        }
                    </div>
                    <div class="mb-3">
                        @for (int i = 0; i < Model.Rating; i++)
                        {
                            <i class="fas fa-star text-warning"></i>
                        }
                        @for (int i = Model.Rating; i < 5; i++)
                        {
                            <i class="far fa-star text-muted"></i>
                        }
                    </div>
                    <p class="mb-4 font-italic">"@Model.Content"</p>
                    <h5 class="mb-1">@Model.ClientName</h5>
                    <p class="text-muted">@Model.ClientTitle, @Model.ClientCompany</p>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Testimonial
                    </a>
                    <button class="btn @(Model.IsActive ? "btn-warning" : "btn-success")"
                            onclick="toggleActive(@Model.Id, this)">
                        <i class="fas @(Model.IsActive ? "fa-eye-slash" : "fa-eye") me-2"></i>
                        @(Model.IsActive ? "Deactivate" : "Activate")
                    </button>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Testimonial
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleActive(id, button) {
            $.post('@Url.Action("ToggleActive")', { id: id })
                .done(function(data) {
                    if (data.success) {
                        location.reload(); // Reload to update the UI
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating testimonial status');
                });
        }
    </script>
}
