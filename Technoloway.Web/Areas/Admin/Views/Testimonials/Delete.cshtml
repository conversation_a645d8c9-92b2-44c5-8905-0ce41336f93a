@model Technoloway.Core.Entities.Testimonial

@{
    ViewData["Title"] = "Delete Testimonial";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Testimonial</h1>
    <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4 border-danger">
            <div class="card-header py-3 bg-danger">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h5 class="alert-heading">Are you sure you want to delete this testimonial?</h5>
                    <p>This action cannot be undone. The testimonial will be permanently removed from your website.</p>
                    <hr>
                    <p class="mb-0">
                        <strong>Testimonial:</strong> "@Model.Content"<br>
                        <strong>Client:</strong> @Model.ClientName (@Model.ClientCompany)
                    </p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Client Name:</strong> @Model.ClientName</p>
                        <p><strong>Title:</strong> @Model.ClientTitle</p>
                        <p><strong>Company:</strong> @Model.ClientCompany</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Rating:</strong> 
                            @for (int i = 0; i < Model.Rating; i++)
                            {
                                <i class="fas fa-star text-warning"></i>
                            }
                            @for (int i = Model.Rating; i < 5; i++)
                            {
                                <i class="far fa-star text-muted"></i>
                            }
                            (@Model.Rating/5)
                        </p>
                        <p><strong>Status:</strong> 
                            <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                                @(Model.IsActive ? "Active" : "Inactive")
                            </span>
                        </p>
                        <p><strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <p><strong>Testimonial Content:</strong></p>
                    <div class="border p-3 bg-light rounded">
                        <p class="mb-0 font-italic">"@Model.Content"</p>
                    </div>
                </div>
                
                <form asp-action="Delete" method="post" class="d-inline">
                    <input asp-for="Id" type="hidden" />
                    <button type="submit" class="btn btn-danger me-2">
                        <i class="fas fa-trash me-2"></i>Yes, Delete Testimonial
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Current Preview</h6>
            </div>
            <div class="card-body">
                <div class="testimonial-preview text-center">
                    <div class="mb-3">
                        @if (!string.IsNullOrEmpty(Model.ClientPhotoUrl))
                        {
                            <img src="@Model.ClientPhotoUrl" alt="@Model.ClientName" 
                                 class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;" />
                        }
                        else
                        {
                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto" 
                                 style="width: 80px; height: 80px;">
                                <i class="fas fa-user fa-lg text-white"></i>
                            </div>
                        }
                    </div>
                    <div class="mb-3">
                        @for (int i = 0; i < Model.Rating; i++)
                        {
                            <i class="fas fa-star text-warning"></i>
                        }
                        @for (int i = Model.Rating; i < 5; i++)
                        {
                            <i class="far fa-star text-muted"></i>
                        }
                    </div>
                    <p class="mb-4 font-italic small">"@Model.Content"</p>
                    <h6 class="mb-1">@Model.ClientName</h6>
                    <p class="text-muted small">@Model.ClientTitle, @Model.ClientCompany</p>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">Alternative Actions</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-3">
                    Instead of deleting, you might want to:
                </p>
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Instead
                    </a>
                    @if (Model.IsActive)
                    {
                        <button class="btn btn-sm btn-warning" onclick="toggleActive(@Model.Id, this)">
                            <i class="fas fa-eye-slash me-2"></i>Deactivate Only
                        </button>
                    }
                    else
                    {
                        <button class="btn btn-sm btn-success" onclick="toggleActive(@Model.Id, this)">
                            <i class="fas fa-eye me-2"></i>Reactivate
                        </button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function toggleActive(id, button) {
            $.post('@Url.Action("ToggleActive")', { id: id })
                .done(function(data) {
                    if (data.success) {
                        // Redirect to index after toggling
                        window.location.href = '@Url.Action("Index")';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating testimonial status');
                });
        }
    </script>
}
