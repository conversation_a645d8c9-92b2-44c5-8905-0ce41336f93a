@model Technoloway.Web.Areas.Admin.Models.TestimonialViewModel

@{
    ViewData["Title"] = "Create Testimonial";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Create New Testimonial</h1>
    <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Testimonial Information</h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientName" class="form-label">Client Name *</label>
                                <input asp-for="ClientName" class="form-control" placeholder="Enter client name" />
                                <span asp-validation-for="ClientName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientTitle" class="form-label">Client Title *</label>
                                <input asp-for="ClientTitle" class="form-control" placeholder="e.g., CEO, Marketing Director" />
                                <span asp-validation-for="ClientTitle" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientCompany" class="form-label">Company *</label>
                                <input asp-for="ClientCompany" class="form-control" placeholder="Enter company name" />
                                <span asp-validation-for="ClientCompany" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="PhotoFile" class="form-label">Client Photo</label>
                                <input asp-for="PhotoFile" class="form-control" type="file" accept="image/*" />
                                <span asp-validation-for="PhotoFile" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Optional: Upload client's photo. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 200x200px.
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Content" class="form-label">Testimonial Content *</label>
                        <textarea asp-for="Content" class="form-control" rows="4"
                                  placeholder="Enter the testimonial content..."></textarea>
                        <span asp-validation-for="Content" class="text-danger"></span>
                        <small class="form-text text-muted">10-1000 characters</small>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-control">
                                    <option value="">Select Rating</option>
                                    <option value="1">⭐ 1 Star</option>
                                    <option value="2">⭐⭐ 2 Stars</option>
                                    <option value="3">⭐⭐⭐ 3 Stars</option>
                                    <option value="4">⭐⭐⭐⭐ 4 Stars</option>
                                    <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="DisplayOrder" class="form-label">Display Order</label>
                                <input asp-for="DisplayOrder" class="form-control" type="number" min="0"
                                       placeholder="0" />
                                <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                <small class="form-text text-muted">Lower numbers appear first</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Status</label>
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        Active (visible on website)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Testimonial
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Preview</h6>
            </div>
            <div class="card-body">
                <div class="testimonial-preview text-center">
                    <div class="mb-3">
                        <img id="preview-photo" src="/images/avatar-placeholder.jpg" alt="Client Photo"
                             class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;" />
                    </div>
                    <div id="preview-rating" class="mb-3">
                        <span class="text-muted">No rating selected</span>
                    </div>
                    <p id="preview-content" class="mb-3 text-muted">
                        Testimonial content will appear here...
                    </p>
                    <h6 id="preview-name" class="mb-1 text-muted">Client Name</h6>
                    <p id="preview-title" class="text-muted small">Title, Company</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Update preview when form fields change
            $('#ClientName').on('input', function() {
                $('#preview-name').text($(this).val() || 'Client Name');
            });

            $('#ClientTitle, #ClientCompany').on('input', function() {
                var title = $('#ClientTitle').val() || 'Title';
                var company = $('#ClientCompany').val() || 'Company';
                $('#preview-title').text(title + ', ' + company);
            });

            $('#Content').on('input', function() {
                $('#preview-content').text($(this).val() || 'Testimonial content will appear here...');
            });

            $('#Rating').on('change', function() {
                var rating = parseInt($(this).val());
                var stars = '';
                if (rating > 0) {
                    for (var i = 0; i < rating; i++) {
                        stars += '<i class="fas fa-star text-warning"></i>';
                    }
                    for (var i = rating; i < 5; i++) {
                        stars += '<i class="far fa-star text-muted"></i>';
                    }
                    $('#preview-rating').html(stars);
                } else {
                    $('#preview-rating').html('<span class="text-muted">No rating selected</span>');
                }
            });

            $('#PhotoFile').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview-photo').attr('src', e.target.result);
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#preview-photo').attr('src', '/images/avatar-placeholder.jpg');
                }
            });
        });
    </script>
}
