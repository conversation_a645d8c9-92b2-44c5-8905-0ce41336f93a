using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using Technoloway.Web.Areas.Admin.ViewModels;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class InvoicesController : Controller
{
    private readonly IRepository<Technoloway.Core.Entities.Invoice> _invoiceRepository;
    private readonly IRepository<Technoloway.Core.Entities.InvoiceItem> _invoiceItemRepository;
    private readonly IRepository<Technoloway.Core.Entities.Payment> _paymentRepository;
    private readonly IRepository<Technoloway.Core.Entities.Client> _clientRepository;
    private readonly IRepository<Technoloway.Core.Entities.Project> _projectRepository;

    public InvoicesController(
        IRepository<Technoloway.Core.Entities.Invoice> invoiceRepository,
        IRepository<Technoloway.Core.Entities.InvoiceItem> invoiceItemRepository,
        IRepository<Technoloway.Core.Entities.Payment> paymentRepository,
        IRepository<Technoloway.Core.Entities.Client> clientRepository,
        IRepository<Technoloway.Core.Entities.Project> projectRepository)
    {
        _invoiceRepository = invoiceRepository;
        _invoiceItemRepository = invoiceItemRepository;
        _paymentRepository = paymentRepository;
        _clientRepository = clientRepository;
        _projectRepository = projectRepository;
    }

    public async Task<IActionResult> Index()
    {
        var invoices = await _invoiceRepository.GetAll()
            .Where(i => !i.IsDeleted)
            .Include(i => i.Client)
            .Include(i => i.Project)
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync();

        // Calculate balances and payments for all invoice types
        decimal partiallyPaidBalance = 0;
        decimal overdueBalance = 0;
        decimal totalPaymentsReceived = 0;
        decimal totalOutstandingBalance = 0;
        int invoicesWithPayments = 0;
        int invoicesWithBalance = 0;

        var partiallyPaidInvoices = invoices.Where(i => i.Status == "Partially Paid").ToList();
        var overdueInvoices = invoices.Where(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow)).ToList();

        // Calculate total payments received from ALL invoices and balances in one pass
        foreach (var invoice in invoices)
        {
            var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == invoice.Id && !p.IsDeleted);
            var paidAmount = payments.Sum(p => p.Amount);
            var remainingBalance = invoice.TotalAmount - paidAmount;

            // Add to total payments received
            totalPaymentsReceived += paidAmount;

            // Count invoices with payments
            if (paidAmount > 0)
            {
                invoicesWithPayments++;
                Console.WriteLine($"Invoice {invoice.InvoiceNumber} ({invoice.Status}): Total Amount = {invoice.TotalAmount:C}, Payments = {paidAmount:C}");
            }

            // Calculate total outstanding balance (for all unpaid and partially paid invoices)
            if (remainingBalance > 0 && invoice.Status != "Paid")
            {
                totalOutstandingBalance += remainingBalance;
                invoicesWithBalance++;
            }

            // Calculate balances for specific statuses
            if (invoice.Status == "Partially Paid")
            {
                partiallyPaidBalance += remainingBalance;
            }
            else if (invoice.Status == "Overdue" || (invoice.Status != "Paid" && invoice.DueDate < DateTime.UtcNow))
            {
                overdueBalance += remainingBalance;
            }
        }

        Console.WriteLine($"Total Payments Received: {totalPaymentsReceived:C}, Invoices with Payments: {invoicesWithPayments}");
        Console.WriteLine($"Total Outstanding Balance: {totalOutstandingBalance:C}, Invoices with Balance: {invoicesWithBalance}");

        var viewModel = new InvoiceStatsViewModel
        {
            Invoices = invoices,
            PartiallyPaidBalance = partiallyPaidBalance,
            OverdueBalance = overdueBalance,
            TotalPaymentsReceived = totalPaymentsReceived,
            InvoicesWithPayments = invoicesWithPayments,
            TotalOutstandingBalance = totalOutstandingBalance,
            InvoicesWithBalance = invoicesWithBalance
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Analytics()
    {
        var invoices = await _invoiceRepository.GetAll()
            .Where(i => !i.IsDeleted)
            .Include(i => i.Client)
            .Include(i => i.Project)
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync();

        // Calculate comprehensive analytics
        var analytics = new InvoiceAnalyticsViewModel
        {
            // Basic counts
            TotalInvoices = invoices.Count,
            PaidInvoices = invoices.Count(i => i.Status == "Paid"),
            PendingInvoices = invoices.Count(i => i.Status == "Pending"),
            PartiallyPaidInvoices = invoices.Count(i => i.Status == "Partially Paid"),
            OverdueInvoices = invoices.Count(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow)),

            // Financial totals
            TotalAmount = invoices.Sum(i => i.TotalAmount),
            PaidAmount = invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount),
            PendingAmount = invoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount),
            PartiallyPaidAmount = invoices.Where(i => i.Status == "Partially Paid").Sum(i => i.TotalAmount),
            OverdueAmount = invoices.Where(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow)).Sum(i => i.TotalAmount),

            // Monthly data for charts
            MonthlyData = GetMonthlyInvoiceData(invoices),
            StatusData = GetStatusDistribution(invoices),
            ClientData = GetTopClientsByRevenue(invoices),

            // Recent activity
            RecentInvoices = invoices.Take(10).ToList(),

            // Average calculations
            AverageInvoiceAmount = invoices.Any() ? invoices.Average(i => i.TotalAmount) : 0,
            AverageDaysToPayment = CalculateAverageDaysToPayment(invoices),

            // Growth metrics
            MonthOverMonthGrowth = CalculateMonthOverMonthGrowth(invoices),
            YearOverYearGrowth = CalculateYearOverYearGrowth(invoices)
        };

        return View(analytics);
    }

    private List<MonthlyInvoiceData> GetMonthlyInvoiceData(List<Invoice> invoices)
    {
        var monthlyData = new List<MonthlyInvoiceData>();
        var startDate = DateTime.Now.AddMonths(-11).Date;

        for (int i = 0; i < 12; i++)
        {
            var month = startDate.AddMonths(i);
            var monthInvoices = invoices.Where(inv =>
                inv.IssueDate.Year == month.Year &&
                inv.IssueDate.Month == month.Month).ToList();

            monthlyData.Add(new MonthlyInvoiceData
            {
                Month = month.ToString("MMM yyyy"),
                InvoiceCount = monthInvoices.Count,
                TotalAmount = monthInvoices.Sum(inv => inv.TotalAmount),
                PaidAmount = monthInvoices.Where(inv => inv.Status == "Paid").Sum(inv => inv.TotalAmount)
            });
        }

        return monthlyData;
    }

    private List<StatusDistributionData> GetStatusDistribution(List<Invoice> invoices)
    {
        return new List<StatusDistributionData>
        {
            new StatusDistributionData { Status = "Paid", Count = invoices.Count(i => i.Status == "Paid"), Amount = invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount) },
            new StatusDistributionData { Status = "Pending", Count = invoices.Count(i => i.Status == "Pending"), Amount = invoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount) },
            new StatusDistributionData { Status = "Partially Paid", Count = invoices.Count(i => i.Status == "Partially Paid"), Amount = invoices.Where(i => i.Status == "Partially Paid").Sum(i => i.TotalAmount) },
            new StatusDistributionData { Status = "Overdue", Count = invoices.Count(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow)), Amount = invoices.Where(i => i.Status == "Overdue" || (i.Status != "Paid" && i.DueDate < DateTime.UtcNow)).Sum(i => i.TotalAmount) }
        };
    }

    private List<ClientRevenueData> GetTopClientsByRevenue(List<Invoice> invoices)
    {
        return invoices
            .Where(i => i.Client != null)
            .GroupBy(i => new { i.ClientId, i.Client.CompanyName })
            .Select(g => new ClientRevenueData
            {
                ClientName = g.Key.CompanyName ?? "Unknown Client",
                TotalRevenue = g.Sum(i => i.TotalAmount),
                InvoiceCount = g.Count(),
                PaidAmount = g.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount)
            })
            .OrderByDescending(c => c.TotalRevenue)
            .Take(10)
            .ToList();
    }

    private double CalculateAverageDaysToPayment(List<Invoice> invoices)
    {
        var paidInvoices = invoices.Where(i => i.Status == "Paid").ToList();
        if (!paidInvoices.Any()) return 0;

        // This is a simplified calculation - in a real scenario, you'd track actual payment dates
        return paidInvoices.Average(i => (i.DueDate - i.IssueDate).TotalDays);
    }

    private decimal CalculateMonthOverMonthGrowth(List<Invoice> invoices)
    {
        var currentMonth = DateTime.Now;
        var lastMonth = currentMonth.AddMonths(-1);

        var currentMonthAmount = invoices
            .Where(i => i.IssueDate.Year == currentMonth.Year && i.IssueDate.Month == currentMonth.Month)
            .Sum(i => i.TotalAmount);

        var lastMonthAmount = invoices
            .Where(i => i.IssueDate.Year == lastMonth.Year && i.IssueDate.Month == lastMonth.Month)
            .Sum(i => i.TotalAmount);

        if (lastMonthAmount == 0) return 0;
        return ((currentMonthAmount - lastMonthAmount) / lastMonthAmount) * 100;
    }

    private decimal CalculateYearOverYearGrowth(List<Invoice> invoices)
    {
        var currentYear = DateTime.Now.Year;
        var lastYear = currentYear - 1;

        var currentYearAmount = invoices
            .Where(i => i.IssueDate.Year == currentYear)
            .Sum(i => i.TotalAmount);

        var lastYearAmount = invoices
            .Where(i => i.IssueDate.Year == lastYear)
            .Sum(i => i.TotalAmount);

        if (lastYearAmount == 0) return 0;
        return ((currentYearAmount - lastYearAmount) / lastYearAmount) * 100;
    }

    public async Task<IActionResult> Create()
    {
        await PopulateDropdowns();
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(Technoloway.Core.Entities.Invoice invoice)
    {
        try
        {
            // Debug information
            Console.WriteLine("Create POST method called");
            Console.WriteLine($"Invoice data: ClientId={invoice.ClientId}, Amount={invoice.Amount}, TaxRate={invoice.TaxRate}");

            // Remove ModelState errors for navigation properties that will be set later
            ModelState.Remove("Client");
            ModelState.Remove("Project");
            ModelState.Remove("Payments");
            ModelState.Remove("Items");

            foreach (var key in ModelState.Keys)
            {
                var state = ModelState[key];
                if (state.Errors.Count > 0)
                {
                    foreach (var error in state.Errors)
                    {
                        Console.WriteLine($"Error in {key}: {error.ErrorMessage}");
                    }
                }
            }

            // Additional validation
            if (invoice.ClientId <= 0)
            {
                ModelState.AddModelError("ClientId", "Please select a client");
            }

            if (invoice.Amount <= 0)
            {
                ModelState.AddModelError("Amount", "Amount must be greater than 0");
            }

            if (invoice.DueDate <= invoice.IssueDate)
            {
                ModelState.AddModelError("DueDate", "Due date must be after issue date");
            }

            if (ModelState.IsValid)
            {
                // Verify client exists
                var client = await _clientRepository.GetByIdAsync(invoice.ClientId);
                if (client == null)
                {
                    ModelState.AddModelError("ClientId", "Selected client does not exist");
                    await PopulateDropdowns();
                    return View(invoice);
                }

                // Calculate tax amount and total amount
                invoice.TaxAmount = invoice.Amount * (invoice.TaxRate / 100);
                invoice.TotalAmount = invoice.Amount + invoice.TaxAmount;

                // Set timestamps
                invoice.CreatedAt = DateTime.UtcNow;
                invoice.UpdatedAt = DateTime.UtcNow;

                // Generate invoice number if not provided
                if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                {
                    invoice.InvoiceNumber = await GenerateInvoiceNumber();
                }

                // Set default status if not provided
                if (string.IsNullOrEmpty(invoice.Status))
                {
                    invoice.Status = "Pending";
                }

                Console.WriteLine($"Invoice details before saving: ClientId={invoice.ClientId}, Amount={invoice.Amount}, TaxRate={invoice.TaxRate}, TaxAmount={invoice.TaxAmount}, TotalAmount={invoice.TotalAmount}");

                var result = await _invoiceRepository.AddAsync(invoice);
                Console.WriteLine($"Invoice saved successfully with ID: {result.Id}");

                TempData["SuccessMessage"] = "Invoice created successfully!";
                return RedirectToAction(nameof(Details), new { id = result.Id });
            }
            else
            {
                Console.WriteLine("ModelState is invalid:");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"Validation error: {error.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in Create method: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ModelState.AddModelError("", $"Error creating invoice: {ex.Message}");
        }

        await PopulateDropdowns(invoice.ClientId, invoice.ProjectId);
        return View(invoice);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        await PopulateDropdowns(invoice.ClientId, invoice.ProjectId);
        return View(invoice);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, Technoloway.Core.Entities.Invoice invoice)
    {
        if (id != invoice.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                // Calculate tax amount and total amount
                invoice.TaxAmount = invoice.Amount * (invoice.TaxRate / 100);
                invoice.TotalAmount = invoice.Amount + invoice.TaxAmount;

                // Update modification date
                invoice.UpdatedAt = DateTime.UtcNow;

                await _invoiceRepository.UpdateAsync(invoice);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await InvoiceExists(invoice.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Details), new { id = invoice.Id });
        }

        await PopulateDropdowns(invoice.ClientId, invoice.ProjectId);
        return View(invoice);
    }

    public async Task<IActionResult> Details(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        var client = await _clientRepository.GetByIdAsync(invoice.ClientId);
        var project = invoice.ProjectId.HasValue ? await _projectRepository.GetByIdAsync(invoice.ProjectId.Value) : null;
        var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == id);
        var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);

        ViewBag.Client = client;
        ViewBag.Project = project;
        ViewBag.Items = items;
        ViewBag.Payments = payments;
        ViewBag.TotalPaid = payments.Sum(p => p.Amount);
        ViewBag.Balance = invoice.TotalAmount - payments.Sum(p => p.Amount);

        return View(invoice);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        var client = await _clientRepository.GetByIdAsync(invoice.ClientId);
        var project = invoice.ProjectId.HasValue ? await _projectRepository.GetByIdAsync(invoice.ProjectId.Value) : null;
        var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == id);
        var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);

        ViewBag.Client = client;
        ViewBag.Project = project;
        ViewBag.ItemCount = items.Count;
        ViewBag.PaymentCount = payments.Count;

        return View(invoice);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var invoice = await _invoiceRepository.GetByIdAsync(id);
            if (invoice == null || invoice.IsDeleted)
            {
                TempData["ErrorMessage"] = "Invoice not found or already deleted.";
                return RedirectToAction(nameof(Index));
            }

            // Use the repository's DeleteAsync method which handles soft delete properly
            await _invoiceRepository.DeleteAsync(invoice);

            TempData["SuccessMessage"] = $"Invoice #{invoice.InvoiceNumber} has been deleted successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting invoice: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            TempData["ErrorMessage"] = $"Error deleting invoice: {ex.Message}";
            return RedirectToAction(nameof(Index));
        }
    }

    public async Task<IActionResult> Items(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == id);
        ViewBag.Invoice = invoice;

        return View(items);
    }

    public async Task<IActionResult> AddItem(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        ViewBag.Invoice = invoice;
        return View(new Technoloway.Core.Entities.InvoiceItem { InvoiceId = id });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddItem(Technoloway.Core.Entities.InvoiceItem item)
    {
        if (ModelState.IsValid)
        {
            // Calculate total price
            item.TotalPrice = item.Quantity * item.UnitPrice;

            // Set creation date
            item.CreatedAt = DateTime.UtcNow;
            item.UpdatedAt = DateTime.UtcNow;

            await _invoiceItemRepository.AddAsync(item);

            // Update invoice amount
            var invoice = await _invoiceRepository.GetByIdAsync(item.InvoiceId);
            if (invoice != null)
            {
                var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == item.InvoiceId);
                invoice.Amount = items.Sum(i => i.TotalPrice);
                invoice.TaxAmount = invoice.Amount * (invoice.TaxRate / 100);
                invoice.TotalAmount = invoice.Amount + invoice.TaxAmount;
                invoice.UpdatedAt = DateTime.UtcNow;
                await _invoiceRepository.UpdateAsync(invoice);
            }

            return RedirectToAction(nameof(Items), new { id = item.InvoiceId });
        }

        var invoiceForView = await _invoiceRepository.GetByIdAsync(item.InvoiceId);
        ViewBag.Invoice = invoiceForView;
        return View(item);
    }

    public async Task<IActionResult> Payments(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);
        var totalPaid = payments.Sum(p => p.Amount);
        var balance = Math.Max(0, invoice.TotalAmount - totalPaid);

        Console.WriteLine($"Payments view: InvoiceId={id}, InvoiceTotal={invoice.TotalAmount}, TotalPaid={totalPaid}, Balance={balance}");

        ViewBag.Invoice = invoice;
        ViewBag.TotalPaid = totalPaid;
        ViewBag.Balance = balance;

        return View(payments);
    }

    public async Task<IActionResult> AddPayment(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        if (invoice == null)
        {
            return NotFound();
        }

        var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);
        var balance = Math.Max(0, invoice.TotalAmount - payments.Sum(p => p.Amount));

        Console.WriteLine($"AddPayment GET: InvoiceId={id}, InvoiceTotal={invoice.TotalAmount}, TotalPaid={payments.Sum(p => p.Amount)}, Balance={balance}");

        ViewBag.Invoice = invoice;
        ViewBag.Balance = balance;

        return View(new Technoloway.Core.Entities.Payment { InvoiceId = id, Amount = balance });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddPayment(Technoloway.Core.Entities.Payment payment)
    {
        try
        {
            Console.WriteLine($"AddPayment POST called: InvoiceId={payment.InvoiceId}, Amount={payment.Amount}, PaymentMethod={payment.PaymentMethod}");

            // Remove ModelState errors for navigation properties that will be set later
            ModelState.Remove("Invoice");

            // Additional validation
            if (payment.InvoiceId <= 0)
            {
                ModelState.AddModelError("InvoiceId", "Invalid invoice ID");
            }

            if (payment.Amount <= 0)
            {
                ModelState.AddModelError("Amount", "Payment amount must be greater than 0");
            }

            if (string.IsNullOrWhiteSpace(payment.PaymentMethod))
            {
                ModelState.AddModelError("PaymentMethod", "Payment method is required");
            }

            // Verify invoice exists and get current balance
            var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
            if (invoice == null)
            {
                ModelState.AddModelError("InvoiceId", "Invoice not found");
                return View(payment);
            }

            var existingPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == payment.InvoiceId);
            var currentBalance = invoice.TotalAmount - existingPayments.Sum(p => p.Amount);

            Console.WriteLine($"Payment validation: InvoiceTotal={invoice.TotalAmount}, ExistingPayments={existingPayments.Sum(p => p.Amount)}, CurrentBalance={currentBalance}, PaymentAmount={payment.Amount}");

            // Use decimal comparison with small tolerance for floating point precision issues
            if (payment.Amount > currentBalance + 0.01m)
            {
                ModelState.AddModelError("Amount", $"Payment amount cannot exceed the remaining balance of {currentBalance:C}");
            }

            if (ModelState.IsValid)
            {
                // Set timestamps
                payment.CreatedAt = DateTime.UtcNow;
                payment.UpdatedAt = DateTime.UtcNow;

                // Generate transaction ID if not provided
                if (string.IsNullOrEmpty(payment.TransactionId))
                {
                    payment.TransactionId = $"TXN-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
                }

                // Set default status if not provided
                if (string.IsNullOrEmpty(payment.Status))
                {
                    payment.Status = "Completed";
                }

                Console.WriteLine($"Payment details before saving: InvoiceId={payment.InvoiceId}, Amount={payment.Amount}, TransactionId={payment.TransactionId}");

                // Add payment to database
                var savedPayment = await _paymentRepository.AddAsync(payment);
                Console.WriteLine($"Payment saved successfully with ID: {savedPayment.Id}");

                // Update invoice status if fully paid
                var updatedPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == payment.InvoiceId);
                var totalPaid = updatedPayments.Sum(p => p.Amount);

                Console.WriteLine($"Total paid: {totalPaid}, Invoice total: {invoice.TotalAmount}");

                if (totalPaid >= invoice.TotalAmount && invoice.Status != "Paid")
                {
                    invoice.Status = "Paid";
                    invoice.UpdatedAt = DateTime.UtcNow;
                    await _invoiceRepository.UpdateAsync(invoice);
                    Console.WriteLine("Invoice status updated to Paid");
                }
                else if (totalPaid > 0 && totalPaid < invoice.TotalAmount && invoice.Status == "Pending")
                {
                    invoice.Status = "Partially Paid";
                    invoice.UpdatedAt = DateTime.UtcNow;
                    await _invoiceRepository.UpdateAsync(invoice);
                    Console.WriteLine("Invoice status updated to Partially Paid");
                }

                TempData["SuccessMessage"] = "Payment added successfully!";
                return RedirectToAction(nameof(Payments), new { id = payment.InvoiceId });
            }
            else
            {
                Console.WriteLine("ModelState is invalid:");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"Validation error: {error.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in AddPayment method: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ModelState.AddModelError("", $"Error adding payment: {ex.Message}");
        }

        // If we get here, there were validation errors - reload the view
        var invoiceForView = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
        if (invoiceForView != null)
        {
            var paymentsForView = await _paymentRepository.ListAsync(p => p.InvoiceId == payment.InvoiceId);
            var balance = Math.Max(0, invoiceForView.TotalAmount - paymentsForView.Sum(p => p.Amount));

            Console.WriteLine($"AddPayment validation error reload: InvoiceId={payment.InvoiceId}, Balance={balance}");

            ViewBag.Invoice = invoiceForView;
            ViewBag.Balance = balance;
        }

        return View(payment);
    }

    public async Task<IActionResult> EditPayment(int id)
    {
        Console.WriteLine($"EditPayment GET called: id={id}");

        var payment = await _paymentRepository.GetByIdAsync(id);
        if (payment == null || payment.IsDeleted)
        {
            Console.WriteLine($"Payment not found or deleted: id={id}");
            return NotFound();
        }

        Console.WriteLine($"Payment found: Id={payment.Id}, Amount={payment.Amount}, InvoiceId={payment.InvoiceId}");

        var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
        if (invoice == null || invoice.IsDeleted)
        {
            Console.WriteLine($"Invoice not found or deleted: id={payment.InvoiceId}");
            return NotFound();
        }

        Console.WriteLine($"Invoice found: Id={invoice.Id}, InvoiceNumber={invoice.InvoiceNumber}");

        ViewBag.Invoice = invoice;
        return View(payment);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditPayment(int id, Technoloway.Core.Entities.Payment payment)
    {
        Console.WriteLine($"EditPayment POST called: id={id}, payment.Id={payment.Id}");

        if (id != payment.Id)
        {
            Console.WriteLine($"ID mismatch: URL id={id}, payment.Id={payment.Id}");
            return NotFound();
        }

        try
        {
            // Remove ModelState errors for navigation properties
            ModelState.Remove("Invoice");

            Console.WriteLine($"ModelState.IsValid: {ModelState.IsValid}");
            if (!ModelState.IsValid)
            {
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"ModelState error: {error.ErrorMessage}");
                }
            }

            // Get the original payment to preserve InvoiceId
            var originalPayment = await _paymentRepository.GetByIdAsync(id);
            if (originalPayment == null)
            {
                return NotFound();
            }

            // Preserve the original InvoiceId
            payment.InvoiceId = originalPayment.InvoiceId;

            // Validation
            if (payment.Amount <= 0)
            {
                ModelState.AddModelError("Amount", "Payment amount must be greater than 0");
            }

            if (string.IsNullOrWhiteSpace(payment.PaymentMethod))
            {
                ModelState.AddModelError("PaymentMethod", "Payment method is required");
            }

            // Verify invoice exists and check balance (excluding current payment)
            var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
            if (invoice == null)
            {
                ModelState.AddModelError("", "Associated invoice not found");
            }
            else
            {
                var existingPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == payment.InvoiceId && p.Id != payment.Id);
                var currentBalance = invoice.TotalAmount - existingPayments.Sum(p => p.Amount);

                Console.WriteLine($"Edit Payment validation: InvoiceTotal={invoice.TotalAmount}, ExistingPayments={existingPayments.Sum(p => p.Amount)}, CurrentBalance={currentBalance}, NewPaymentAmount={payment.Amount}");

                // Use decimal comparison with small tolerance for floating point precision issues
                if (payment.Amount > currentBalance + 0.01m)
                {
                    ModelState.AddModelError("Amount", $"Payment amount cannot exceed the remaining balance of {currentBalance:C}");
                }
            }

            if (ModelState.IsValid)
            {
                // Preserve original creation date and update modification date
                payment.CreatedAt = originalPayment.CreatedAt;
                payment.UpdatedAt = DateTime.UtcNow;

                // Preserve transaction ID if not changed
                if (string.IsNullOrEmpty(payment.TransactionId))
                {
                    payment.TransactionId = originalPayment.TransactionId;
                }

                // Set default status if not provided
                if (string.IsNullOrEmpty(payment.Status))
                {
                    payment.Status = "Completed";
                }

                Console.WriteLine($"Updating payment: Id={payment.Id}, Amount={payment.Amount}, TransactionId={payment.TransactionId}");

                await _paymentRepository.UpdateDetachedAsync(payment);

                // Update invoice status based on new payment total
                var allPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == payment.InvoiceId);
                var totalPaid = allPayments.Sum(p => p.Amount);

                if (totalPaid >= invoice.TotalAmount && invoice.Status != "Paid")
                {
                    invoice.Status = "Paid";
                    invoice.UpdatedAt = DateTime.UtcNow;
                    await _invoiceRepository.UpdateAsync(invoice);
                }
                else if (totalPaid > 0 && totalPaid < invoice.TotalAmount)
                {
                    invoice.Status = "Partially Paid";
                    invoice.UpdatedAt = DateTime.UtcNow;
                    await _invoiceRepository.UpdateAsync(invoice);
                }
                else if (totalPaid == 0)
                {
                    invoice.Status = "Pending";
                    invoice.UpdatedAt = DateTime.UtcNow;
                    await _invoiceRepository.UpdateAsync(invoice);
                }

                TempData["SuccessMessage"] = "Payment updated successfully!";
                return RedirectToAction(nameof(Payments), new { id = payment.InvoiceId });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating payment: {ex.Message}");
            ModelState.AddModelError("", $"Error updating payment: {ex.Message}");
        }

        // If we get here, there were validation errors - reload the view
        Console.WriteLine("Validation failed, reloading view");
        var invoiceForView = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
        ViewBag.Invoice = invoiceForView;

        // Add a general error message for the user
        if (!ModelState.IsValid)
        {
            TempData["ErrorMessage"] = "Please correct the errors below and try again.";
        }

        return View(payment);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeletePayment(int id)
    {
        try
        {
            var payment = await _paymentRepository.GetByIdAsync(id);
            if (payment == null || payment.IsDeleted)
            {
                TempData["ErrorMessage"] = "Payment not found.";
                return RedirectToAction(nameof(Index));
            }

            var invoiceId = payment.InvoiceId;
            var invoice = await _invoiceRepository.GetByIdAsync(invoiceId);

            Console.WriteLine($"Deleting payment: Id={payment.Id}, Amount={payment.Amount}, InvoiceId={invoiceId}");

            // Delete the payment
            await _paymentRepository.DeleteAsync(payment);

            // Update invoice status based on remaining payments
            if (invoice != null)
            {
                var remainingPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == invoiceId);
                var totalPaid = remainingPayments.Sum(p => p.Amount);

                if (totalPaid >= invoice.TotalAmount)
                {
                    invoice.Status = "Paid";
                }
                else if (totalPaid > 0)
                {
                    invoice.Status = "Partially Paid";
                }
                else
                {
                    invoice.Status = "Pending";
                }

                invoice.UpdatedAt = DateTime.UtcNow;
                await _invoiceRepository.UpdateAsync(invoice);
                Console.WriteLine($"Invoice status updated to: {invoice.Status}");
            }

            TempData["SuccessMessage"] = "Payment deleted successfully!";
            return RedirectToAction(nameof(Payments), new { id = invoiceId });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting payment: {ex.Message}");
            TempData["ErrorMessage"] = $"Error deleting payment: {ex.Message}";
            return RedirectToAction(nameof(Index));
        }
    }

    private async Task<bool> InvoiceExists(int id)
    {
        var invoice = await _invoiceRepository.GetByIdAsync(id);
        return invoice != null;
    }

    private async Task PopulateDropdowns(int? selectedClientId = null, int? selectedProjectId = null)
    {
        try
        {
            var clients = await _clientRepository.ListAsync(c => !c.IsDeleted);

            // Debug information
            Console.WriteLine($"Found {clients.Count} clients");
            foreach (var client in clients)
            {
                Console.WriteLine($"Client ID: {client.Id}, Name: {client.CompanyName}");
            }

            if (clients.Count == 0)
            {
                // Add a dummy client for testing if no clients exist
                var dummyClient = new Technoloway.Core.Entities.Client
                {
                    CompanyName = "Test Company",
                    ContactName = "Test Contact",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "************",
                    Address = "123 Test St",
                    City = "Test City",
                    State = "TS",
                    ZipCode = "12345",
                    Country = "Test Country",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _clientRepository.AddAsync(dummyClient);
                clients = await _clientRepository.ListAsync(c => !c.IsDeleted);
                Console.WriteLine($"Added dummy client with ID: {dummyClient.Id}");
            }

            ViewBag.Clients = new SelectList(clients, "Id", "CompanyName", selectedClientId);

            var projects = await _projectRepository.ListAsync(p => !p.IsDeleted);
            ViewBag.Projects = new SelectList(projects, "Id", "Name", selectedProjectId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in PopulateDropdowns: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            // Create empty SelectLists to prevent errors
            ViewBag.Clients = new SelectList(new List<Technoloway.Core.Entities.Client>(), "Id", "CompanyName");
            ViewBag.Projects = new SelectList(new List<Technoloway.Core.Entities.Project>(), "Id", "Name");
        }
    }

    private async Task<string> GenerateInvoiceNumber()
    {
        var year = DateTime.UtcNow.Year;
        var month = DateTime.UtcNow.Month;
        var invoiceCount = await _invoiceRepository.CountAsync(i => i.IssueDate.Year == year && i.IssueDate.Month == month);

        return $"INV-{year}{month:D2}-{invoiceCount + 1:D4}";
    }
}
