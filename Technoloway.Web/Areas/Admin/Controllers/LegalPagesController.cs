using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;

namespace Technoloway.Web.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Policy = "RequireContentManager")]
    public class LegalPagesController : Controller
    {
        private readonly ILegalPageRepository _legalPageRepository;

        public LegalPagesController(ILegalPageRepository legalPageRepository)
        {
            _legalPageRepository = legalPageRepository;
        }

        public async Task<IActionResult> Index()
        {
            var legalPages = await _legalPageRepository.ListAllAsync();
            return View(legalPages);
        }

        public async Task<IActionResult> Details(int id)
        {
            var legalPage = await _legalPageRepository.GetWithSectionsAsync(id);
            if (legalPage == null)
            {
                return NotFound();
            }
            return View(legalPage);
        }

        public IActionResult Create()
        {
            var model = new LegalPageViewModel
            {
                IsActive = true,
                Sections = new List<LegalPageSectionViewModel>()
            };
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(LegalPageViewModel model)
        {
            if (ModelState.IsValid)
            {
                var legalPage = new LegalPage
                {
                    Title = model.Title,
                    Slug = model.Slug.ToLower().Replace(" ", "-"),
                    MetaDescription = model.MetaDescription,
                    Content = model.Content,
                    IsActive = model.IsActive,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = User.Identity?.Name
                };

                await _legalPageRepository.AddAsync(legalPage);

                // Add sections if any
                if (model.Sections?.Any() == true)
                {
                    var sections = model.Sections.Select(s => new LegalPageSection
                    {
                        LegalPageId = legalPage.Id,
                        Title = s.Title,
                        Content = s.Content,
                        IconClass = s.IconClass,
                        DisplayOrder = s.DisplayOrder,
                        IsActive = s.IsActive
                    }).ToList();

                    await _legalPageRepository.UpdateWithSectionsAsync(legalPage, sections);
                }

                TempData["SuccessMessage"] = "Legal page created successfully.";
                return RedirectToAction(nameof(Index));
            }

            return View(model);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var legalPage = await _legalPageRepository.GetWithSectionsAsync(id);
            if (legalPage == null)
            {
                return NotFound();
            }

            var model = new LegalPageViewModel
            {
                Id = legalPage.Id,
                Title = legalPage.Title,
                Slug = legalPage.Slug,
                MetaDescription = legalPage.MetaDescription,
                Content = legalPage.Content,
                IsActive = legalPage.IsActive,
                Sections = legalPage.Sections.Select(s => new LegalPageSectionViewModel
                {
                    Id = s.Id,
                    Title = s.Title,
                    Content = s.Content,
                    IconClass = s.IconClass,
                    DisplayOrder = s.DisplayOrder,
                    IsActive = s.IsActive
                }).ToList()
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, LegalPageViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var legalPage = await _legalPageRepository.GetByIdAsync(id);
                if (legalPage == null)
                {
                    return NotFound();
                }

                legalPage.Title = model.Title;
                legalPage.Slug = model.Slug.ToLower().Replace(" ", "-");
                legalPage.MetaDescription = model.MetaDescription;
                legalPage.Content = model.Content;
                legalPage.IsActive = model.IsActive;
                legalPage.LastModified = DateTime.UtcNow;
                legalPage.ModifiedBy = User.Identity?.Name;

                var sections = model.Sections?.Select(s => new LegalPageSection
                {
                    Id = s.Id,
                    LegalPageId = legalPage.Id,
                    Title = s.Title,
                    Content = s.Content,
                    IconClass = s.IconClass,
                    DisplayOrder = s.DisplayOrder,
                    IsActive = s.IsActive
                }).ToList() ?? new List<LegalPageSection>();

                await _legalPageRepository.UpdateWithSectionsAsync(legalPage, sections);

                TempData["SuccessMessage"] = "Legal page updated successfully.";
                return RedirectToAction(nameof(Index));
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var legalPage = await _legalPageRepository.GetByIdAsync(id);
            if (legalPage == null)
            {
                return NotFound();
            }

            await _legalPageRepository.DeleteAsync(legalPage);
            TempData["SuccessMessage"] = "Legal page deleted successfully.";
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var legalPage = await _legalPageRepository.GetByIdAsync(id);
            if (legalPage == null)
            {
                return Json(new { success = false, message = "Legal page not found." });
            }

            legalPage.IsActive = !legalPage.IsActive;
            legalPage.LastModified = DateTime.UtcNow;
            legalPage.ModifiedBy = User.Identity?.Name;
            await _legalPageRepository.UpdateAsync(legalPage);

            return Json(new { success = true, isActive = legalPage.IsActive });
        }
    }
}
