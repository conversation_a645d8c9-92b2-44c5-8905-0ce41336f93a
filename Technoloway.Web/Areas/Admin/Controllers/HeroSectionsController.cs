using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Infrastructure.Data;
using Technoloway.Core.Entities;
using Technoloway.Web.Areas.Admin.Models;

namespace Technoloway.Web.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "SuperAdmin,ContentManager")]
    public class HeroSectionsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public HeroSectionsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Admin/HeroSections
        public async Task<IActionResult> Index()
        {
            var heroSections = await _context.HeroSections
                .Include(h => h.Slides)
                .OrderByDescending(h => h.CreatedAt)
                .ToListAsync();

            return View(heroSections);
        }

        // GET: Admin/HeroSections/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var heroSection = await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

            if (heroSection == null)
            {
                return NotFound();
            }

            var viewModel = new HeroSectionViewModel
            {
                Id = heroSection.Id,
                Title = heroSection.Title,
                PageName = heroSection.PageName,
                MetaDescription = heroSection.MetaDescription,
                MetaKeywords = heroSection.MetaKeywords,
                MainTitle = heroSection.MainTitle,
                MainSubtitle = heroSection.MainSubtitle,
                MainDescription = heroSection.MainDescription,
                PrimaryButtonText = heroSection.PrimaryButtonText,
                PrimaryButtonUrl = heroSection.PrimaryButtonUrl,
                SecondaryButtonText = heroSection.SecondaryButtonText,
                SecondaryButtonUrl = heroSection.SecondaryButtonUrl,
                EnableSlideshow = heroSection.EnableSlideshow,
                SlideshowSpeed = heroSection.SlideshowSpeed,
                AutoPlay = heroSection.AutoPlay,
                ShowDots = heroSection.ShowDots,
                ShowArrows = heroSection.ShowArrows,
                EnableFloatingElements = heroSection.EnableFloatingElements,
                FloatingElementsConfig = heroSection.FloatingElementsConfig,
                IsActive = heroSection.IsActive,
                Slides = heroSection.Slides.Select(s => new HeroSlideViewModel
                {
                    Id = s.Id,
                    Content = s.Content,
                    MediaType = s.MediaType,
                    ImageUrl = s.ImageUrl,
                    VideoUrl = s.VideoUrl,
                    MediaAlt = s.MediaAlt,
                    VideoAutoPlay = s.VideoAutoPlay,
                    VideoMuted = s.VideoMuted,
                    VideoLoop = s.VideoLoop,
                    VideoControls = s.VideoControls,
                    ButtonText = s.ButtonText,
                    ButtonUrl = s.ButtonUrl,
                    DisplayOrder = s.DisplayOrder,
                    IsActive = s.IsActive,
                    AnimationType = s.AnimationType,
                    Duration = s.Duration
                }).ToList()
            };

            return View(viewModel);
        }

        // GET: Admin/HeroSections/Create
        public IActionResult Create()
        {
            var viewModel = new HeroSectionViewModel();
            return View(viewModel);
        }

        // POST: Admin/HeroSections/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(HeroSectionViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var heroSection = new HeroSection
                {
                    Title = viewModel.Title,
                    PageName = viewModel.PageName,
                    MetaDescription = viewModel.MetaDescription,
                    MetaKeywords = viewModel.MetaKeywords,
                    MainTitle = viewModel.MainTitle,
                    MainSubtitle = viewModel.MainSubtitle,
                    MainDescription = viewModel.MainDescription,
                    PrimaryButtonText = viewModel.PrimaryButtonText,
                    PrimaryButtonUrl = viewModel.PrimaryButtonUrl,
                    SecondaryButtonText = viewModel.SecondaryButtonText,
                    SecondaryButtonUrl = viewModel.SecondaryButtonUrl,
                    EnableSlideshow = viewModel.EnableSlideshow,
                    SlideshowSpeed = viewModel.SlideshowSpeed,
                    AutoPlay = viewModel.AutoPlay,
                    ShowDots = viewModel.ShowDots,
                    ShowArrows = viewModel.ShowArrows,
                    EnableFloatingElements = viewModel.EnableFloatingElements,
                    FloatingElementsConfig = viewModel.FloatingElementsConfig,
                    IsActive = viewModel.IsActive,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = User.Identity?.Name ?? "Unknown"
                };

                _context.Add(heroSection);
                await _context.SaveChangesAsync();

                // Add slides if any
                if (viewModel.Slides != null && viewModel.Slides.Any())
                {
                    foreach (var slideViewModel in viewModel.Slides)
                    {
                        var newSlide = new HeroSlide
                        {
                            HeroSectionId = heroSection.Id,
                            Content = slideViewModel.Content,
                            MediaType = slideViewModel.MediaType,
                            ImageUrl = slideViewModel.ImageUrl,
                            VideoUrl = slideViewModel.VideoUrl,
                            MediaAlt = slideViewModel.MediaAlt,
                            VideoAutoPlay = slideViewModel.VideoAutoPlay,
                            VideoMuted = slideViewModel.VideoMuted,
                            VideoLoop = slideViewModel.VideoLoop,
                            VideoControls = slideViewModel.VideoControls,
                            ButtonText = slideViewModel.ButtonText,
                            ButtonUrl = slideViewModel.ButtonUrl,
                            DisplayOrder = slideViewModel.DisplayOrder,
                            IsActive = slideViewModel.IsActive,
                            AnimationType = slideViewModel.AnimationType,
                            Duration = slideViewModel.Duration
                        };

                        _context.HeroSlides.Add(newSlide);
                    }
                    await _context.SaveChangesAsync();
                }

                TempData["SuccessMessage"] = "Hero section created successfully.";
                return RedirectToAction(nameof(Index));
            }

            return View(viewModel);
        }

        // GET: Admin/HeroSections/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var heroSection = await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(h => h.Id == id && !h.IsDeleted);

            if (heroSection == null)
            {
                return NotFound();
            }

            var viewModel = new HeroSectionViewModel
            {
                Id = heroSection.Id,
                Title = heroSection.Title,
                PageName = heroSection.PageName,
                MetaDescription = heroSection.MetaDescription,
                MetaKeywords = heroSection.MetaKeywords,
                MainTitle = heroSection.MainTitle,
                MainSubtitle = heroSection.MainSubtitle,
                MainDescription = heroSection.MainDescription,
                PrimaryButtonText = heroSection.PrimaryButtonText,
                PrimaryButtonUrl = heroSection.PrimaryButtonUrl,
                SecondaryButtonText = heroSection.SecondaryButtonText,
                SecondaryButtonUrl = heroSection.SecondaryButtonUrl,
                EnableSlideshow = heroSection.EnableSlideshow,
                SlideshowSpeed = heroSection.SlideshowSpeed,
                AutoPlay = heroSection.AutoPlay,
                ShowDots = heroSection.ShowDots,
                ShowArrows = heroSection.ShowArrows,
                EnableFloatingElements = heroSection.EnableFloatingElements,
                FloatingElementsConfig = heroSection.FloatingElementsConfig,
                IsActive = heroSection.IsActive,
                Slides = heroSection.Slides.Select(s => new HeroSlideViewModel
                {
                    Id = s.Id,
                    Content = s.Content,
                    MediaType = s.MediaType,
                    ImageUrl = s.ImageUrl,
                    VideoUrl = s.VideoUrl,
                    MediaAlt = s.MediaAlt,
                    VideoAutoPlay = s.VideoAutoPlay,
                    VideoMuted = s.VideoMuted,
                    VideoLoop = s.VideoLoop,
                    VideoControls = s.VideoControls,
                    ButtonText = s.ButtonText,
                    ButtonUrl = s.ButtonUrl,
                    DisplayOrder = s.DisplayOrder,
                    IsActive = s.IsActive,
                    AnimationType = s.AnimationType,
                    Duration = s.Duration
                }).ToList()
            };

            return View(viewModel);
        }

        // POST: Admin/HeroSections/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, HeroSectionViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var heroSection = await _context.HeroSections
                        .Include(h => h.Slides)
                        .FirstOrDefaultAsync(h => h.Id == id && !h.IsDeleted);

                    if (heroSection == null)
                    {
                        return NotFound();
                    }

                    // Update hero section properties
                    heroSection.Title = viewModel.Title;
                    heroSection.PageName = viewModel.PageName;
                    heroSection.MetaDescription = viewModel.MetaDescription;
                    heroSection.MetaKeywords = viewModel.MetaKeywords;
                    heroSection.MainTitle = viewModel.MainTitle;
                    heroSection.MainSubtitle = viewModel.MainSubtitle;
                    heroSection.MainDescription = viewModel.MainDescription;
                    heroSection.PrimaryButtonText = viewModel.PrimaryButtonText;
                    heroSection.PrimaryButtonUrl = viewModel.PrimaryButtonUrl;
                    heroSection.SecondaryButtonText = viewModel.SecondaryButtonText;
                    heroSection.SecondaryButtonUrl = viewModel.SecondaryButtonUrl;
                    heroSection.EnableSlideshow = viewModel.EnableSlideshow;
                    heroSection.SlideshowSpeed = viewModel.SlideshowSpeed;
                    heroSection.AutoPlay = viewModel.AutoPlay;
                    heroSection.ShowDots = viewModel.ShowDots;
                    heroSection.ShowArrows = viewModel.ShowArrows;
                    heroSection.EnableFloatingElements = viewModel.EnableFloatingElements;
                    heroSection.FloatingElementsConfig = viewModel.FloatingElementsConfig;
                    heroSection.IsActive = viewModel.IsActive;
                    heroSection.LastModified = DateTime.UtcNow;
                    heroSection.ModifiedBy = User.Identity?.Name ?? "Unknown";

                    // Handle slides
                    UpdateSlides(heroSection, viewModel.Slides);

                    _context.Update(heroSection);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "Hero section updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!HeroSectionExists(viewModel.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            return View(viewModel);
        }

        // GET: Admin/HeroSections/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var heroSection = await _context.HeroSections
                .Include(h => h.Slides)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

            if (heroSection == null)
            {
                return NotFound();
            }

            return View(heroSection);
        }

        // POST: Admin/HeroSections/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var heroSection = await _context.HeroSections
                .Include(h => h.Slides)
                .FirstOrDefaultAsync(h => h.Id == id);

            if (heroSection != null)
            {
                // Hard delete - permanently remove from database
                // First remove all associated slides
                if (heroSection.Slides.Any())
                {
                    _context.HeroSlides.RemoveRange(heroSection.Slides);
                }

                // Then remove the hero section
                _context.HeroSections.Remove(heroSection);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Hero section permanently deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool HeroSectionExists(int id)
        {
            return _context.HeroSections.Any(e => e.Id == id && !e.IsDeleted);
        }

        private void UpdateSlides(HeroSection heroSection, List<HeroSlideViewModel> slideViewModels)
        {
            // Get existing slides
            var existingSlides = heroSection.Slides.Where(s => !s.IsDeleted).ToList();
            var slideViewModelIds = slideViewModels.Where(s => s.Id > 0).Select(s => s.Id).ToList();

            // Mark slides for deletion if they're not in the view model
            foreach (var existingSlide in existingSlides)
            {
                if (!slideViewModelIds.Contains(existingSlide.Id))
                {
                    existingSlide.IsDeleted = true;
                }
            }

            // Update or add slides
            foreach (var slideViewModel in slideViewModels)
            {
                if (slideViewModel.Id > 0)
                {
                    // Update existing slide
                    var existingSlide = existingSlides.FirstOrDefault(s => s.Id == slideViewModel.Id);
                    if (existingSlide != null)
                    {
                        existingSlide.Content = slideViewModel.Content;
                        existingSlide.MediaType = slideViewModel.MediaType;
                        existingSlide.ImageUrl = slideViewModel.ImageUrl;
                        existingSlide.VideoUrl = slideViewModel.VideoUrl;
                        existingSlide.MediaAlt = slideViewModel.MediaAlt;
                        existingSlide.VideoAutoPlay = slideViewModel.VideoAutoPlay;
                        existingSlide.VideoMuted = slideViewModel.VideoMuted;
                        existingSlide.VideoLoop = slideViewModel.VideoLoop;
                        existingSlide.VideoControls = slideViewModel.VideoControls;
                        existingSlide.ButtonText = slideViewModel.ButtonText;
                        existingSlide.ButtonUrl = slideViewModel.ButtonUrl;
                        existingSlide.DisplayOrder = slideViewModel.DisplayOrder;
                        existingSlide.IsActive = slideViewModel.IsActive;
                        existingSlide.AnimationType = slideViewModel.AnimationType;
                        existingSlide.Duration = slideViewModel.Duration;
                        existingSlide.UpdatedAt = DateTime.UtcNow;
                    }
                }
                else
                {
                    // Add new slide
                    var newSlide = new HeroSlide
                    {
                        HeroSectionId = heroSection.Id,
                        Content = slideViewModel.Content,
                        MediaType = slideViewModel.MediaType,
                        ImageUrl = slideViewModel.ImageUrl,
                        VideoUrl = slideViewModel.VideoUrl,
                        MediaAlt = slideViewModel.MediaAlt,
                        VideoAutoPlay = slideViewModel.VideoAutoPlay,
                        VideoMuted = slideViewModel.VideoMuted,
                        VideoLoop = slideViewModel.VideoLoop,
                        VideoControls = slideViewModel.VideoControls,
                        ButtonText = slideViewModel.ButtonText,
                        ButtonUrl = slideViewModel.ButtonUrl,
                        DisplayOrder = slideViewModel.DisplayOrder,
                        IsActive = slideViewModel.IsActive,
                        AnimationType = slideViewModel.AnimationType,
                        Duration = slideViewModel.Duration
                    };

                    heroSection.Slides.Add(newSlide);
                }
            }
        }

        // POST: Admin/HeroSections/ToggleActive/5
        [HttpPost]
        public async Task<IActionResult> ToggleActive(int id)
        {
            try
            {
                var heroSection = await _context.HeroSections.FindAsync(id);
                if (heroSection == null || heroSection.IsDeleted)
                {
                    return Json(new { success = false, message = "Hero section not found" });
                }

                heroSection.IsActive = !heroSection.IsActive;
                heroSection.LastModified = DateTime.UtcNow;
                heroSection.ModifiedBy = User.Identity?.Name ?? "Unknown";

                _context.Update(heroSection);
                await _context.SaveChangesAsync();

                return Json(new { success = true, isActive = heroSection.IsActive });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating hero section status" });
            }
        }

        // GET: Admin/HeroSections/Slides/5
        public async Task<IActionResult> Slides(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var heroSection = await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(h => h.Id == id && !h.IsDeleted);

            if (heroSection == null)
            {
                return NotFound();
            }

            ViewBag.HeroSectionTitle = heroSection.Title;
            ViewBag.HeroSectionId = heroSection.Id;
            return View(heroSection.Slides.Where(s => !s.IsDeleted).ToList());
        }

        // POST: Admin/HeroSections/ToggleSlideActive/5
        [HttpPost]
        public async Task<IActionResult> ToggleSlideActive(int id)
        {
            try
            {
                var slide = await _context.HeroSlides.FindAsync(id);
                if (slide == null || slide.IsDeleted)
                {
                    return Json(new { success = false, message = "Slide not found" });
                }

                slide.IsActive = !slide.IsActive;

                _context.Update(slide);
                await _context.SaveChangesAsync();

                return Json(new { success = true, isActive = slide.IsActive });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating slide status" });
            }
        }

        // POST: Admin/HeroSections/DeleteSlide/5
        [HttpPost]
        public async Task<IActionResult> DeleteSlide(int id)
        {
            try
            {
                var slide = await _context.HeroSlides.FindAsync(id);
                if (slide == null || slide.IsDeleted)
                {
                    return Json(new { success = false, message = "Slide not found" });
                }

                // Hard delete - permanently remove from database
                _context.HeroSlides.Remove(slide);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Slide deleted successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error deleting slide" });
            }
        }

        // GET: Admin/HeroSections/EditSlide/5
        public async Task<IActionResult> EditSlide(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var slide = await _context.HeroSlides
                .Include(s => s.HeroSection)
                .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

            if (slide == null)
            {
                return NotFound();
            }

            return View(slide);
        }
    }
}
