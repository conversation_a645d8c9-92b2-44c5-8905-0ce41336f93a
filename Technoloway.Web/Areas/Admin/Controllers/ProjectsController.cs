using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class ProjectsController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Technoloway.Core.Entities.Client> _clientRepository;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IRepository<ProjectDocument> _documentRepository;
    private readonly IRepository<Message> _messageRepository;
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IFileUploadService _fileUploadService;

    public ProjectsController(
        IRepository<Project> projectRepository,
        IRepository<Technoloway.Core.Entities.Client> clientRepository,
        IRepository<Service> serviceRepository,
        IRepository<Technology> technologyRepository,
        IRepository<ProjectDocument> documentRepository,
        IRepository<Message> messageRepository,
        IRepository<Invoice> invoiceRepository,
        UserManager<IdentityUser> userManager,
        IFileUploadService fileUploadService)
    {
        _projectRepository = projectRepository;
        _clientRepository = clientRepository;
        _serviceRepository = serviceRepository;
        _technologyRepository = technologyRepository;
        _documentRepository = documentRepository;
        _messageRepository = messageRepository;
        _invoiceRepository = invoiceRepository;
        _userManager = userManager;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var projects = await _projectRepository.GetAll()
            .Where(p => !p.IsDeleted)
            .Include(p => p.Client)
            .Include(p => p.Service)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return View(projects);
    }

    public async Task<IActionResult> Create()
    {
        await PopulateDropdowns();

        var viewModel = new ProjectViewModel();
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ProjectViewModel viewModel, int[] selectedTechnologies)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.ImageFile != null && viewModel.ImageFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.ImageFile))
                {
                    ModelState.AddModelError("ImageFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.ImageUrl = await _fileUploadService.UploadImageAsync(viewModel.ImageFile, "projects");
                }
            }

            // Additional validation
            if (viewModel.ServiceId <= 0)
            {
                ModelState.AddModelError("ServiceId", "Please select a service.");
            }

            if (ModelState.IsValid)
            {
                var project = viewModel.ToEntity();

                // Set client name if client is selected
                if (project.ClientId.HasValue && project.ClientId.Value > 0)
                {
                    var client = await _clientRepository.GetByIdAsync(project.ClientId.Value);
                    if (client != null)
                    {
                        project.ClientName = client.CompanyName;
                    }
                }
                else
                {
                    project.ClientName = string.Empty;
                    project.ClientId = null;
                }

                // Ensure completion date is set
                if (project.CompletionDate == default(DateTime))
                {
                    project.CompletionDate = DateTime.UtcNow.AddMonths(1);
                }

                // Set timestamps
                project.CreatedAt = DateTime.UtcNow;
                project.UpdatedAt = DateTime.UtcNow;

                // Add the project using repository
                var createdProject = await _projectRepository.AddAsync(project);

                // Add selected technologies
                if (selectedTechnologies != null && selectedTechnologies.Length > 0)
                {
                    var technologies = await _technologyRepository.ListAsync(t => selectedTechnologies.Contains(t.Id));

                    // Load the project with technologies to update the relationship
                    var projectWithTechnologies = await _projectRepository.GetAll()
                        .Include(p => p.Technologies)
                        .FirstOrDefaultAsync(p => p.Id == createdProject.Id);

                    if (projectWithTechnologies != null)
                    {
                        foreach (var technology in technologies)
                        {
                            projectWithTechnologies.Technologies.Add(technology);
                        }
                        await _projectRepository.UpdateAsync(projectWithTechnologies);
                    }
                }

                TempData["SuccessMessage"] = "Project created successfully!";
                return RedirectToAction(nameof(Details), new { id = createdProject.Id });
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating project: {ex.Message}");
        }

        await PopulateDropdowns();
        return View(viewModel);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var project = await _projectRepository.GetAll()
            .Include(p => p.Technologies)
            .Include(p => p.Client)
            .Include(p => p.Service)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (project == null)
        {
            return NotFound();
        }

        var viewModel = ProjectViewModel.FromEntity(project);
        await PopulateDropdowns(project.ClientId, project.ServiceId);
        ViewBag.SelectedTechnologies = project.Technologies.Select(t => t.Id).ToArray();

        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, ProjectViewModel viewModel, int[] selectedTechnologies)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingProject = await _projectRepository.GetAll()
                .Include(p => p.Technologies)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (existingProject == null)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.ImageFile != null && viewModel.ImageFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.ImageFile))
                {
                    ModelState.AddModelError("ImageFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingProject.ImageUrl) && existingProject.ImageUrl.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingProject.ImageUrl);
                    }

                    viewModel.ImageUrl = await _fileUploadService.UploadImageAsync(viewModel.ImageFile, "projects", oldFileName);
                }
            }
            else
            {
                // Keep existing image if no new file uploaded
                viewModel.ImageUrl = existingProject.ImageUrl;
            }

            // Validate required fields
            if (viewModel.ServiceId <= 0)
            {
                ModelState.AddModelError("ServiceId", "Please select a service.");
            }

            if (ModelState.IsValid)
            {
                // Set client name if client is selected
                if (viewModel.ClientId.HasValue)
                {
                    var client = await _clientRepository.GetByIdAsync(viewModel.ClientId.Value);
                    if (client != null)
                    {
                        viewModel.ClientName = client.CompanyName;
                    }
                }
                else
                {
                    viewModel.ClientName = string.Empty;
                }

                // Update project properties
                viewModel.UpdateEntity(existingProject);
                existingProject.UpdatedAt = DateTime.UtcNow;

                // Update technologies
                existingProject.Technologies.Clear();
                if (selectedTechnologies != null && selectedTechnologies.Length > 0)
                {
                    var technologies = await _technologyRepository.ListAsync(t => selectedTechnologies.Contains(t.Id));
                    foreach (var technology in technologies)
                    {
                        existingProject.Technologies.Add(technology);
                    }
                }

                // Save changes using repository
                await _projectRepository.UpdateAsync(existingProject);

                TempData["SuccessMessage"] = "Project updated successfully!";
                return RedirectToAction(nameof(Details), new { id = viewModel.Id });
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating project: {ex.Message}");
        }

        await PopulateDropdowns(viewModel.ClientId, viewModel.ServiceId);
        ViewBag.SelectedTechnologies = selectedTechnologies;

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var project = await _projectRepository.GetAll()
            .Include(p => p.Client)
            .Include(p => p.Service)
            .Include(p => p.Technologies)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (project == null)
        {
            return NotFound();
        }

        var documents = await _documentRepository.ListAsync(d => d.ProjectId == id);
        var messages = await _messageRepository.ListAsync(m => m.ProjectId == id);
        var invoices = await _invoiceRepository.ListAsync(i => i.ProjectId == id);

        ViewBag.Documents = documents;
        ViewBag.Messages = messages;
        ViewBag.Invoices = invoices;
        ViewBag.RecentInvoices = invoices.AsEnumerable().OrderByDescending(i => i.IssueDate).Take(5).ToList();
        ViewBag.RecentDocuments = documents.AsEnumerable().Take(5).ToList();
        ViewBag.DocumentCount = documents.Count;
        ViewBag.MessageCount = messages.Count;
        ViewBag.InvoiceCount = invoices.Count;
        ViewBag.TotalInvoiceAmount = invoices.Sum(i => i.TotalAmount);
        ViewBag.PaidInvoiceAmount = invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount);

        return View(project);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var project = await _projectRepository.GetAll()
            .Include(p => p.Client)
            .Include(p => p.Service)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (project == null)
        {
            return NotFound();
        }

        var documents = await _documentRepository.ListAsync(d => d.ProjectId == id);
        var messages = await _messageRepository.ListAsync(m => m.ProjectId == id);
        var invoices = await _invoiceRepository.ListAsync(i => i.ProjectId == id);

        ViewBag.DocumentCount = documents.Count;
        ViewBag.MessageCount = messages.Count;
        ViewBag.InvoiceCount = invoices.Count;

        return View(project);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        if (project == null)
        {
            return NotFound();
        }

        // Soft delete
        project.IsDeleted = true;
        project.UpdatedAt = DateTime.UtcNow;
        await _projectRepository.UpdateAsync(project);

        return RedirectToAction(nameof(Index));
    }

    public async Task<IActionResult> Documents(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        if (project == null)
        {
            return NotFound();
        }

        var documents = await _documentRepository.ListAsync(d => d.ProjectId == id);
        ViewBag.Project = project;

        return View(documents);
    }

    public async Task<IActionResult> AddDocument(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        if (project == null)
        {
            return NotFound();
        }

        ViewBag.Project = project;
        return View(new Technoloway.Core.Entities.ProjectDocument { ProjectId = id });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddDocument(Technoloway.Core.Entities.ProjectDocument document)
    {
        if (ModelState.IsValid)
        {
            // Set creation date
            document.CreatedAt = DateTime.UtcNow;
            document.UpdatedAt = DateTime.UtcNow;

            // Set uploaded by info
            var user = await _userManager.GetUserAsync(User);
            if (user != null)
            {
                document.UploadedById = user.Id;
                document.UploadedByName = user.UserName;
            }

            await _documentRepository.AddAsync(document);
            return RedirectToAction(nameof(Documents), new { id = document.ProjectId });
        }

        var project = await _projectRepository.GetByIdAsync(document.ProjectId);
        ViewBag.Project = project;

        return View(document);
    }

    public async Task<IActionResult> Messages(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        if (project == null)
        {
            return NotFound();
        }

        var messages = await _messageRepository.ListAsync(m => m.ProjectId == id);
        ViewBag.Project = project;

        return View(messages);
    }

    public async Task<IActionResult> AddMessage(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        if (project == null)
        {
            return NotFound();
        }

        ViewBag.Project = project;
        return View(new Technoloway.Core.Entities.Message { ProjectId = id });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddMessage(Technoloway.Core.Entities.Message message)
    {
        if (ModelState.IsValid)
        {
            // Set creation date
            message.CreatedAt = DateTime.UtcNow;
            message.UpdatedAt = DateTime.UtcNow;

            // Set sender info
            var user = await _userManager.GetUserAsync(User);
            if (user != null)
            {
                message.SenderId = user.Id;
                message.SenderName = user.UserName;
                message.SenderRole = "Admin";
            }

            await _messageRepository.AddAsync(message);
            return RedirectToAction(nameof(Messages), new { id = message.ProjectId });
        }

        var project = await _projectRepository.GetByIdAsync(message.ProjectId);
        ViewBag.Project = project;

        return View(message);
    }

    private async Task<bool> ProjectExists(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        return project != null;
    }

    private async Task PopulateDropdowns(int? selectedClientId = null, int? selectedServiceId = null)
    {
        var clients = await _clientRepository.ListAllAsync();
        ViewBag.Clients = new SelectList(clients, "Id", "CompanyName", selectedClientId);

        var services = await _serviceRepository.ListAllAsync();
        ViewBag.Services = new SelectList(services, "Id", "Name", selectedServiceId);

        var technologies = await _technologyRepository.ListAllAsync();
        ViewBag.Technologies = technologies;
    }
}
