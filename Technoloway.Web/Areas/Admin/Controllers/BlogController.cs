using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
public class BlogController : Controller
{
    private readonly IRepository<BlogPost> _blogPostRepository;
    private readonly IFileUploadService _fileUploadService;

    public BlogController(IRepository<BlogPost> blogPostRepository, IFileUploadService fileUploadService)
    {
        _blogPostRepository = blogPostRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var posts = await _blogPostRepository.GetAllAsNoTracking()
            .Where(p => !p.IsDeleted)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return View(posts);
    }

    public IActionResult Create()
    {
        var viewModel = new BlogPostViewModel();
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(BlogPostViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.FeaturedImageFile != null && viewModel.FeaturedImageFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.FeaturedImageFile))
                {
                    ModelState.AddModelError("FeaturedImageFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.FeaturedImageUrl = await _fileUploadService.UploadImageAsync(viewModel.FeaturedImageFile, "blog");
                }
            }

            if (ModelState.IsValid)
            {
                var blogPost = viewModel.ToEntity();

                // Set creation date and other defaults
                blogPost.CreatedAt = DateTime.UtcNow;
                blogPost.UpdatedAt = DateTime.UtcNow;

                // If published, set the published date
                if (blogPost.IsPublished)
                {
                    blogPost.PublishedAt = DateTime.UtcNow;
                }

                // Generate slug if not provided
                if (string.IsNullOrEmpty(blogPost.Slug))
                {
                    blogPost.Slug = GenerateSlug(blogPost.Title);
                }

                await _blogPostRepository.AddAsync(blogPost);
                TempData["SuccessMessage"] = "Blog post created successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating blog post: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var blogPost = await _blogPostRepository.GetByIdAsync(id);
        if (blogPost == null)
        {
            return NotFound();
        }

        var viewModel = BlogPostViewModel.FromEntity(blogPost);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, BlogPostViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingBlogPost = await _blogPostRepository.GetByIdAsync(id);
            if (existingBlogPost == null)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.FeaturedImageFile != null && viewModel.FeaturedImageFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.FeaturedImageFile))
                {
                    ModelState.AddModelError("FeaturedImageFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingBlogPost.FeaturedImageUrl) && existingBlogPost.FeaturedImageUrl.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingBlogPost.FeaturedImageUrl);
                    }

                    viewModel.FeaturedImageUrl = await _fileUploadService.UploadImageAsync(viewModel.FeaturedImageFile, "blog", oldFileName);
                }
            }
            else
            {
                // Keep existing featured image if no new file uploaded
                viewModel.FeaturedImageUrl = existingBlogPost.FeaturedImageUrl;
            }

            if (ModelState.IsValid)
            {
                // Update properties
                viewModel.UpdateEntity(existingBlogPost);
                existingBlogPost.UpdatedAt = DateTime.UtcNow;

                // Check if publishing status changed
                if (!existingBlogPost.IsPublished && viewModel.IsPublished)
                {
                    existingBlogPost.PublishedAt = DateTime.UtcNow;
                }
                else if (existingBlogPost.IsPublished && viewModel.IsPublished)
                {
                    // Keep the original published date if already published
                    // (already preserved in existingBlogPost)
                }

                // Generate slug if not provided
                if (string.IsNullOrEmpty(existingBlogPost.Slug))
                {
                    existingBlogPost.Slug = GenerateSlug(existingBlogPost.Title);
                }

                await _blogPostRepository.UpdateAsync(existingBlogPost);
                TempData["SuccessMessage"] = "Blog post updated successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await BlogPostExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating blog post: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var blogPost = await _blogPostRepository.GetByIdAsync(id);
        if (blogPost == null)
        {
            return NotFound();
        }

        return View(blogPost);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var blogPost = await _blogPostRepository.GetByIdAsync(id);
        if (blogPost == null)
        {
            return NotFound();
        }

        return View(blogPost);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var blogPost = await _blogPostRepository.GetByIdAsync(id);
        if (blogPost == null)
        {
            return NotFound();
        }

        // Soft delete
        blogPost.IsDeleted = true;
        blogPost.UpdatedAt = DateTime.UtcNow;
        await _blogPostRepository.UpdateAsync(blogPost);

        return RedirectToAction(nameof(Index));
    }

    private async Task<bool> BlogPostExists(int id)
    {
        return await _blogPostRepository.GetAllAsNoTracking()
            .AnyAsync(p => p.Id == id && !p.IsDeleted);
    }

    private string GenerateSlug(string title)
    {
        // Convert to lowercase
        var slug = title.ToLowerInvariant();

        // Remove special characters
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"[^a-z0-9\s-]", "");

        // Replace spaces with hyphens
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"\s+", "-");

        // Remove multiple hyphens
        slug = System.Text.RegularExpressions.Regex.Replace(slug, @"-+", "-");

        // Trim hyphens from start and end
        slug = slug.Trim('-');

        return slug;
    }
}
