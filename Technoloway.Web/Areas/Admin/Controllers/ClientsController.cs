using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Areas.Admin.ViewModels;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class ClientsController : Controller
{
    private readonly IRepository<Technoloway.Core.Entities.Client> _clientRepository;
    private readonly IRepository<Technoloway.Core.Entities.Project> _projectRepository;
    private readonly IRepository<Technoloway.Core.Entities.Invoice> _invoiceRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IFileUploadService _fileUploadService;

    public ClientsController(
        IRepository<Technoloway.Core.Entities.Client> clientRepository,
        IRepository<Technoloway.Core.Entities.Project> projectRepository,
        IRepository<Technoloway.Core.Entities.Invoice> invoiceRepository,
        UserManager<IdentityUser> userManager,
        IFileUploadService fileUploadService)
    {
        _clientRepository = clientRepository;
        _projectRepository = projectRepository;
        _invoiceRepository = invoiceRepository;
        _userManager = userManager;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var clients = await _clientRepository.GetAll()
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();

        return View(clients);
    }

    public IActionResult Create()
    {
        var viewModel = new ClientViewModel();
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ClientViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.LogoFile != null && viewModel.LogoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.LogoFile))
                {
                    ModelState.AddModelError("LogoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.LogoUrl = await _fileUploadService.UploadImageAsync(viewModel.LogoFile, "clients");
                }
            }

            if (ModelState.IsValid)
            {
                var client = viewModel.ToEntity();
                client.CreatedAt = DateTime.UtcNow;
                client.UpdatedAt = DateTime.UtcNow;

                await _clientRepository.AddAsync(client);
                TempData["SuccessMessage"] = "Client created successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating client: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        var viewModel = ClientViewModel.FromEntity(client);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, ClientViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingClient = await _clientRepository.GetByIdAsync(id);
            if (existingClient == null)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.LogoFile != null && viewModel.LogoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.LogoFile))
                {
                    ModelState.AddModelError("LogoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingClient.LogoUrl) && existingClient.LogoUrl.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingClient.LogoUrl);
                    }

                    viewModel.LogoUrl = await _fileUploadService.UploadImageAsync(viewModel.LogoFile, "clients", oldFileName);
                }
            }
            else
            {
                // Keep existing logo if no new file uploaded
                viewModel.LogoUrl = existingClient.LogoUrl;
            }

            if (ModelState.IsValid)
            {
                // Update properties
                viewModel.UpdateEntity(existingClient);
                existingClient.UpdatedAt = DateTime.UtcNow;

                await _clientRepository.UpdateAsync(existingClient);
                TempData["SuccessMessage"] = "Client updated successfully!";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await ClientExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating client: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        var projects = await _projectRepository.ListAsync(p => p.ClientId == id);
        var invoices = await _invoiceRepository.ListAsync(i => i.ClientId == id);

        ViewBag.Projects = projects;
        ViewBag.Invoices = invoices;
        ViewBag.ProjectCount = projects.Count;
        ViewBag.InvoiceCount = invoices.Count;
        ViewBag.PendingInvoiceCount = invoices.Count(i => i.Status == "Pending");
        ViewBag.TotalInvoiceAmount = invoices.Sum(i => i.TotalAmount);
        ViewBag.PaidInvoiceAmount = invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount);

        return View(client);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        var projects = await _projectRepository.ListAsync(p => p.ClientId == id);
        var invoices = await _invoiceRepository.ListAsync(i => i.ClientId == id);

        ViewBag.ProjectCount = projects.Count;
        ViewBag.InvoiceCount = invoices.Count;

        return View(client);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        // Soft delete
        client.IsDeleted = true;
        client.UpdatedAt = DateTime.UtcNow;
        await _clientRepository.UpdateAsync(client);

        return RedirectToAction(nameof(Index));
    }

    public async Task<IActionResult> Projects(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        var projects = await _projectRepository.ListAsync(p => p.ClientId == id);
        ViewBag.Client = client;

        return View(projects);
    }

    public async Task<IActionResult> Invoices(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        if (client == null)
        {
            return NotFound();
        }

        var invoices = await _invoiceRepository.ListAsync(i => i.ClientId == id);
        ViewBag.Client = client;

        return View(invoices);
    }

    public async Task<IActionResult> Reports()
    {
        var clients = await _clientRepository.GetAll()
            .Include(c => c.Projects)
            .Include(c => c.Invoices)
            .Where(c => !c.IsDeleted)
            .ToListAsync();

        // Calculate comprehensive client analytics
        var reports = new ClientReportsViewModel
        {
            // Basic counts
            TotalClients = clients.Count,
            ActiveClients = clients.Count(c => c.Projects.Any(p => !p.IsDeleted)),
            ClientsWithProjects = clients.Count(c => c.Projects.Any()),
            ClientsWithInvoices = clients.Count(c => c.Invoices.Any()),

            // Geographic distribution
            CountryDistribution = GetCountryDistribution(clients),

            // Client value analysis
            TopClientsByRevenue = GetTopClientsByRevenue(clients),
            TopClientsByProjects = GetTopClientsByProjects(clients),

            // Time-based analysis
            MonthlyClientGrowth = GetMonthlyClientGrowth(clients),
            ClientAcquisitionTrend = GetClientAcquisitionTrend(clients),

            // Financial metrics
            TotalClientRevenue = clients.SelectMany(c => c.Invoices).Sum(i => i.TotalAmount),
            AverageRevenuePerClient = clients.Any() ? clients.SelectMany(c => c.Invoices).Sum(i => i.TotalAmount) / clients.Count : 0,
            TotalOutstandingAmount = clients.SelectMany(c => c.Invoices).Where(i => i.Status != "Paid").Sum(i => i.TotalAmount),

            // Project metrics
            TotalProjects = clients.SelectMany(c => c.Projects).Count(p => !p.IsDeleted),
            AverageProjectsPerClient = clients.Any() ? (decimal)clients.SelectMany(c => c.Projects).Count(p => !p.IsDeleted) / clients.Count : 0,

            // Recent activity
            RecentClients = clients.OrderByDescending(c => c.CreatedAt).Take(10).ToList(),

            // Growth metrics
            MonthOverMonthGrowth = CalculateClientMonthOverMonthGrowth(clients),
            YearOverYearGrowth = CalculateClientYearOverYearGrowth(clients)
        };

        return View(reports);
    }

    private List<CountryDistributionData> GetCountryDistribution(List<Core.Entities.Client> clients)
    {
        return clients
            .Where(c => !string.IsNullOrEmpty(c.Country))
            .GroupBy(c => c.Country)
            .Select(g => new CountryDistributionData
            {
                Country = g.Key,
                ClientCount = g.Count(),
                TotalRevenue = g.SelectMany(c => c.Invoices).Sum(i => i.TotalAmount),
                TotalProjects = g.SelectMany(c => c.Projects).Count(p => !p.IsDeleted)
            })
            .OrderByDescending(c => c.ClientCount)
            .ToList();
    }

    private List<ClientRevenueData> GetTopClientsByRevenue(List<Core.Entities.Client> clients)
    {
        return clients
            .Select(c => new ClientRevenueData
            {
                ClientName = c.CompanyName ?? "Unknown Client",
                TotalRevenue = c.Invoices.Sum(i => i.TotalAmount),
                InvoiceCount = c.Invoices.Count,
                PaidAmount = c.Invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount),
                ProjectCount = c.Projects.Count(p => !p.IsDeleted)
            })
            .OrderByDescending(c => c.TotalRevenue)
            .Take(10)
            .ToList();
    }

    private List<ClientProjectData> GetTopClientsByProjects(List<Core.Entities.Client> clients)
    {
        return clients
            .Select(c => new ClientProjectData
            {
                ClientName = c.CompanyName ?? "Unknown Client",
                ProjectCount = c.Projects.Count(p => !p.IsDeleted),
                CompletedProjects = c.Projects.Count(p => !p.IsDeleted && p.CompletionDate <= DateTime.UtcNow),
                TotalRevenue = c.Invoices.Sum(i => i.TotalAmount)
            })
            .OrderByDescending(c => c.ProjectCount)
            .Take(10)
            .ToList();
    }

    private List<MonthlyClientData> GetMonthlyClientGrowth(List<Core.Entities.Client> clients)
    {
        var monthlyData = new List<MonthlyClientData>();
        var startDate = DateTime.Now.AddMonths(-11).Date;

        for (int i = 0; i < 12; i++)
        {
            var month = startDate.AddMonths(i);
            var monthClients = clients.Where(c =>
                c.CreatedAt.Year == month.Year &&
                c.CreatedAt.Month == month.Month).ToList();

            monthlyData.Add(new MonthlyClientData
            {
                Month = month.ToString("MMM yyyy"),
                NewClients = monthClients.Count,
                TotalRevenue = monthClients.SelectMany(c => c.Invoices).Sum(i => i.TotalAmount),
                TotalProjects = monthClients.SelectMany(c => c.Projects).Count(p => !p.IsDeleted)
            });
        }

        return monthlyData;
    }

    private List<ClientAcquisitionData> GetClientAcquisitionTrend(List<Core.Entities.Client> clients)
    {
        var currentYear = DateTime.Now.Year;
        var quarters = new List<ClientAcquisitionData>();

        for (int quarter = 1; quarter <= 4; quarter++)
        {
            var startMonth = (quarter - 1) * 3 + 1;
            var endMonth = quarter * 3;

            var quarterClients = clients.Where(c =>
                c.CreatedAt.Year == currentYear &&
                c.CreatedAt.Month >= startMonth &&
                c.CreatedAt.Month <= endMonth).ToList();

            quarters.Add(new ClientAcquisitionData
            {
                Period = $"Q{quarter} {currentYear}",
                NewClients = quarterClients.Count,
                TotalValue = quarterClients.SelectMany(c => c.Invoices).Sum(i => i.TotalAmount)
            });
        }

        return quarters;
    }

    private decimal CalculateClientMonthOverMonthGrowth(List<Core.Entities.Client> clients)
    {
        var currentMonth = DateTime.Now;
        var lastMonth = currentMonth.AddMonths(-1);

        var currentMonthClients = clients.Count(c =>
            c.CreatedAt.Year == currentMonth.Year &&
            c.CreatedAt.Month == currentMonth.Month);

        var lastMonthClients = clients.Count(c =>
            c.CreatedAt.Year == lastMonth.Year &&
            c.CreatedAt.Month == lastMonth.Month);

        if (lastMonthClients == 0) return 0;
        return ((decimal)(currentMonthClients - lastMonthClients) / lastMonthClients) * 100;
    }

    private decimal CalculateClientYearOverYearGrowth(List<Core.Entities.Client> clients)
    {
        var currentYear = DateTime.Now.Year;
        var lastYear = currentYear - 1;

        var currentYearClients = clients.Count(c => c.CreatedAt.Year == currentYear);
        var lastYearClients = clients.Count(c => c.CreatedAt.Year == lastYear);

        if (lastYearClients == 0) return 0;
        return ((decimal)(currentYearClients - lastYearClients) / lastYearClients) * 100;
    }

    private async Task<bool> ClientExists(int id)
    {
        var client = await _clientRepository.GetByIdAsync(id);
        return client != null;
    }
}
