using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class TechnologiesController : Controller
{
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IFileUploadService _fileUploadService;

    public TechnologiesController(IRepository<Technology> technologyRepository, IFileUploadService fileUploadService)
    {
        _technologyRepository = technologyRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var technologies = await _technologyRepository.GetAll()
            .Where(t => !t.IsDeleted)
            .OrderBy(t => t.DisplayOrder)
            .ThenBy(t => t.Name)
            .ToListAsync();

        return View(technologies);
    }

    public IActionResult Create()
    {
        var viewModel = new TechnologyViewModel
        {
            DisplayOrder = 0
        };
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(TechnologyViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.IconUrl = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "technologies");
                }
            }

            if (ModelState.IsValid)
            {
                var technology = viewModel.ToEntity();
                technology.CreatedAt = DateTime.UtcNow;
                technology.UpdatedAt = DateTime.UtcNow;

                await _technologyRepository.AddAsync(technology);
                TempData["SuccessMessage"] = "Technology created successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating technology: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);
        if (technology == null || technology.IsDeleted)
        {
            return NotFound();
        }

        return View(technology);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);
        if (technology == null || technology.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = TechnologyViewModel.FromEntity(technology);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, TechnologyViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingTechnology = await _technologyRepository.GetByIdAsync(id);
            if (existingTechnology == null || existingTechnology.IsDeleted)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingTechnology.IconUrl))
                    {
                        oldFileName = Path.GetFileName(existingTechnology.IconUrl);
                    }

                    viewModel.IconUrl = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "technologies", oldFileName);
                }
            }
            else
            {
                // Keep existing icon URL if no new file uploaded
                viewModel.IconUrl = existingTechnology.IconUrl;
            }

            if (ModelState.IsValid)
            {
                viewModel.UpdateEntity(existingTechnology);
                existingTechnology.UpdatedAt = DateTime.UtcNow;

                await _technologyRepository.UpdateAsync(existingTechnology);
                TempData["SuccessMessage"] = "Technology updated successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await TechnologyExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating technology: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);
        if (technology == null || technology.IsDeleted)
        {
            return NotFound();
        }

        return View(technology);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);
        if (technology == null)
        {
            return NotFound();
        }

        // Soft delete
        await _technologyRepository.DeleteAsync(technology);
        TempData["SuccessMessage"] = "Technology deleted successfully.";

        return RedirectToAction(nameof(Index));
    }

    private async Task<bool> TechnologyExists(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);
        return technology != null && !technology.IsDeleted;
    }
}
