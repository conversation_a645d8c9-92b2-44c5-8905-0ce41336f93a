using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class TeamMembersController : Controller
{
    private readonly IRepository<TeamMember> _teamMemberRepository;
    private readonly IFileUploadService _fileUploadService;

    public TeamMembersController(IRepository<TeamMember> teamMemberRepository, IFileUploadService fileUploadService)
    {
        _teamMemberRepository = teamMemberRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var teamMembers = await _teamMemberRepository.GetAll()
            .Where(t => !t.IsDeleted)
            .OrderBy(t => t.DisplayOrder)
            .ThenBy(t => t.Name)
            .ToListAsync();

        return View(teamMembers);
    }

    public IActionResult Create()
    {
        var viewModel = new TeamMemberViewModel
        {
            DisplayOrder = 0,
            IsActive = true
        };
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(TeamMemberViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.PhotoFile != null && viewModel.PhotoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.PhotoFile))
                {
                    ModelState.AddModelError("PhotoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.PhotoUrl = await _fileUploadService.UploadImageAsync(viewModel.PhotoFile, "team");
                }
            }

            if (ModelState.IsValid)
            {
                var teamMember = viewModel.ToEntity();
                teamMember.CreatedAt = DateTime.UtcNow;
                teamMember.UpdatedAt = DateTime.UtcNow;

                // Set display order if not provided
                if (teamMember.DisplayOrder == 0)
                {
                    var maxOrder = await _teamMemberRepository.GetAll()
                        .Where(t => !t.IsDeleted)
                        .MaxAsync(t => (int?)t.DisplayOrder) ?? 0;
                    teamMember.DisplayOrder = maxOrder + 1;
                }

                await _teamMemberRepository.AddAsync(teamMember);
                TempData["SuccessMessage"] = "Team member created successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating team member: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        if (teamMember == null || teamMember.IsDeleted)
        {
            return NotFound();
        }

        return View(teamMember);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        if (teamMember == null || teamMember.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = TeamMemberViewModel.FromEntity(teamMember);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, TeamMemberViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingTeamMember = await _teamMemberRepository.GetByIdAsync(id);
            if (existingTeamMember == null || existingTeamMember.IsDeleted)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.PhotoFile != null && viewModel.PhotoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.PhotoFile))
                {
                    ModelState.AddModelError("PhotoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingTeamMember.PhotoUrl))
                    {
                        oldFileName = Path.GetFileName(existingTeamMember.PhotoUrl);
                    }

                    viewModel.PhotoUrl = await _fileUploadService.UploadImageAsync(viewModel.PhotoFile, "team", oldFileName);
                }
            }
            else
            {
                // Keep existing photo URL if no new file uploaded
                viewModel.PhotoUrl = existingTeamMember.PhotoUrl;
            }

            if (ModelState.IsValid)
            {
                viewModel.UpdateEntity(existingTeamMember);
                existingTeamMember.UpdatedAt = DateTime.UtcNow;

                await _teamMemberRepository.UpdateAsync(existingTeamMember);
                TempData["SuccessMessage"] = "Team member updated successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await TeamMemberExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating team member: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        if (teamMember == null || teamMember.IsDeleted)
        {
            return NotFound();
        }

        return View(teamMember);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        if (teamMember == null)
        {
            return NotFound();
        }

        // Soft delete
        await _teamMemberRepository.DeleteAsync(teamMember);
        TempData["SuccessMessage"] = "Team member deleted successfully.";

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ToggleStatus(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        if (teamMember == null || teamMember.IsDeleted)
        {
            return NotFound();
        }

        teamMember.IsActive = !teamMember.IsActive;
        teamMember.UpdatedAt = DateTime.UtcNow;
        await _teamMemberRepository.UpdateAsync(teamMember);

        TempData["SuccessMessage"] = $"Team member {(teamMember.IsActive ? "activated" : "deactivated")} successfully.";
        return RedirectToAction(nameof(Index));
    }

    private async Task<bool> TeamMemberExists(int id)
    {
        var teamMember = await _teamMemberRepository.GetByIdAsync(id);
        return teamMember != null && !teamMember.IsDeleted;
    }
}
