using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
public class ServicesController : Controller
{
    private readonly IRepository<Service> _serviceRepository;
    private readonly IFileUploadService _fileUploadService;

    public ServicesController(IRepository<Service> serviceRepository, IFileUploadService fileUploadService)
    {
        _serviceRepository = serviceRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var services = await _serviceRepository.GetAll()
            .Where(s => !s.IsDeleted)
            .OrderBy(s => s.DisplayOrder)
            .ToListAsync();

        return View(services);
    }

    public IActionResult Create()
    {
        var viewModel = new ServiceViewModel
        {
            IsActive = true,
            DisplayOrder = 0
        };
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ServiceViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.IconClass = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "services");
                }
            }

            if (ModelState.IsValid)
            {
                var service = viewModel.ToEntity();
                service.CreatedAt = DateTime.UtcNow;
                service.UpdatedAt = DateTime.UtcNow;
                service.IsActive = true; // Ensure new services are active by default

                await _serviceRepository.AddAsync(service);
                TempData["SuccessMessage"] = "Service created successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating service: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        return View(service);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = ServiceViewModel.FromEntity(service);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, ServiceViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingService = await _serviceRepository.GetByIdAsync(id);
            if (existingService == null || existingService.IsDeleted)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingService.IconClass) && existingService.IconClass.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingService.IconClass);
                    }

                    viewModel.IconClass = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "services", oldFileName);
                }
            }
            else
            {
                // Keep existing icon if no new file uploaded
                viewModel.IconClass = existingService.IconClass;
            }

            if (ModelState.IsValid)
            {
                viewModel.UpdateEntity(existingService);
                existingService.UpdatedAt = DateTime.UtcNow;

                await _serviceRepository.UpdateAsync(existingService);
                TempData["SuccessMessage"] = "Service updated successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await ServiceExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating service: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        return View(service);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        service.IsDeleted = true;
        service.UpdatedAt = DateTime.UtcNow;
        await _serviceRepository.UpdateAsync(service);

        return RedirectToAction(nameof(Index));
    }

    private async Task<bool> ServiceExists(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        return service != null && !service.IsDeleted;
    }
}
