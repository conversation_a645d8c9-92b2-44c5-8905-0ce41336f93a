using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class TechnologyViewModel
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string Description { get; set; } = string.Empty;
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    // For file upload
    [Display(Name = "Icon")]
    public IFormFile? IconFile { get; set; }
    
    // Current icon URL (for editing)
    public string IconUrl { get; set; } = string.Empty;
    
    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Convert from Entity to ViewModel
    public static TechnologyViewModel FromEntity(Technology technology)
    {
        return new TechnologyViewModel
        {
            Id = technology.Id,
            Name = technology.Name,
            Description = technology.Description,
            DisplayOrder = technology.DisplayOrder,
            IconUrl = technology.IconUrl,
            CreatedAt = technology.CreatedAt,
            UpdatedAt = technology.UpdatedAt
        };
    }
    
    // Convert from ViewModel to Entity
    public Technology ToEntity()
    {
        return new Technology
        {
            Id = this.Id,
            Name = this.Name,
            Description = this.Description,
            DisplayOrder = this.DisplayOrder,
            IconUrl = this.IconUrl,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }
    
    // Update existing entity with ViewModel data
    public void UpdateEntity(Technology technology)
    {
        technology.Name = this.Name;
        technology.Description = this.Description;
        technology.DisplayOrder = this.DisplayOrder;
        
        // Only update IconUrl if it's provided
        if (!string.IsNullOrEmpty(this.IconUrl))
        {
            technology.IconUrl = this.IconUrl;
        }
    }
}
