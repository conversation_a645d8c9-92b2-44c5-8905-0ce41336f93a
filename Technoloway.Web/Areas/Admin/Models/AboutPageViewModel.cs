using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class AboutPageViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Page Title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "Meta Description")]
        public string? MetaDescription { get; set; }

        [StringLength(500)]
        [Display(Name = "Meta Keywords")]
        public string? MetaKeywords { get; set; }

        // Hero Section
        [Required]
        [StringLength(200)]
        [Display(Name = "Hero Title")]
        public string HeroTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        [Display(Name = "Hero Subtitle")]
        public string HeroSubtitle { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "Hero Image URL")]
        public string? HeroImageUrl { get; set; }

        // Main Story Section
        [Required]
        [StringLength(100)]
        [Display(Name = "Story Title")]
        public string StoryTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(300)]
        [Display(Name = "Story Subtitle")]
        public string StorySubtitle { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Story Content")]
        public string StoryContent { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "Story Image URL")]
        public string? StoryImageUrl { get; set; }

        // Stats Section
        [StringLength(50)]
        [Display(Name = "Stat 1 Number")]
        public string? Stat1Number { get; set; }

        [StringLength(100)]
        [Display(Name = "Stat 1 Label")]
        public string? Stat1Label { get; set; }

        [StringLength(50)]
        [Display(Name = "Stat 2 Number")]
        public string? Stat2Number { get; set; }

        [StringLength(100)]
        [Display(Name = "Stat 2 Label")]
        public string? Stat2Label { get; set; }

        [StringLength(50)]
        [Display(Name = "Stat 3 Number")]
        public string? Stat3Number { get; set; }

        [StringLength(100)]
        [Display(Name = "Stat 3 Label")]
        public string? Stat3Label { get; set; }

        [StringLength(50)]
        [Display(Name = "Stat 4 Number")]
        public string? Stat4Number { get; set; }

        [StringLength(100)]
        [Display(Name = "Stat 4 Label")]
        public string? Stat4Label { get; set; }

        // Mission & Vision Section
        [StringLength(100)]
        [Display(Name = "Mission Title")]
        public string? MissionTitle { get; set; }

        [Display(Name = "Mission Content")]
        public string? MissionContent { get; set; }

        [StringLength(100)]
        [Display(Name = "Vision Title")]
        public string? VisionTitle { get; set; }

        [Display(Name = "Vision Content")]
        public string? VisionContent { get; set; }

        // Values Section
        [StringLength(100)]
        [Display(Name = "Values Title")]
        public string? ValuesTitle { get; set; }

        [Display(Name = "Values Content")]
        public string? ValuesContent { get; set; }

        // CTA Section
        [StringLength(100)]
        [Display(Name = "CTA Title")]
        public string? CtaTitle { get; set; }

        [StringLength(300)]
        [Display(Name = "CTA Subtitle")]
        public string? CtaSubtitle { get; set; }

        [StringLength(50)]
        [Display(Name = "Primary Button Text")]
        public string? CtaPrimaryButtonText { get; set; }

        [StringLength(200)]
        [Display(Name = "Primary Button URL")]
        public string? CtaPrimaryButtonUrl { get; set; }

        [StringLength(50)]
        [Display(Name = "Secondary Button Text")]
        public string? CtaSecondaryButtonText { get; set; }

        [StringLength(200)]
        [Display(Name = "Secondary Button URL")]
        public string? CtaSecondaryButtonUrl { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        public DateTime LastModified { get; set; }

        public string? ModifiedBy { get; set; }

        public List<AboutPageSectionViewModel> Sections { get; set; } = new List<AboutPageSectionViewModel>();
    }

    public class AboutPageSectionViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Section Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Section Content")]
        public string Content { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "Icon Class")]
        public string? IconClass { get; set; }

        [StringLength(500)]
        [Display(Name = "Image URL")]
        public string? ImageUrl { get; set; }

        [Display(Name = "Display Order")]
        public int DisplayOrder { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [StringLength(50)]
        [Display(Name = "Section Type")]
        public string SectionType { get; set; } = "content";
    }
}
