using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class TeamMemberViewModel
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Position is required")]
    [StringLength(100, ErrorMessage = "Position cannot exceed 100 characters")]
    public string Position { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Bio cannot exceed 1000 characters")]
    public string Bio { get; set; } = string.Empty;
    
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string Email { get; set; } = string.Empty;
    
    [Url(ErrorMessage = "Please enter a valid URL")]
    [StringLength(500, ErrorMessage = "LinkedIn URL cannot exceed 500 characters")]
    public string LinkedInUrl { get; set; } = string.Empty;
    
    [Url(ErrorMessage = "Please enter a valid URL")]
    [StringLength(500, ErrorMessage = "Twitter URL cannot exceed 500 characters")]
    public string TwitterUrl { get; set; } = string.Empty;
    
    [Url(ErrorMessage = "Please enter a valid URL")]
    [StringLength(500, ErrorMessage = "GitHub URL cannot exceed 500 characters")]
    public string GithubUrl { get; set; } = string.Empty;
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // For file upload
    [Display(Name = "Photo")]
    public IFormFile? PhotoFile { get; set; }
    
    // Current photo URL (for editing)
    public string PhotoUrl { get; set; } = string.Empty;
    
    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Convert from Entity to ViewModel
    public static TeamMemberViewModel FromEntity(TeamMember teamMember)
    {
        return new TeamMemberViewModel
        {
            Id = teamMember.Id,
            Name = teamMember.Name,
            Position = teamMember.Position,
            Bio = teamMember.Bio,
            Email = teamMember.Email,
            LinkedInUrl = teamMember.LinkedInUrl,
            TwitterUrl = teamMember.TwitterUrl,
            GithubUrl = teamMember.GithubUrl,
            DisplayOrder = teamMember.DisplayOrder,
            IsActive = teamMember.IsActive,
            PhotoUrl = teamMember.PhotoUrl,
            CreatedAt = teamMember.CreatedAt,
            UpdatedAt = teamMember.UpdatedAt
        };
    }
    
    // Convert from ViewModel to Entity
    public TeamMember ToEntity()
    {
        return new TeamMember
        {
            Id = this.Id,
            Name = this.Name,
            Position = this.Position,
            Bio = this.Bio,
            Email = this.Email,
            LinkedInUrl = this.LinkedInUrl,
            TwitterUrl = this.TwitterUrl,
            GithubUrl = this.GithubUrl,
            DisplayOrder = this.DisplayOrder,
            IsActive = this.IsActive,
            PhotoUrl = this.PhotoUrl,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }
    
    // Update existing entity with ViewModel data
    public void UpdateEntity(TeamMember teamMember)
    {
        teamMember.Name = this.Name;
        teamMember.Position = this.Position;
        teamMember.Bio = this.Bio;
        teamMember.Email = this.Email;
        teamMember.LinkedInUrl = this.LinkedInUrl;
        teamMember.TwitterUrl = this.TwitterUrl;
        teamMember.GithubUrl = this.GithubUrl;
        teamMember.DisplayOrder = this.DisplayOrder;
        teamMember.IsActive = this.IsActive;
        
        // Only update PhotoUrl if it's provided
        if (!string.IsNullOrEmpty(this.PhotoUrl))
        {
            teamMember.PhotoUrl = this.PhotoUrl;
        }
    }
}
