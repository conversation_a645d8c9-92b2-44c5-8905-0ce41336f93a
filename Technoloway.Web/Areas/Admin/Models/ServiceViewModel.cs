using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class ServiceViewModel
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Description is required")]
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string Description { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Price is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }
    
    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // For file upload
    [Display(Name = "Icon")]
    public IFormFile? IconFile { get; set; }
    
    // Current icon URL (for editing) - this will replace IconClass
    public string IconClass { get; set; } = string.Empty;
    
    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Convert from Entity to ViewModel
    public static ServiceViewModel FromEntity(Service service)
    {
        return new ServiceViewModel
        {
            Id = service.Id,
            Name = service.Name,
            Description = service.Description,
            Price = service.Price,
            DisplayOrder = service.DisplayOrder,
            IsActive = service.IsActive,
            IconClass = service.IconClass,
            CreatedAt = service.CreatedAt,
            UpdatedAt = service.UpdatedAt
        };
    }
    
    // Convert from ViewModel to Entity
    public Service ToEntity()
    {
        return new Service
        {
            Id = this.Id,
            Name = this.Name,
            Description = this.Description,
            Price = this.Price,
            DisplayOrder = this.DisplayOrder,
            IsActive = this.IsActive,
            IconClass = this.IconClass,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }
    
    // Update existing entity with ViewModel data
    public void UpdateEntity(Service service)
    {
        service.Name = this.Name;
        service.Description = this.Description;
        service.Price = this.Price;
        service.DisplayOrder = this.DisplayOrder;
        service.IsActive = this.IsActive;
        
        // Only update IconClass if it's provided
        if (!string.IsNullOrEmpty(this.IconClass))
        {
            service.IconClass = this.IconClass;
        }
    }
}
