using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class SiteSettingViewModel
{
    public SiteSettingViewModel()
    {
        // Ensure all required fields are initialized to prevent validation errors
        Key = string.Empty;
        Value = string.Empty;
        Group = "General";
        Description = string.Empty;
        Icon = string.Empty; // Important: Initialize to empty string for database compatibility
        IsPublic = true;
    }

    public int Id { get; set; }

    [Required(ErrorMessage = "Key is required")]
    [StringLength(100, ErrorMessage = "Key cannot exceed 100 characters")]
    public string Key { get; set; } = string.Empty;

    [StringLength(2000, ErrorMessage = "Value cannot exceed 2000 characters")]
    public string Value { get; set; } = string.Empty;

    [Required(ErrorMessage = "Group is required")]
    [StringLength(50, ErrorMessage = "Group cannot exceed 50 characters")]
    public string Group { get; set; } = "General";

    public bool IsPublic { get; set; } = true;

    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    // Current icon (image URL only) - NOT required for form validation
    public string? Icon { get; set; } = string.Empty;

    // For file upload
    [Display(Name = "Icon File")]
    public IFormFile? IconFile { get; set; }

    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Convert from Entity to ViewModel
    public static SiteSettingViewModel FromEntity(SiteSetting siteSetting)
    {
        return new SiteSettingViewModel
        {
            Id = siteSetting.Id,
            Key = siteSetting.Key,
            Value = siteSetting.Value,
            Group = siteSetting.Group,
            IsPublic = siteSetting.IsPublic,
            Description = siteSetting.Description,
            Icon = siteSetting.Icon,
            CreatedAt = siteSetting.CreatedAt,
            UpdatedAt = siteSetting.UpdatedAt
        };
    }

    // Convert from ViewModel to Entity
    public SiteSetting ToEntity()
    {
        return new SiteSetting
        {
            Id = this.Id,
            Key = this.Key ?? string.Empty,
            Value = this.Value ?? string.Empty,
            Group = this.Group ?? "General",
            IsPublic = this.IsPublic,
            Description = this.Description ?? string.Empty,
            Icon = this.Icon ?? string.Empty, // Ensure Icon is never null
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }

    // Update existing entity with ViewModel data
    public void UpdateEntity(SiteSetting siteSetting)
    {
        siteSetting.Key = this.Key ?? string.Empty;
        siteSetting.Value = this.Value ?? string.Empty;
        siteSetting.Group = this.Group ?? "General";
        siteSetting.IsPublic = this.IsPublic;
        siteSetting.Description = this.Description ?? string.Empty;
        siteSetting.Icon = this.Icon ?? string.Empty; // Ensure Icon is never null
    }
}
