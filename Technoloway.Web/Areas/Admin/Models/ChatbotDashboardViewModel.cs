using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ChatbotDashboardViewModel
    {
        public List<ChatbotIntent> Intents { get; set; } = new List<ChatbotIntent>();
        public List<ChatbotResponse> Responses { get; set; } = new List<ChatbotResponse>();
        public List<ChatbotKeyword> Keywords { get; set; } = new List<ChatbotKeyword>();
        public List<ChatbotQuickAction> QuickActions { get; set; } = new List<ChatbotQuickAction>();

        // Computed properties for dashboard stats
        public int TotalIntents => Intents.Count;
        public int ActiveIntents => Intents.Count(i => i.IsActive);
        public int TotalResponses => Responses.Count;
        public int ActiveResponses => Responses.Count(r => r.IsActive);
        public int TotalKeywords => Keywords.Count;
        public int ActiveKeywords => Keywords.Count(k => k.<PERSON>Active);
        public int TotalQuickActions => QuickActions.Count;
        public int ActiveQuickActions => QuickActions.Count(qa => qa.IsActive);

        // Recent items for dashboard display
        public List<ChatbotIntent> RecentIntents => Intents
            .OrderByDescending(i => i.CreatedAt)
            .Take(5)
            .ToList();

        public List<ChatbotResponse> RecentResponses => Responses
            .OrderByDescending(r => r.CreatedAt)
            .Take(5)
            .ToList();

        public List<ChatbotKeyword> RecentKeywords => Keywords
            .OrderByDescending(k => k.CreatedAt)
            .Take(5)
            .ToList();
    }
}
