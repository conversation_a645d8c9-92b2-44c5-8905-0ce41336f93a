using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ChatbotKeywordViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Keyword is required")]
        [StringLength(100, ErrorMessage = "Keyword cannot exceed 100 characters")]
        [Display(Name = "Keyword")]
        public string Keyword { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "Synonyms cannot exceed 100 characters")]
        [Display(Name = "Synonyms (comma-separated)")]
        public string? Synonyms { get; set; }

        [Range(1, 10, ErrorMessage = "Weight must be between 1 and 10")]
        [Display(Name = "Weight")]
        public int Weight { get; set; } = 1;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [StringLength(50, ErrorMessage = "Match type cannot exceed 50 characters")]
        [Display(Name = "Match Type")]
        public string MatchType { get; set; } = "contains";

        [Display(Name = "Case Sensitive")]
        public bool IsCaseSensitive { get; set; } = false;

        [Required(ErrorMessage = "Please select an intent")]
        [Display(Name = "Intent")]
        public int ChatbotIntentId { get; set; }
    }
}
