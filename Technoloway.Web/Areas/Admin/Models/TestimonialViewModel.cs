using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class TestimonialViewModel
{
    public TestimonialViewModel()
    {
        ClientName = string.Empty;
        ClientTitle = string.Empty;
        ClientCompany = string.Empty;
        ClientPhotoUrl = string.Empty;
        Content = string.Empty;
        IsActive = true;
    }

    public int Id { get; set; }

    [Required(ErrorMessage = "Client name is required")]
    [StringLength(100, ErrorMessage = "Client name cannot exceed 100 characters")]
    public string ClientName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Client title is required")]
    [StringLength(100, ErrorMessage = "Client title cannot exceed 100 characters")]
    public string ClientTitle { get; set; } = string.Empty;

    [Required(ErrorMessage = "Client company is required")]
    [StringLength(100, ErrorMessage = "Client company cannot exceed 100 characters")]
    public string ClientCompany { get; set; } = string.Empty;

    // Current photo URL - NOT required for form validation
    public string? ClientPhotoUrl { get; set; } = string.Empty;

    // For file upload
    [Display(Name = "Client Photo")]
    public IFormFile? PhotoFile { get; set; }

    [Required(ErrorMessage = "Testimonial content is required")]
    [StringLength(1000, MinimumLength = 10, ErrorMessage = "Content must be between 10 and 1000 characters")]
    public string Content { get; set; } = string.Empty;

    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5 stars")]
    public int Rating { get; set; } // 1-5 stars

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Convert from Entity to ViewModel
    public static TestimonialViewModel FromEntity(Testimonial testimonial)
    {
        return new TestimonialViewModel
        {
            Id = testimonial.Id,
            ClientName = testimonial.ClientName,
            ClientTitle = testimonial.ClientTitle,
            ClientCompany = testimonial.ClientCompany,
            ClientPhotoUrl = testimonial.ClientPhotoUrl,
            Content = testimonial.Content,
            Rating = testimonial.Rating,
            IsActive = testimonial.IsActive,
            DisplayOrder = testimonial.DisplayOrder,
            CreatedAt = testimonial.CreatedAt,
            UpdatedAt = testimonial.UpdatedAt
        };
    }

    // Convert from ViewModel to Entity
    public Testimonial ToEntity()
    {
        return new Testimonial
        {
            Id = this.Id,
            ClientName = this.ClientName ?? string.Empty,
            ClientTitle = this.ClientTitle ?? string.Empty,
            ClientCompany = this.ClientCompany ?? string.Empty,
            ClientPhotoUrl = this.ClientPhotoUrl ?? string.Empty,
            Content = this.Content ?? string.Empty,
            Rating = this.Rating,
            IsActive = this.IsActive,
            DisplayOrder = this.DisplayOrder,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }

    // Update existing entity with ViewModel data
    public void UpdateEntity(Testimonial testimonial)
    {
        testimonial.ClientName = this.ClientName ?? string.Empty;
        testimonial.ClientTitle = this.ClientTitle ?? string.Empty;
        testimonial.ClientCompany = this.ClientCompany ?? string.Empty;
        testimonial.ClientPhotoUrl = this.ClientPhotoUrl ?? string.Empty;
        testimonial.Content = this.Content ?? string.Empty;
        testimonial.Rating = this.Rating;
        testimonial.IsActive = this.IsActive;
        testimonial.DisplayOrder = this.DisplayOrder;
    }
}
