using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ChatbotIntentViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Intent name is required")]
        [StringLength(50, ErrorMessage = "Intent name cannot exceed 50 characters")]
        [Display(Name = "Intent Name")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Display name is required")]
        [StringLength(100, ErrorMessage = "Display name cannot exceed 100 characters")]
        [Display(Name = "Display Name")]
        public string DisplayName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
        [Display(Name = "Display Order")]
        public int DisplayOrder { get; set; }

        [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
        [Display(Name = "Icon Class")]
        public string IconClass { get; set; } = string.Empty;
    }
}
