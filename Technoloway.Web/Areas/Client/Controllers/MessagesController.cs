using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class MessagesController : Controller
{
    private readonly IRepository<Message> _messageRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IRepository<Core.Entities.Client> _clientRepository;

    public MessagesController(
        IRepository<Message> messageRepository,
        IRepository<Project> projectRepository,
        UserManager<IdentityUser> userManager,
        IRepository<Core.Entities.Client> clientRepository)
    {
        _messageRepository = messageRepository;
        _projectRepository = projectRepository;
        _userManager = userManager;
        _clientRepository = clientRepository;
    }

    public async Task<IActionResult> Index(int? projectId = null)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var clientProjects = await _projectRepository.ListAsync(p => p.ClientId == client.Id);
        var projectIds = clientProjects.Select(p => p.Id).ToList();

        var query = _messageRepository.GetAll()
            .Where(m => projectIds.Contains(m.ProjectId) && !m.IsDeleted);

        if (projectId.HasValue)
        {
            query = query.Where(m => m.ProjectId == projectId.Value);
        }

        var messages = await query
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync();

        ViewBag.Projects = clientProjects.OrderBy(p => p.Name).ToList();
        ViewBag.SelectedProjectId = projectId;
        ViewBag.UnreadCount = messages.Count(m => !m.IsRead && m.SenderRole != "Client");
        ViewBag.TotalMessages = messages.Count;

        return View(messages);
    }

    public async Task<IActionResult> Conversation(int projectId)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == projectId && p.ClientId == client.Id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        var messages = await _messageRepository.ListAsync(m => m.ProjectId == projectId);
        ViewBag.Project = project;
        ViewBag.ClientName = client.ContactName;

        // Mark messages as read
        var unreadMessages = messages.Where(m => !m.IsRead && m.SenderRole != "Client").ToList();
        foreach (var message in unreadMessages)
        {
            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
            await _messageRepository.UpdateAsync(message);
        }

        return View(messages.OrderBy(m => m.CreatedAt));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SendMessage(int projectId, string content)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "User not authenticated" });
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return Json(new { success = false, message = "Client profile not found" });
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == projectId && p.ClientId == client.Id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return Json(new { success = false, message = "Project not found" });
        }

        if (string.IsNullOrWhiteSpace(content))
        {
            return Json(new { success = false, message = "Message content cannot be empty" });
        }

        var message = new Message
        {
            ProjectId = projectId,
            Content = content.Trim(),
            SenderId = user.Id,
            SenderName = client.ContactName,
            SenderRole = "Client",
            IsRead = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _messageRepository.AddAsync(message);

        return Json(new {
            success = true,
            message = "Message sent successfully",
            messageData = new {
                id = message.Id,
                content = message.Content,
                senderName = message.SenderName,
                senderRole = message.SenderRole,
                createdAt = message.CreatedAt.ToString("yyyy-MM-dd HH:mm")
            }
        });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> MarkAsRead(int messageId)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "User not authenticated" });
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return Json(new { success = false, message = "Client profile not found" });
        }

        var message = await _messageRepository.GetByIdAsync(messageId);
        if (message == null)
        {
            return Json(new { success = false, message = "Message not found" });
        }

        // Verify the message belongs to a project owned by this client
        var project = await _projectRepository.GetByIdAsync(message.ProjectId);
        if (project == null || project.ClientId != client.Id)
        {
            return Json(new { success = false, message = "Access denied" });
        }

        if (!message.IsRead && message.SenderRole != "Client")
        {
            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
            await _messageRepository.UpdateAsync(message);
        }

        return Json(new { success = true, message = "Message marked as read" });
    }

    public async Task<IActionResult> GetUnreadCount()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { count = 0 });
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return Json(new { count = 0 });
        }

        var clientProjects = await _projectRepository.ListAsync(p => p.ClientId == client.Id);
        var projectIds = clientProjects.Select(p => p.Id).ToList();

        var unreadCount = await _messageRepository.GetAll()
            .Where(m => projectIds.Contains(m.ProjectId) && !m.IsRead && m.SenderRole != "Client" && !m.IsDeleted)
            .CountAsync();

        return Json(new { count = unreadCount });
    }
}
