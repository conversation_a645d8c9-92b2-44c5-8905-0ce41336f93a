using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using System.Linq;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class HomeController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly IRepository<Message> _messageRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IRepository<Core.Entities.Client> _clientRepository;

    public HomeController(
        IRepository<Project> projectRepository,
        IRepository<Invoice> invoiceRepository,
        IRepository<Message> messageRepository,
        UserManager<IdentityUser> userManager,
        IRepository<Core.Entities.Client> clientRepository)
    {
        _projectRepository = projectRepository;
        _invoiceRepository = invoiceRepository;
        _messageRepository = messageRepository;
        _userManager = userManager;
        _clientRepository = clientRepository;
    }

    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var projects = await _projectRepository.ListAsync(p => p.ClientId == client.Id);
        var invoices = await _invoiceRepository.ListAsync(i => i.ClientId == client.Id);
        var pendingInvoices = invoices.Where(i => i.Status == "Pending").ToList();

        ViewBag.ProjectCount = projects.Count;
        ViewBag.InvoiceCount = invoices.Count;
        ViewBag.PendingInvoiceCount = pendingInvoices.Count;

        // Convert to List first to avoid LINQ issues
        var projectsList = projects.ToList();
        var invoicesList = invoices.ToList();

        ViewBag.RecentProjects = projectsList.OrderByDescending(p => p.CreatedAt).Take(5).ToList();
        ViewBag.RecentInvoices = invoicesList.OrderByDescending(i => i.CreatedAt).Take(5).ToList();
        ViewBag.RecentInvoicesForDisplay = invoicesList.OrderByDescending(i => i.CreatedAt).Take(3).ToList();

        return View();
    }
}
