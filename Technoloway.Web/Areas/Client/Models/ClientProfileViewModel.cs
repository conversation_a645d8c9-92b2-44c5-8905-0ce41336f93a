using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Client.Models;

public class ClientProfileViewModel
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Company name is required")]
    [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
    [Display(Name = "Company Name")]
    public string CompanyName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Contact name is required")]
    [StringLength(100, ErrorMessage = "Contact name cannot exceed 100 characters")]
    [Display(Name = "Contact Name")]
    public string ContactName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Contact email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    [Display(Name = "Contact Email")]
    public string ContactEmail { get; set; } = string.Empty;
    
    [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
    [Display(Name = "Contact Phone")]
    public string? ContactPhone { get; set; }
    
    [StringLength(200, ErrorMessage = "Address cannot exceed 200 characters")]
    [Display(Name = "Address")]
    public string? Address { get; set; }
    
    [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
    [Display(Name = "City")]
    public string? City { get; set; }
    
    [StringLength(50, ErrorMessage = "State cannot exceed 50 characters")]
    [Display(Name = "State/Province")]
    public string? State { get; set; }
    
    [StringLength(20, ErrorMessage = "Zip code cannot exceed 20 characters")]
    [Display(Name = "Zip/Postal Code")]
    public string? ZipCode { get; set; }
    
    [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
    [Display(Name = "Country")]
    public string? Country { get; set; }
    
    [StringLength(500, ErrorMessage = "Logo URL cannot exceed 500 characters")]
    [Display(Name = "Company Logo URL")]
    public string? LogoUrl { get; set; }
}
