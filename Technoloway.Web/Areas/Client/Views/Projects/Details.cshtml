@model Technoloway.Core.Entities.Project
@{
    ViewData["Title"] = Model.Name;
}

<div class="container-fluid p-4">
    <!-- Project Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-3">
                                <a asp-action="Index" class="btn btn-outline-secondary btn-sm me-3">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Projects
                                </a>
                                <div>
                                    <h1 class="mb-1">@Model.Name</h1>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="status-indicator in-progress">In Progress</span>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Started @Model.CreatedAt.ToString("MMMM dd, yyyy")
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="btn-group">
                                <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-success">
                                    <i class="fas fa-comments me-1"></i> Messages
                                    @if (ViewBag.UnreadMessageCount > 0)
                                    {
                                        <span class="badge bg-light text-dark ms-1">@ViewBag.UnreadMessageCount</span>
                                    }
                                </a>
                                <a asp-action="Documents" asp-route-id="@Model.Id" class="btn btn-info">
                                    <i class="fas fa-folder me-1"></i> Documents
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Project Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Project Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Description</h6>
                            <p>@Model.Description</p>
                        </div>
                        <div class="col-md-6">
                            @if (!string.IsNullOrEmpty(Model.ImageUrl))
                            {
                                <h6 class="text-muted mb-2">Project Preview</h6>
                                <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" style="max-height: 200px;">
                            }
                        </div>
                    </div>
                    
                    <!-- Project Details Grid -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-tag text-primary me-2"></i>
                                <strong>Service:</strong> @(Model.Service?.Name ?? "N/A")
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-calendar-check text-success me-2"></i>
                                <strong>Completion Date:</strong> @Model.CompletionDate.ToString("MMMM dd, yyyy")
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.ProjectUrl))
                        {
                            <div class="col-md-6">
                                <div class="info-item">
                                    <i class="fas fa-external-link-alt text-info me-2"></i>
                                    <strong>Project URL:</strong> 
                                    <a href="@Model.ProjectUrl" target="_blank" class="text-decoration-none">
                                        View Live Site <i class="fas fa-external-link-alt small"></i>
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Project Progress -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Project Progress</h5>
                </div>
                <div class="card-body">
                    <!-- Overall Progress -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Overall Progress</h6>
                            <span class="fw-bold text-primary">75%</span>
                        </div>
                        <div class="progress" style="height: 12px;">
                            <div class="progress-bar bg-primary" style="width: 75%"></div>
                        </div>
                    </div>

                    <!-- Milestones -->
                    <div class="milestones">
                        <h6 class="mb-3">Project Milestones</h6>
                        <div class="milestone-item completed">
                            <div class="milestone-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="milestone-content">
                                <h6 class="mb-1">Project Planning</h6>
                                <p class="text-muted mb-1">Requirements gathering and project scope definition</p>
                                <small class="text-success">Completed on @Model.CreatedAt.AddDays(3).ToString("MMM dd, yyyy")</small>
                            </div>
                        </div>
                        
                        <div class="milestone-item completed">
                            <div class="milestone-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="milestone-content">
                                <h6 class="mb-1">Design Phase</h6>
                                <p class="text-muted mb-1">UI/UX design and wireframe creation</p>
                                <small class="text-success">Completed on @Model.CreatedAt.AddDays(10).ToString("MMM dd, yyyy")</small>
                            </div>
                        </div>
                        
                        <div class="milestone-item active">
                            <div class="milestone-icon">
                                <i class="fas fa-cog fa-spin"></i>
                            </div>
                            <div class="milestone-content">
                                <h6 class="mb-1">Development</h6>
                                <p class="text-muted mb-1">Frontend and backend development</p>
                                <small class="text-info">In Progress - 75% Complete</small>
                            </div>
                        </div>
                        
                        <div class="milestone-item pending">
                            <div class="milestone-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="milestone-content">
                                <h6 class="mb-1">Testing & QA</h6>
                                <p class="text-muted mb-1">Quality assurance and bug fixes</p>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                        
                        <div class="milestone-item pending">
                            <div class="milestone-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="milestone-content">
                                <h6 class="mb-1">Deployment</h6>
                                <p class="text-muted mb-1">Final deployment and go-live</p>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Messages -->
            @if (ViewBag.Messages != null && ViewBag.Messages.Count > 0)
            {
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Messages</h5>
                        <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-primary">
                            View All (@ViewBag.MessageCount)
                        </a>
                    </div>
                    <div class="card-body">
                        @foreach (var message in ViewBag.Messages)
                        {
                            <div class="message-preview mb-3 pb-3 @(message != ViewBag.Messages.Last() ? "border-bottom" : "")">
                                <div class="d-flex align-items-start">
                                    <div class="avatar-sm bg-@(message.SenderRole == "Client" ? "primary" : "success") rounded-circle d-flex align-items-center justify-content-center me-3 flex-shrink-0">
                                        <i class="fas fa-@(message.SenderRole == "Client" ? "user" : "user-tie") text-white"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <h6 class="mb-0">@message.SenderName</h6>
                                            <small class="text-muted">@message.CreatedAt.ToString("MMM dd, HH:mm")</small>
                                        </div>
                                        <p class="mb-0 text-muted">@message.Content.Substring(0, Math.Min(100, message.Content.Length))@(message.Content.Length > 100 ? "..." : "")</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Technologies Used -->
            @if (Model.Technologies != null && Model.Technologies.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Technologies Used</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            @foreach (var tech in Model.Technologies)
                            {
                                <span class="badge bg-light text-dark border">@tech.Name</span>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-outline-success">
                            <i class="fas fa-comment me-2"></i>Send Message
                        </a>
                        <a asp-action="Documents" asp-route-id="@Model.Id" class="btn btn-outline-info">
                            <i class="fas fa-folder me-2"></i>View Documents
                        </a>
                        @if (!string.IsNullOrEmpty(Model.ProjectUrl))
                        {
                            <a href="@Model.ProjectUrl" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-2"></i>View Live Site
                            </a>
                        }
                    </div>
                </div>
            </div>

            <!-- Project Stats -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">Messages</span>
                            <span class="fw-bold">@ViewBag.MessageCount</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">Documents</span>
                            <span class="fw-bold">@(Model.Documents?.Count ?? 0)</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">Days Active</span>
                            <span class="fw-bold">@((DateTime.Now - Model.CreatedAt).Days)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    padding: 0.5rem 0;
}

.milestones {
    position: relative;
}

.milestone-item {
    display: flex;
    margin-bottom: 1.5rem;
    position: relative;
}

.milestone-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 35px;
    width: 2px;
    height: calc(100% + 10px);
    background: var(--border-color);
}

.milestone-item.completed::after {
    background: var(--success-color);
}

.milestone-item.active::after {
    background: var(--info-color);
}

.milestone-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    color: white;
    font-size: 0.75rem;
}

.milestone-item.completed .milestone-icon {
    background: var(--success-color);
}

.milestone-item.active .milestone-icon {
    background: var(--info-color);
}

.milestone-item.pending .milestone-icon {
    background: var(--secondary-color);
}

.milestone-content h6 {
    font-size: 0.875rem;
    font-weight: 600;
}

.milestone-content p {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.message-preview {
    transition: var(--transition);
}

.message-preview:hover {
    background: rgba(79, 70, 229, 0.05);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    margin: -0.75rem;
    margin-bottom: 0.75rem;
}

.stat-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}
</style>
