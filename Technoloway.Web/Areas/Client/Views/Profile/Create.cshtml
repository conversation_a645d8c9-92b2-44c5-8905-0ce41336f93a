@model Technoloway.Web.Areas.Client.Models.ClientProfileViewModel
@{
    ViewData["Title"] = "Create Client Profile";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <div class="icon-circle bg-primary">
                        <i class="fas fa-user-plus text-white"></i>
                    </div>
                </div>
                <div>
                    <h1 class="text-gradient mb-1">Create Your Profile</h1>
                    <p class="text-muted mb-0">Complete your client profile to access all features</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Creation Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Company Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Create" method="post" class="needs-validation" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <!-- Company Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="CompanyName" class="form-control" placeholder="Company Name" required>
                                    <label asp-for="CompanyName"></label>
                                    <span asp-validation-for="CompanyName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="ContactName" class="form-control" placeholder="Contact Name" required>
                                    <label asp-for="ContactName"></label>
                                    <span asp-validation-for="ContactName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="ContactEmail" type="email" class="form-control" placeholder="Contact Email" required>
                                    <label asp-for="ContactEmail"></label>
                                    <span asp-validation-for="ContactEmail" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="ContactPhone" type="tel" class="form-control" placeholder="Contact Phone">
                                    <label asp-for="ContactPhone"></label>
                                    <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>Address Information (Optional)
                            </h6>
                            <div class="form-floating mb-3">
                                <input asp-for="Address" class="form-control" placeholder="Street Address">
                                <label asp-for="Address"></label>
                                <span asp-validation-for="Address" class="text-danger"></span>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input asp-for="City" class="form-control" placeholder="City">
                                        <label asp-for="City"></label>
                                        <span asp-validation-for="City" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input asp-for="State" class="form-control" placeholder="State/Province">
                                        <label asp-for="State"></label>
                                        <span asp-validation-for="State" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input asp-for="ZipCode" class="form-control" placeholder="Zip/Postal Code">
                                        <label asp-for="ZipCode"></label>
                                        <span asp-validation-for="ZipCode" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input asp-for="Country" class="form-control" placeholder="Country">
                                <label asp-for="Country"></label>
                                <span asp-validation-for="Country" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Company Logo -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-image me-2"></i>Company Logo (Optional)
                            </h6>
                            <div class="form-floating mb-3">
                                <input asp-for="LogoUrl" type="url" class="form-control" placeholder="Logo URL">
                                <label asp-for="LogoUrl"></label>
                                <span asp-validation-for="LogoUrl" class="text-danger"></span>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Enter a URL to your company logo image
                            </small>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a asp-area="Identity" asp-page="/Account/Logout" class="btn btn-outline-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>Cancel & Logout
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Create Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Bootstrap form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
}
