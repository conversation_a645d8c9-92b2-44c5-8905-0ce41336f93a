<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Technoloway Client Portal</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/client.css" asp-append-version="true" />
    <meta name="theme-color" content="#4f46e5">

    <style>
        /* Star Rating Styles */
        .rating-stars {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
            gap: 5px;
        }

        .rating-stars input[type="radio"] {
            display: none;
        }

        .rating-stars label {
            cursor: pointer;
            font-size: 1.5rem;
            color: #dee2e6;
            transition: color 0.2s ease-in-out;
        }

        .rating-stars label:hover {
            color: #ffc107;
        }

        .rating-stars input[type="radio"]:checked ~ label {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Enhanced Modern Sidebar -->
        <div class="modern-sidebar" id="sidebar-wrapper">
            <!-- Logo Section -->
            <div class="sidebar-header">
                <a class="sidebar-brand" asp-area="Client" asp-controller="Home" asp-action="Index">
                    <div class="brand-icon">
                        <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">Technoloway</span>
                        <span class="brand-subtitle">Client Portal</span>
                    </div>
                </a>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Overview</div>
                    <a class="nav-item @(ViewContext.RouteData.Values["action"]?.ToString() == "Index" && ViewContext.RouteData.Values["controller"]?.ToString() == "Home" ? "active" : "")" asp-area="Client" asp-controller="Home" asp-action="Index" data-page="dashboard">
                        <div class="nav-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="nav-text">Dashboard</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Project Management</div>
                    <a class="nav-item @(ViewContext.RouteData.Values["controller"]?.ToString() == "Projects" ? "active" : "")" asp-area="Client" asp-controller="Projects" asp-action="Index" data-page="projects">
                        <div class="nav-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <span class="nav-text">My Projects</span>
                        <span class="nav-badge">3</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item @(ViewContext.RouteData.Values["controller"]?.ToString() == "Documents" ? "active" : "")" asp-area="Client" asp-controller="Documents" asp-action="Index" data-page="documents">
                        <div class="nav-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <span class="nav-text">Documents</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Communication</div>
                    <a class="nav-item @(ViewContext.RouteData.Values["controller"]?.ToString() == "Messages" ? "active" : "")" asp-area="Client" asp-controller="Messages" asp-action="Index" data-page="messages">
                        <div class="nav-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <span class="nav-text">Messages</span>
                        <span class="nav-badge warning" id="unreadMessagesBadge">2</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item @(ViewContext.RouteData.Values["controller"]?.ToString() == "Feedback" ? "active" : "")" asp-area="Client" asp-controller="Feedback" asp-action="Index" data-page="feedback">
                        <div class="nav-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="nav-text">My Feedback</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Business</div>
                    <a class="nav-item @(ViewContext.RouteData.Values["controller"]?.ToString() == "Invoices" ? "active" : "")" asp-area="Client" asp-controller="Invoices" asp-action="Index" data-page="invoices">
                        <div class="nav-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <span class="nav-text">Invoices</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Support</div>
                    <a class="nav-item" href="#" data-bs-toggle="modal" data-bs-target="#supportModal">
                        <div class="nav-icon">
                            <i class="fas fa-life-ring"></i>
                        </div>
                        <span class="nav-text">Help & Support</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" href="#" data-bs-toggle="modal" data-bs-target="#feedbackModal">
                        <div class="nav-icon">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <span class="nav-text">Send Feedback</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Account</div>
                    <a class="nav-item" asp-area="Client" asp-controller="Profile" asp-action="Index" data-page="profile">
                        <div class="nav-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <span class="nav-text">Profile Settings</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a class="nav-item" href="#" onclick="toggleTheme()">
                        <div class="nav-icon">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </div>
                        <span class="nav-text">Dark Mode</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">@User.Identity?.Name</div>
                        <div class="user-role">Client</div>
                    </div>
                </div>
                <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post" class="w-100 mt-3">
                    <button type="submit" class="btn btn-outline-light btn-sm w-100">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </button>
                </form>
            </div>
        </div>
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Enhanced Top Navigation -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button class="btn" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="ms-3">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                        </ol>
                    </nav>

                    <!-- Top Navigation Items -->
                    <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                        <!-- Quick Actions -->
                        <div class="nav-item me-3">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index" target="_blank" title="View Main Site">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>

                        <!-- Notifications -->
                        <div class="nav-item dropdown me-3">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell fs-5"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                                    3
                                </span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 320px;">
                                <div class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span class="fw-bold">Notifications</span>
                                    <a href="#" class="text-primary small">Mark all read</a>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item d-flex align-items-start py-2" href="#">
                                    <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-3 flex-shrink-0">
                                        <i class="fas fa-check text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fs-6">Project Completed</h6>
                                        <p class="mb-1 small text-muted">Your website project has been completed</p>
                                        <small class="text-muted">2 hours ago</small>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center small" href="#">View all notifications</a>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="nav-item dropdown">
                            <a class="nav-link d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <span class="d-none d-md-inline">@User.Identity?.Name</span>
                                <i class="fas fa-chevron-down ms-2 small"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                    <i class="fas fa-user me-2"></i>My Profile
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="toggleTheme()">
                                    <i class="fas fa-moon me-2" id="themeIconTop"></i>Dark Mode
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        @TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }

                @if (TempData["Error"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        @TempData["Error"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }

                @RenderBody()
            </main>
        </div>
    </div>

    <!-- Support Modal -->
    <div class="modal fade" id="supportModal" tabindex="-1" aria-labelledby="supportModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="supportModalLabel">Help & Support</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-question-circle fa-2x text-primary mb-3"></i>
                                    <h6>FAQ</h6>
                                    <p class="small text-muted">Find answers to common questions</p>
                                    <a href="#" class="btn btn-outline-primary btn-sm">View FAQ</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-headset fa-2x text-success mb-3"></i>
                                    <h6>Contact Support</h6>
                                    <p class="small text-muted">Get help from our team</p>
                                    <a href="mailto:<EMAIL>" class="btn btn-outline-success btn-sm">Email Us</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback Modal -->
    <div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="feedbackModalLabel">Send Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="feedbackForm" asp-area="Client" asp-controller="Feedback" asp-action="Submit" method="post">
                    @Html.AntiForgeryToken()
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="feedbackType" class="form-label">Feedback Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="feedbackType" name="feedbackType" required>
                                    <option value="">Select feedback type</option>
                                    <option value="General Feedback">General Feedback</option>
                                    <option value="Bug Report">Bug Report</option>
                                    <option value="Feature Request">Feature Request</option>
                                    <option value="Complaint">Complaint</option>
                                    <option value="Compliment">Compliment</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="projectId" class="form-label">Related Project (Optional)</label>
                                <select class="form-select" id="projectId" name="projectId">
                                    <option value="">Select a project</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" maxlength="200" required>
                            <div class="form-text">Maximum 200 characters</div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="5" maxlength="2000" minlength="10" required></textarea>
                            <div class="form-text">Between 10 and 2000 characters</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Overall Rating (Optional)</label>
                            <div class="rating-stars">
                                <input type="radio" id="star5" name="rating" value="5">
                                <label for="star5" title="5 stars"><i class="fas fa-star"></i></label>
                                <input type="radio" id="star4" name="rating" value="4">
                                <label for="star4" title="4 stars"><i class="fas fa-star"></i></label>
                                <input type="radio" id="star3" name="rating" value="3">
                                <label for="star3" title="3 stars"><i class="fas fa-star"></i></label>
                                <input type="radio" id="star2" name="rating" value="2">
                                <label for="star2" title="2 stars"><i class="fas fa-star"></i></label>
                                <input type="radio" id="star1" name="rating" value="1">
                                <label for="star1" title="1 star"><i class="fas fa-star"></i></label>
                            </div>
                        </div>

                        <div id="feedbackError" class="alert alert-danger d-none"></div>
                        <div id="feedbackSuccess" class="alert alert-success d-none"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="submitFeedbackBtn">
                            <i class="fas fa-paper-plane me-1"></i> Send Feedback
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Sidebar toggle
            $("#sidebarToggle").click(function(e) {
                e.preventDefault();
                $("#wrapper").toggleClass("toggled");
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Update unread messages count
            updateUnreadMessagesCount();
            setInterval(updateUnreadMessagesCount, 30000); // Update every 30 seconds

            // Modern sidebar active page highlighting
            const currentPage = window.location.pathname.toLowerCase();
            const navItems = document.querySelectorAll('.nav-item[data-page]');

            navItems.forEach(item => {
                const page = item.getAttribute('data-page');
                if (currentPage.includes(page) ||
                    (page === 'dashboard' && (currentPage.includes('/client/home') || currentPage.includes('/client') && currentPage.split('/').length <= 3))) {
                    item.classList.add('active');
                }
            });

            // Add hover effects for nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.background = 'rgba(255, 255, 255, 0.1)';
                        this.style.transform = 'translateX(4px)';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.background = '';
                        this.style.transform = '';
                    }
                });
            });
        });

        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const themeIconTop = document.getElementById('themeIconTop');

            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                if (themeIcon) themeIcon.className = 'fas fa-moon';
                if (themeIconTop) themeIconTop.className = 'fas fa-moon me-2';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                if (themeIcon) themeIcon.className = 'fas fa-sun';
                if (themeIconTop) themeIconTop.className = 'fas fa-sun me-2';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                const themeIcon = document.getElementById('themeIcon');
                const themeIconTop = document.getElementById('themeIconTop');
                if (themeIcon) themeIcon.className = 'fas fa-sun';
                if (themeIconTop) themeIconTop.className = 'fas fa-sun me-2';
            }
        });

        // Update unread messages count
        function updateUnreadMessagesCount() {
            fetch('@Url.Action("GetUnreadCount", "Messages")')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('unreadMessagesBadge');
                    if (badge && data.count > 0) {
                        badge.textContent = data.count;
                        badge.style.display = 'inline';
                    } else if (badge) {
                        badge.style.display = 'none';
                    }
                })
                .catch(error => console.error('Error updating message count:', error));
        }

        // Feedback modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const feedbackModal = document.getElementById('feedbackModal');
            const feedbackForm = document.getElementById('feedbackForm');
            const projectSelect = document.getElementById('projectId');

            // Load projects when modal is shown
            feedbackModal.addEventListener('show.bs.modal', function() {
                loadClientProjects();
            });

            // Handle form submission
            feedbackForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitFeedback();
            });

            // Star rating functionality
            const stars = document.querySelectorAll('.rating-stars input[type="radio"]');
            const starLabels = document.querySelectorAll('.rating-stars label');

            starLabels.forEach((label, index) => {
                label.addEventListener('mouseover', function() {
                    highlightStars(5 - index);
                });

                label.addEventListener('click', function() {
                    const rating = this.getAttribute('for').replace('star', '');
                    document.getElementById('star' + rating).checked = true;
                    highlightStars(parseInt(rating));
                });
            });

            document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
                const checkedStar = document.querySelector('.rating-stars input[type="radio"]:checked');
                if (checkedStar) {
                    highlightStars(parseInt(checkedStar.value));
                } else {
                    highlightStars(0);
                }
            });

            function highlightStars(rating) {
                starLabels.forEach((label, index) => {
                    if (5 - index <= rating) {
                        label.style.color = '#ffc107';
                    } else {
                        label.style.color = '#dee2e6';
                    }
                });
            }
        });

        function loadClientProjects() {
            fetch('@Url.Action("GetClientProjects", "Feedback")')
                .then(response => response.json())
                .then(data => {
                    const projectSelect = document.getElementById('projectId');
                    projectSelect.innerHTML = '<option value="">Select a project</option>';

                    if (data.success && data.projects) {
                        data.projects.forEach(project => {
                            const option = document.createElement('option');
                            option.value = project.id;
                            option.textContent = project.name;
                            projectSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error loading projects:', error));
        }

        function submitFeedback() {
            const form = document.getElementById('feedbackForm');
            const submitBtn = document.getElementById('submitFeedbackBtn');
            const errorDiv = document.getElementById('feedbackError');
            const successDiv = document.getElementById('feedbackSuccess');

            // Hide previous messages
            errorDiv.classList.add('d-none');
            successDiv.classList.add('d-none');

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Sending...';

            // Get form data
            const formData = new FormData(form);

            // Submit feedback
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    successDiv.textContent = data.message;
                    successDiv.classList.remove('d-none');

                    // Reset form
                    form.reset();

                    // Reset star rating
                    document.querySelectorAll('.rating-stars label').forEach(label => {
                        label.style.color = '#dee2e6';
                    });

                    // Close modal after 3 seconds
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('feedbackModal'));
                        modal.hide();
                    }, 3000);
                } else {
                    errorDiv.textContent = data.message;
                    errorDiv.classList.remove('d-none');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred while sending your feedback. Please try again.';
                errorDiv.classList.remove('d-none');
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane me-1"></i> Send Feedback';
            });
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
