@{
    ViewData["Title"] = "Dashboard";
}

<div class="container-fluid p-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">Welcome Back!</h1>
                    <p class="text-muted">Here's what's happening with your projects today.</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" id="refreshDashboard">
                        <i class="fas fa-sync-alt me-1"></i> Refresh
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar me-1"></i> This Month
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">This Week</a></li>
                            <li><a class="dropdown-item" href="#">This Month</a></li>
                            <li><a class="dropdown-item" href="#">This Quarter</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card fade-in">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-label">Active Projects</div>
                        <div class="stats-number">@ViewBag.ProjectCount</div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-arrow-up text-success me-1"></i>
                            <small class="text-success">+2 this month</small>
                        </div>
                    </div>
                    <div class="stats-icon primary">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info fade-in" style="animation-delay: 0.1s;">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-label">Total Invoices</div>
                        <div class="stats-number">@ViewBag.InvoiceCount</div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-arrow-down text-danger me-1"></i>
                            <small class="text-danger">-1 this month</small>
                        </div>
                    </div>
                    <div class="stats-icon info">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning fade-in" style="animation-delay: 0.2s;">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-label">Pending Invoices</div>
                        <div class="stats-number">@ViewBag.PendingInvoiceCount</div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-clock text-warning me-1"></i>
                            <small class="text-warning">Needs attention</small>
                        </div>
                    </div>
                    <div class="stats-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success fade-in" style="animation-delay: 0.3s;">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-label">Messages</div>
                        <div class="stats-number">12</div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope text-info me-1"></i>
                            <small class="text-info">3 unread</small>
                        </div>
                    </div>
                    <div class="stats-icon success">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Projects")" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-project-diagram fa-2x mb-2"></i>
                                <span>View Projects</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Messages")" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-comments fa-2x mb-2"></i>
                                <span>Send Message</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Invoices")" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-file-invoice-dollar fa-2x mb-2"></i>
                                <span>View Invoices</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Documents")" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-file-alt fa-2x mb-2"></i>
                                <span>Documents</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Projects -->
        <div class="col-lg-8 mb-4">
            <div class="card slide-in-left">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Projects</h5>
                    <a asp-area="Client" asp-controller="Projects" asp-action="Index" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye me-1"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    @if (ViewBag.RecentProjects != null && ViewBag.RecentProjects.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0">Project</th>
                                        <th class="border-0">Service</th>
                                        <th class="border-0">Status</th>
                                        <th class="border-0">Progress</th>
                                        <th class="border-0">Date</th>
                                        <th class="border-0">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var project in ViewBag.RecentProjects)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="fas fa-project-diagram text-white"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">@project.Name</h6>
                                                        <small class="text-muted">@project.Description.Substring(0, Math.Min(50, project.Description.Length))...</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">@(project.Service?.Name ?? "N/A")</span>
                                            </td>
                                            <td>
                                                <span class="status-indicator in-progress">In Progress</span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-primary" style="width: 75%"></div>
                                                </div>
                                                <small class="text-muted">75%</small>
                                            </td>
                                            <td>
                                                <small class="text-muted">@project.CreatedAt.ToString("MMM dd, yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a asp-area="Client" asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-area="Client" asp-controller="Projects" asp-action="Messages" asp-route-id="@project.Id" class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-comment"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No projects yet</h5>
                            <p class="text-muted">Your projects will appear here once they're created.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Recent Activity & Notifications -->
        <div class="col-lg-4 mb-4">
            <div class="card slide-in-left" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-icon bg-success">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Project Milestone Completed</h6>
                                <p class="text-muted mb-1">Website design phase completed</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon bg-info">
                                <i class="fas fa-comment"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">New Message</h6>
                                <p class="text-muted mb-1">Team sent you an update</p>
                                <small class="text-muted">5 hours ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon bg-warning">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Invoice Generated</h6>
                                <p class="text-muted mb-1">Invoice #INV-2024-001 created</p>
                                <small class="text-muted">1 day ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Document Uploaded</h6>
                                <p class="text-muted mb-1">Project requirements.pdf</p>
                                <small class="text-muted">2 days ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices Summary -->
            <div class="card mt-4 slide-in-left" style="animation-delay: 0.3s;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Invoices</h5>
                    <a asp-area="Client" asp-controller="Invoices" asp-action="Index" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if (ViewBag.RecentInvoicesForDisplay != null && ViewBag.RecentInvoicesForDisplay.Count > 0)
                    {
                        @foreach (var invoice in ViewBag.RecentInvoicesForDisplay)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-3 pb-3 border-bottom">
                                <div>
                                    <h6 class="mb-1">@invoice.InvoiceNumber</h6>
                                    <small class="text-muted">Due: @invoice.DueDate.ToString("MMM dd, yyyy")</small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">$@invoice.TotalAmount.ToString("N2")</div>
                                    @if (invoice.Status == "Paid")
                                    {
                                        <span class="status-indicator completed">Paid</span>
                                    }
                                    else if (invoice.Status == "Pending")
                                    {
                                        <span class="status-indicator pending">Pending</span>
                                    }
                                    else if (invoice.Status == "Overdue")
                                    {
                                        <span class="status-indicator overdue">Overdue</span>
                                    }
                                    else
                                    {
                                        <span class="status-indicator">@invoice.Status</span>
                                    }
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No invoices yet</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add custom styles for activity feed -->
<style>
.activity-feed {
    position: relative;
}

.activity-item {
    display: flex;
    margin-bottom: 1.5rem;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 35px;
    width: 2px;
    height: calc(100% + 10px);
    background: var(--border-color);
}

.activity-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    color: white;
    font-size: 0.75rem;
}

.activity-content h6 {
    font-size: 0.875rem;
    font-weight: 600;
}

.activity-content p {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>

<!-- Dashboard JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Refresh dashboard functionality
    document.getElementById('refreshDashboard')?.addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Refreshing...';
        setTimeout(() => {
            location.reload();
        }, 1000);
    });

    // Add loading animation to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
});
</script>
