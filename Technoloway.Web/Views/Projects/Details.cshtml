@model Technoloway.Web.Models.ProjectDetailsViewModel

@{
    ViewData["Title"] = Model.Project.Name;
    ViewData["MetaDescription"] = $"Learn more about our {Model.Project.Name} project. {Model.Project.Description.Substring(0, <PERSON><PERSON>Min(Model.Project.Description.Length, 150))}";
    ViewData["MetaKeywords"] = $"portfolio, projects, {Model.Project.Name.ToLower()}, {Model.Project.Service?.Name.ToLower()}, case study";
}

<!-- <PERSON> Header -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="fw-bold">@Model.Project.Name</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Portfolio</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">@Model.Project.Name</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Project Details -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card border-0 shadow mb-4">
                    <img src="@Model.Project.ImageUrl" class="card-img-top" alt="@Model.Project.Name">
                    <div class="card-body p-4">
                        <div class="d-flex flex-wrap mb-3">
                            <span class="badge bg-primary me-2 mb-2">@(Model.Project.Service?.Name ?? "Other")</span>
                            <span class="badge bg-secondary me-2 mb-2">
                                <i class="fas fa-calendar-alt me-1"></i> @Model.Project.CompletionDate.ToString("MMMM yyyy")
                            </span>
                            <span class="badge bg-info text-dark me-2 mb-2">
                                <i class="fas fa-building me-1"></i> @Model.Project.ClientName
                            </span>
                        </div>

                        <h2 class="fw-bold mb-3">Project Overview</h2>
                        <p class="lead mb-4">@Model.Project.Description</p>

                        <h3 class="fw-bold mb-3">The Challenge</h3>
                        <p>@Model.Project.ClientName needed a solution that would help them streamline their operations and improve customer experience. They were facing challenges with their existing systems, which were outdated and inefficient.</p>

                        <h3 class="fw-bold mb-3">Our Solution</h3>
                        <p>We developed a custom @(Model.Project.Service?.Name ?? "software") solution that addressed all of @Model.Project.ClientName's requirements. Our team worked closely with the client to ensure that the solution was tailored to their specific needs and business processes.</p>

                        <h3 class="fw-bold mb-3">Technologies Used</h3>
                        <div class="d-flex flex-wrap mb-4">
                            @foreach (var tech in Model.Technologies)
                            {
                                <div class="me-4 mb-3 text-center">
                                    <img src="@tech.IconUrl" alt="@tech.Name" class="img-fluid mb-2" style="height: 50px;">
                                    <p class="mb-0 small">@tech.Name</p>
                                </div>
                            }
                        </div>

                        <h3 class="fw-bold mb-3">Results</h3>
                        <p>The implementation of our solution resulted in significant improvements for @Model.Project.ClientName:</p>
                        <ul>
                            <li>Increased operational efficiency by 30%</li>
                            <li>Reduced customer response time by 50%</li>
                            <li>Improved data accuracy and reporting capabilities</li>
                            <li>Enhanced user experience and customer satisfaction</li>
                        </ul>

                        @if (!string.IsNullOrEmpty(Model.Project.ProjectUrl))
                        {
                            <div class="text-center mt-4">
                                <a href="@Model.Project.ProjectUrl" target="_blank" class="btn btn-primary btn-lg">
                                    <i class="fas fa-external-link-alt me-2"></i> Visit Project
                                </a>
                            </div>
                        }
                    </div>
                </div>

                <!-- Client Testimonial -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Client Testimonial</h3>
                        <div class="testimonial">
                            <p class="lead fst-italic mb-3">
                                <i class="fas fa-quote-left me-2 text-primary"></i>
                                Working with Technoloway on our @Model.Project.Name project was a great experience. Their team was professional, responsive, and delivered exactly what we needed. The solution has significantly improved our operations and helped us better serve our customers.
                                <i class="fas fa-quote-right ms-2 text-primary"></i>
                            </p>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <img src="/images/testimonials/client-avatar.jpg" alt="Client" class="rounded-circle" width="60" height="60">
                                </div>
                                <div>
                                    <h5 class="fw-bold mb-0">John Smith</h5>
                                    <p class="mb-0">CTO, @Model.Project.ClientName</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Project Info -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Project Information</h3>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Client</h6>
                                        <p>@Model.Project.ClientName</p>
                                    </div>
                                </div>
                            </li>
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Service</h6>
                                        <p>@(Model.Project.Service?.Name ?? "Custom Software")</p>
                                    </div>
                                </div>
                            </li>
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Completion Date</h6>
                                        <p>@Model.Project.CompletionDate.ToString("MMMM yyyy")</p>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Project Duration</h6>
                                        <p>3 months</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Related Projects -->
                @if (Model.RelatedProjects != null && Model.RelatedProjects.Any())
                {
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-3">Related Projects</h3>
                            @foreach (var relatedProject in Model.RelatedProjects)
                            {
                                <div class="mb-3">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <img src="@relatedProject.ImageUrl" alt="@relatedProject.Name" class="img-fluid rounded" width="80" height="80" style="object-fit: cover;">
                                        </div>
                                        <div>
                                            <h5 class="fw-bold mb-1">@relatedProject.Name</h5>
                                            <p class="mb-2 small">@relatedProject.Description.Substring(0, Math.Min(relatedProject.Description.Length, 60))...</p>
                                            <a asp-action="Details" asp-route-id="@relatedProject.Id" class="btn btn-sm btn-outline-primary">View Project</a>
                                        </div>
                                    </div>
                                </div>
                                @if (relatedProject != Model.RelatedProjects.Last())
                                {
                                    <hr />
                                }
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <h2 class="fw-bold">Ready to Start Your Project?</h2>
                <p class="lead mb-0">Contact us today to discuss how we can help you achieve your business goals.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/Contact" class="btn btn-primary btn-lg">Get in Touch</a>
            </div>
        </div>
    </div>
</section>
