@model Technoloway.Web.Models.BlogIndexViewModel

@{
    ViewData["Title"] = "Blog";
    ViewData["MetaDescription"] = "Stay updated with the latest news, insights, and trends in software development from Technoloway.";
    ViewData["MetaKeywords"] = "blog, software development, tech news, programming, web development, mobile apps";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
<partial name="_HeroSection" />

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header fixed-height-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Blog</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Our</span> Blog
                        </h1>
                        <p class="page-subtitle">
                            Stay updated with the latest insights, trends, and news in software development from our expert team.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Blog Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row">
            <!-- Blog Posts -->
            <div class="col-lg-8 mb-4 mb-lg-0">
                @if (Model.Posts != null && Model.Posts.Any())
                {
                    <div class="row g-4">
                        @{
                            int postIndex = 0;
                        }
                        @foreach (var post in Model.Posts)
                        {
                            <div class="col-12 col-md-6 col-xl-4">
                                <div class="modern-card blog-post-card animate-on-scroll" data-delay="@((postIndex % 3 + 1) * 100)">
                                    <div class="blog-image-wrapper">
                                        <img src="@post.FeaturedImageUrl" alt="@post.Title" class="blog-featured-image" loading="lazy">
                                        <div class="blog-category-badge">
                                            @if (!string.IsNullOrEmpty(post.Categories))
                                            {
                                                var categories = post.Categories.Split(',');
                                                foreach (var category in categories.Take(1))
                                                {
                                                    <span class="category-tag">
                                                        @category.Trim()
                                                    </span>
                                                }
                                            }
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <h3 class="card-title">
                                            <a href="@Url.Action("Details", new { slug = post.Slug })" class="blog-title-link">@post.Title</a>
                                        </h3>
                                        <p class="card-subtitle mb-4">
                                            @post.Excerpt
                                        </p>
                                        <div class="blog-meta mb-4">
                                            <div class="blog-author">
                                                <i class="fas fa-user"></i>
                                                <span>By @post.AuthorName</span>
                                            </div>
                                            <div class="blog-date">
                                                <i class="fas fa-calendar"></i>
                                                <span>@post.PublishedAt?.ToString("MMM dd, yyyy")</span>
                                            </div>
                                        </div>
                                        <div class="mt-auto">
                                            <a href="@Url.Action("Details", new { slug = post.Slug })" class="modern-btn primary w-100">
                                                <span class="btn-text">Read More</span>
                                                <span class="btn-icon">
                                                    <i class="fas fa-arrow-right"></i>
                                                </span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            postIndex++;
                        }
                    </div>

                    <!-- Modern Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <div class="modern-pagination mt-5">
                            <div class="pagination-wrapper">
                                @if (Model.CurrentPage > 1)
                                {
                                    <a href="@Url.Action("Index", new { page = Model.CurrentPage - 1, category = Model.Category })" class="pagination-btn prev-btn">
                                        <i class="fas fa-chevron-left"></i>
                                        <span>Previous</span>
                                    </a>
                                }

                                <div class="pagination-numbers">
                                    @for (int i = 1; i <= Model.TotalPages; i++)
                                    {
                                        <a href="@Url.Action("Index", new { page = i, category = Model.Category })"
                                           class="pagination-number @(i == Model.CurrentPage ? "active" : "")">@i</a>
                                    }
                                </div>

                                @if (Model.CurrentPage < Model.TotalPages)
                                {
                                    <a href="@Url.Action("Index", new { page = Model.CurrentPage + 1, category = Model.Category })" class="pagination-btn next-btn">
                                        <span>Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="modern-card empty-state-card animate-on-scroll">
                        <div class="card-body text-center">
                            <div class="empty-state-icon">
                                <i class="fas fa-blog"></i>
                            </div>
                            <h3 class="card-title">No Blog Posts Found</h3>
                            <p class="card-subtitle mb-4">We're working on creating new content. Please check back later for updates.</p>
                            <a href="/" class="modern-btn primary">
                                <span class="btn-text">Back to Home</span>
                                <span class="btn-icon">
                                    <i class="fas fa-home"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                }
            </div>

            <!-- Sidebar - Modern Design -->
            <div class="col-lg-4">
                <!-- Search Widget -->
                <div class="modern-card search-card animate-on-scroll mb-4" data-delay="100">
                    <div class="card-header">
                        <h4 class="card-title">
                            <span class="title-highlight">Search</span> Posts
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="get" action="@Url.Action("Index")">
                            <div class="search-input-wrapper">
                                <input type="text" class="search-input" placeholder="Search for..." name="search">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Categories Widget -->
                <div class="modern-card categories-card animate-on-scroll mb-4" data-delay="200">
                    <div class="card-header">
                        <h4 class="card-title">
                            <span class="title-highlight">Categories</span>
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="categories-list">
                            <a href="@Url.Action("Index")" class="category-link @(string.IsNullOrEmpty(Model.Category) ? "active" : "")">
                                <span>All Categories</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="@Url.Action("Index", new { category = "Web Development" })" class="category-link @(Model.Category == "Web Development" ? "active" : "")">
                                <span>Web Development</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="@Url.Action("Index", new { category = "Mobile Development" })" class="category-link @(Model.Category == "Mobile Development" ? "active" : "")">
                                <span>Mobile Development</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="@Url.Action("Index", new { category = "UI/UX Design" })" class="category-link @(Model.Category == "UI/UX Design" ? "active" : "")">
                                <span>UI/UX Design</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="@Url.Action("Index", new { category = "Cloud Computing" })" class="category-link @(Model.Category == "Cloud Computing" ? "active" : "")">
                                <span>Cloud Computing</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="@Url.Action("Index", new { category = "DevOps" })" class="category-link @(Model.Category == "DevOps" ? "active" : "")">
                                <span>DevOps</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Newsletter Widget -->
                <div class="modern-card newsletter-card animate-on-scroll" data-delay="300">
                    <div class="card-header">
                        <h4 class="card-title">
                            <span class="title-highlight">Newsletter</span>
                        </h4>
                        <p class="card-subtitle">Get the latest updates and news delivered to your inbox.</p>
                    </div>
                    <div class="card-body">
                        <form class="newsletter-form">
                            <div class="newsletter-input-wrapper">
                                <input type="email" class="newsletter-input" placeholder="Your Email Address" required>
                            </div>
                            <button type="submit" class="modern-btn primary w-100 mt-3">
                                <span class="btn-text">Subscribe</span>
                                <span class="btn-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Blog post card hover effects
            const blogCards = document.querySelectorAll('.blog-post-card');

            blogCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    const image = this.querySelector('.blog-featured-image');
                    if (image) {
                        image.style.transform = 'scale(1.05)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const image = this.querySelector('.blog-featured-image');
                    if (image) {
                        image.style.transform = 'scale(1)';
                    }
                });
            });

            // Category link hover effects
            const categoryLinks = document.querySelectorAll('.category-link');

            categoryLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'translateX(5px)';
                    }
                });

                link.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'translateX(0)';
                    }
                });
            });

            // Search input focus effects
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('focus', function() {
                    this.parentElement.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                });

                searchInput.addEventListener('blur', function() {
                    this.parentElement.style.boxShadow = 'none';
                });
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}
