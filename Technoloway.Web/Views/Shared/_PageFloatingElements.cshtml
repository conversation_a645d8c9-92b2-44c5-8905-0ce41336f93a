@{
    var pageName = ViewBag.PageName?.ToString() ?? "";
}

@switch (pageName.ToLower())
{
    case "services":
        <div class="floating-element services-icon">
            <i class="fas fa-cogs"></i>
        </div>
        <div class="floating-element services-icon-2">
            <i class="fas fa-code"></i>
        </div>
        <div class="floating-element services-icon-3">
            <i class="fas fa-rocket"></i>
        </div>
        break;

    case "projects":
    case "portfolio":
        <div class="floating-element portfolio-icon">
            <i class="fas fa-folder-open"></i>
        </div>
        <div class="floating-element portfolio-icon-2">
            <i class="fas fa-project-diagram"></i>
        </div>
        <div class="floating-element portfolio-icon-3">
            <i class="fas fa-trophy"></i>
        </div>
        break;

    case "technologies":
        <div class="floating-element tech-icon">
            <i class="fas fa-microchip"></i>
        </div>
        <div class="floating-element tech-icon-2">
            <i class="fas fa-laptop-code"></i>
        </div>
        <div class="floating-element tech-icon-3">
            <i class="fas fa-database"></i>
        </div>
        break;

    case "blog":
        <div class="floating-element blog-icon">
            <i class="fas fa-blog"></i>
        </div>
        <div class="floating-element blog-icon-2">
            <i class="fas fa-pen-fancy"></i>
        </div>
        <div class="floating-element blog-icon-3">
            <i class="fas fa-newspaper"></i>
        </div>
        break;

    case "careers":
        <div class="floating-element careers-icon">
            <i class="fas fa-briefcase"></i>
        </div>
        <div class="floating-element careers-icon-2">
            <i class="fas fa-users"></i>
        </div>
        <div class="floating-element careers-icon-3">
            <i class="fas fa-chart-line"></i>
        </div>
        break;

    case "contact":
    case "contact us":
        <div class="floating-element contact-icon">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="floating-element phone-icon">
            <i class="fas fa-phone"></i>
        </div>
        <div class="floating-element location-icon">
            <i class="fas fa-map-marker-alt"></i>
        </div>
        break;

    case "about":
    case "about us":
        <div class="floating-element about-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="floating-element about-icon-2">
            <i class="fas fa-heart"></i>
        </div>
        <div class="floating-element about-icon-3">
            <i class="fas fa-lightbulb"></i>
        </div>
        break;

    default:
        <div class="floating-element default-icon">
            <i class="fas fa-star"></i>
        </div>
        <div class="floating-element default-icon-2">
            <i class="fas fa-rocket"></i>
        </div>
        <div class="floating-element default-icon-3">
            <i class="fas fa-cog"></i>
        </div>
        break;
}
