@{
    var heroSection = ViewBag.HeroSection as Technoloway.Core.Entities.HeroSection;
    var pageName = ViewBag.PageName?.ToString() ?? "";
    var isHomePage = pageName.ToLower() == "home";
}

@if (heroSection != null)
{
    <!-- Dynamic Hero Section -->
    <div class="modern-page-header @(!isHomePage ? "fixed-height-hero" : "")">
        @if (heroSection.EnableSlideshow && heroSection.Slides.Any())
        {
            <!-- Hero Slideshow -->
            <div class="hero-slideshow"
                 data-autoplay="@heroSection.AutoPlay.ToString().ToLower()"
                 data-speed="@heroSection.SlideshowSpeed"
                 data-show-dots="@heroSection.ShowDots.ToString().ToLower()"
                 data-show-arrows="@heroSection.ShowArrows.ToString().ToLower()">

                @foreach (var slide in heroSection.Slides.OrderBy(s => s.DisplayOrder))
                {
                    <div class="hero-slide"
                         data-animation="@slide.AnimationType"
                         data-duration="@slide.Duration">

                        @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                        {
                            <div class="hero-video-container @(!isHomePage ? "fixed-height-media" : "")">
                                <video class="hero-video"
                                       @(slide.VideoAutoPlay ? "autoplay" : "")
                                       @(slide.VideoMuted ? "muted" : "")
                                       @(slide.VideoLoop ? "loop" : "")
                                       @(slide.VideoControls ? "controls" : "")>
                                    <source src="@slide.VideoUrl" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                        }
                        else if (!string.IsNullOrEmpty(slide.ImageUrl))
                        {
                            <div class="hero-image-container @(!isHomePage ? "fixed-height-media" : "")">
                                <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="hero-image">
                            </div>
                        }

                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <div class="page-header-content">
                                        @Html.Raw(slide.Content)

                                        @if (!string.IsNullOrEmpty(slide.ButtonText) && !string.IsNullOrEmpty(slide.ButtonUrl))
                                        {
                                            <div class="hero-actions mt-4">
                                                <a href="@slide.ButtonUrl" class="modern-btn primary">
                                                    <span class="btn-text">@slide.ButtonText</span>
                                                    <span class="btn-icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="page-header-visual">
                                        @await Html.PartialAsync("_PageFloatingElements")
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (heroSection.ShowArrows)
                {
                    <div class="hero-navigation">
                        <button class="hero-nav-btn hero-prev" aria-label="Previous slide">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="hero-nav-btn hero-next" aria-label="Next slide">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                }

                @if (heroSection.ShowDots)
                {
                    <div class="hero-dots">
                        @for (int i = 0; i < heroSection.Slides.Count; i++)
                        {
                            <button class="hero-dot @(i == 0 ? "active" : "")" data-slide="@i" aria-label="Go to slide @(i + 1)"></button>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Single Hero Section -->
            <div class="container">
                <div class="row align-items-center">
                    <div class="@(isHomePage ? "col-lg-8" : "col-12 text-center")">
                        <div class="page-header-content">
                            @if (!isHomePage)
                            {
                                <div class="page-breadcrumb">
                                    <a href="/" class="breadcrumb-link">
                                        <i class="fas fa-home"></i>
                                        <span>Home</span>
                                    </a>
                                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                                    <span class="breadcrumb-current">@ViewBag.PageName</span>
                                </div>
                            }
                            <h1 class="page-title">@Html.Raw(heroSection.MainTitle)</h1>
                            @if (!string.IsNullOrEmpty(heroSection.MainSubtitle))
                            {
                                <p class="page-subtitle">@Html.Raw(heroSection.MainSubtitle)</p>
                            }
                            @if (!string.IsNullOrEmpty(heroSection.MainDescription))
                            {
                                <p class="page-subtitle">@Html.Raw(heroSection.MainDescription)</p>
                            }
                        </div>
                    </div>
                    @if (isHomePage)
                    {
                        <div class="col-lg-4">
                            <div class="page-header-visual">
                                @await Html.PartialAsync("_PageFloatingElements")
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        @if (heroSection.EnableFloatingElements && !string.IsNullOrEmpty(heroSection.FloatingElementsConfig))
        {
            <!-- Floating Elements -->
            <div class="floating-elements" data-config="@heroSection.FloatingElementsConfig"></div>
        }
    </div>

    <!-- Hero Section Styles -->
    <style>
        /* Fixed Height Hero Section */
        .fixed-height-hero {
            height: 250px !important;
            min-height: 250px !important;
            max-height: 250px !important;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
        }

        .fixed-height-hero .container {
            position: relative;
            z-index: 2;
        }

        /* Fixed Height Media Containers */
        .fixed-height-media {
            position: absolute !important;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .fixed-height-media .hero-image,
        .fixed-height-media .hero-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        /* Overlay for better text readability on fixed height heroes */
        .fixed-height-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
            z-index: 1;
        }

        .fixed-height-hero .page-header-content {
            color: white;
            position: relative;
            z-index: 2;
        }

        .fixed-height-hero .page-title {
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .fixed-height-hero .page-subtitle {
            color: rgba(255,255,255,0.9);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .fixed-height-hero .breadcrumb-link,
        .fixed-height-hero .breadcrumb-current,
        .fixed-height-hero .breadcrumb-separator {
            color: rgba(255,255,255,0.8);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .fixed-height-hero .breadcrumb-link:hover {
            color: white;
        }

        /* Hero Image Display (for Home page) */
        .hero-image-display {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .hero-image-display:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .hero-image-display img {
            transition: transform 0.3s ease;
        }

        .hero-image-display:hover img {
            transform: scale(1.05);
        }

        /* Hero Slideshow Styles */
        .hero-slideshow {
            position: relative;
            width: 100%;
            height: 100%;
            min-height: 400px;
        }

        .fixed-height-hero .hero-slideshow {
            height: 250px;
            min-height: 250px;
        }

        .hero-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 1s ease-in-out;
            display: flex;
            align-items: center;
            padding: 60px 0 40px;
        }

        .fixed-height-hero .hero-slide {
            padding: 0;
        }

        .hero-slide.active {
            opacity: 1;
        }

        .hero-image-container,
        .hero-video-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-image,
        .hero-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .hero-navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 2rem;
            z-index: 3;
        }

        .hero-nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hero-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .hero-dots {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 0.5rem;
            z-index: 3;
        }

        .hero-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hero-dot.active {
            background: white;
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .fixed-height-hero {
                height: 200px !important;
                min-height: 200px !important;
                max-height: 200px !important;
            }

            .fixed-height-hero .hero-slideshow {
                height: 200px;
                min-height: 200px;
            }

            .hero-navigation {
                padding: 0 1rem;
            }

            .hero-nav-btn {
                width: 40px;
                height: 40px;
            }
        }

        @@media (max-width: 576px) {
            .fixed-height-hero {
                height: 180px !important;
                min-height: 180px !important;
                max-height: 180px !important;
            }

            .fixed-height-hero .hero-slideshow {
                height: 180px;
                min-height: 180px;
            }

            .fixed-height-hero .page-title {
                font-size: 1.8rem;
            }

            .fixed-height-hero .page-subtitle {
                font-size: 0.9rem;
            }
        }
    </style>

    <!-- Hero Section JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slideshow = document.querySelector('.hero-slideshow');
            if (!slideshow) return;

            const slides = slideshow.querySelectorAll('.hero-slide');
            const dots = slideshow.querySelectorAll('.hero-dot');
            const prevBtn = slideshow.querySelector('.hero-prev');
            const nextBtn = slideshow.querySelector('.hero-next');

            let currentSlide = 0;
            let autoplayInterval;

            const autoplay = slideshow.dataset.autoplay === 'true';
            const speed = parseInt(slideshow.dataset.speed) || 5000;

            function showSlide(index) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
                dots.forEach((dot, i) => {
                    dot.classList.toggle('active', i === index);
                });
                currentSlide = index;
            }

            function nextSlide() {
                const next = (currentSlide + 1) % slides.length;
                showSlide(next);
            }

            function prevSlide() {
                const prev = (currentSlide - 1 + slides.length) % slides.length;
                showSlide(prev);
            }

            function startAutoplay() {
                if (autoplay && slides.length > 1) {
                    autoplayInterval = setInterval(nextSlide, speed);
                }
            }

            function stopAutoplay() {
                if (autoplayInterval) {
                    clearInterval(autoplayInterval);
                }
            }

            // Initialize
            if (slides.length > 0) {
                showSlide(0);
                startAutoplay();
            }

            // Event listeners
            if (nextBtn) nextBtn.addEventListener('click', () => { stopAutoplay(); nextSlide(); startAutoplay(); });
            if (prevBtn) prevBtn.addEventListener('click', () => { stopAutoplay(); prevSlide(); startAutoplay(); });

            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    stopAutoplay();
                    showSlide(index);
                    startAutoplay();
                });
            });

            // Pause on hover
            slideshow.addEventListener('mouseenter', stopAutoplay);
            slideshow.addEventListener('mouseleave', startAutoplay);
        });
    </script>
}
