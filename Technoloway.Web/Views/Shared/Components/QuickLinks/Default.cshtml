@model Technoloway.Web.ViewComponents.QuickLinksViewModel

@{
    string GetLinkDisplayName(string key)
    {
        return key.Replace("Link", "").Replace("Url", "").Trim() switch
        {
            "Home" => "Home",
            "About" => "About Us",
            "Services" => "Our Services",
            "Projects" => "Projects",
            "Contact" => "Contact Us",
            "Blog" => "Blog",
            "Portfolio" => "Portfolio",
            "Team" => "Our Team",
            "Careers" => "Careers",
            "Privacy" => "Privacy Policy",
            "Terms" => "Terms of Service",
            _ => key.Replace("Link", "").Replace("Url", "").Trim()
        };
    }

    string GetLinkIcon(string key)
    {
        return key.ToLower() switch
        {
            var k when k.Contains("home") => "fas fa-home",
            var k when k.Contains("about") => "fas fa-info-circle",
            var k when k.Contains("service") => "fas fa-cogs",
            var k when k.Contains("project") || k.Contains("portfolio") => "fas fa-project-diagram",
            var k when k.Contains("contact") => "fas fa-envelope",
            var k when k.Contains("blog") => "fas fa-blog",
            var k when k.Contains("team") => "fas fa-users",
            var k when k.Contains("career") => "fas fa-briefcase",
            var k when k.Contains("privacy") => "fas fa-shield-alt",
            var k when k.Contains("terms") => "fas fa-file-contract",
            _ => "fas fa-link"
        };
    }
}

@if (Model.QuickLinksSettings.Any())
{
    <div class="col-md-4 mb-4 mb-md-0">
        <h5>Quick Links</h5>
        <ul class="list-unstyled">
            @foreach (var link in Model.QuickLinksSettings.Where(s => !string.IsNullOrEmpty(s.Value)).OrderBy(s => s.Key))
            {
                <li class="mb-2">
                    <a href="@link.Value" class="text-white text-decoration-none d-flex align-items-center">
                        @if (!string.IsNullOrEmpty(link.Icon))
                        {
                            @if (link.Icon.StartsWith("fa") || link.Icon.Contains("fa-"))
                            {
                                <!-- FontAwesome icon -->
                                <i class="@link.Icon me-2" style="background: none; border: none; width: 20px; display: inline-block;"></i>
                            }
                            else
                            {
                                <!-- Uploaded image icon -->
                                <img src="@link.Icon" alt="@link.Key icon" style="width: 20px; height: 20px; margin-right: 8px; display: inline-block;" />
                            }
                        }
                        else
                        {
                            <!-- Fallback FontAwesome icon -->
                            <i class="@GetLinkIcon(link.Key) me-2" style="background: none; border: none; width: 20px; display: inline-block;"></i>
                        }
                        @GetLinkDisplayName(link.Key)
                    </a>
                </li>
            }
        </ul>
    </div>
}
