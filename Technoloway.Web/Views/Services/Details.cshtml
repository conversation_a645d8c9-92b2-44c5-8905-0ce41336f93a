@model Technoloway.Web.Models.ServiceDetailsViewModel

@{
    ViewData["Title"] = Model.Service.Name;
    ViewData["MetaDescription"] = $"Learn more about our {Model.Service.Name} service. {Model.Service.Description.Substring(0, Math.Min(Model.Service.Description.Length, 150))}";
    ViewData["MetaKeywords"] = $"services, {Model.Service.Name.ToLower()}, software development, IT solutions";
}

<!-- <PERSON> Header -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="fw-bold">@Model.Service.Name</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Services</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">@Model.Service.Name</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Service Details -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4 text-primary">
                            @if (!string.IsNullOrEmpty(Model.Service.IconClass))
                            {
                                @if (Model.Service.IconClass.StartsWith("/images/"))
                                {
                                    <img src="@Model.Service.IconClass" alt="@Model.Service.Name Icon" style="height: 96px; width: 96px; object-fit: contain;" />
                                }
                                else
                                {
                                    <i class="@Model.Service.IconClass fa-4x"></i>
                                }
                            }
                            else
                            {
                                <i class="fas fa-cog fa-4x"></i>
                            }
                        </div>
                        <h2 class="fw-bold mb-3">@Model.Service.Name</h2>
                        <p class="lead mb-4">@Model.Service.Description</p>

                        <h3 class="fw-bold mb-3">Our Approach</h3>
                        <p>At Technoloway, we take a collaborative and strategic approach to @Model.Service.Name. We work closely with you to understand your specific needs and challenges, and then develop a customized solution that addresses those needs effectively.</p>

                        <div class="row mt-4">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Requirements Analysis</h5>
                                        <p>We start by thoroughly understanding your business requirements and objectives.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Strategic Planning</h5>
                                        <p>We develop a comprehensive plan that aligns with your business goals.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Agile Development</h5>
                                        <p>We use agile methodologies to ensure flexibility and continuous improvement.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Quality Assurance</h5>
                                        <p>We implement rigorous testing to ensure high-quality deliverables.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="/Contact" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i> Request a Quote
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Related Projects -->
                @if (Model.RelatedProjects != null && Model.RelatedProjects.Any())
                {
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-4">Related Projects</h3>
                            <div class="row">
                                @foreach (var project in Model.RelatedProjects)
                                {
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <img src="@project.ImageUrl" class="card-img-top" alt="@project.Name">
                                            <div class="card-body">
                                                <h5 class="card-title fw-bold">@project.Name</h5>
                                                <p class="card-text small">@project.Description.Substring(0, Math.Min(project.Description.Length, 100))...</p>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="col-lg-4">
                <!-- Service Info -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Service Information</h3>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Price</h6>
                                        <p>Starting at @Model.Service.Price.ToString("C0")</p>
                                    </div>
                                </div>
                            </li>
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Typical Timeline</h6>
                                        <p>4-12 weeks (depending on project scope)</p>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Team Size</h6>
                                        <p>3-5 specialists</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Testimonials -->
                @if (Model.Testimonials != null && Model.Testimonials.Any())
                {
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-3">Client Testimonials</h3>
                            <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner">
                                    @{
                                        bool isFirst = true;
                                        foreach (var testimonial in Model.Testimonials.Take(3))
                                        {
                                            <div class="carousel-item @(isFirst ? "active" : "")">
                                                <div class="testimonial-item text-center">
                                                    <div class="mb-3">
                                                        @for (int i = 0; i < testimonial.Rating; i++)
                                                        {
                                                            <i class="fas fa-star text-warning"></i>
                                                        }
                                                    </div>
                                                    <p class="mb-3"><i class="fas fa-quote-left me-2 text-primary"></i>@testimonial.Content<i class="fas fa-quote-right ms-2 text-primary"></i></p>
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        @if (!string.IsNullOrEmpty(testimonial.ClientPhotoUrl))
                                                        {
                                                            <img src="@testimonial.ClientPhotoUrl" class="rounded-circle me-3" width="50" height="50" alt="@testimonial.ClientName">
                                                        }
                                                        <div class="text-start">
                                                            <h6 class="fw-bold mb-0">@testimonial.ClientName</h6>
                                                            <small>@testimonial.ClientTitle, @testimonial.ClientCompany</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            isFirst = false;
                                        }
                                    }
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Next</span>
                                </button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <h2 class="fw-bold">Ready to Get Started?</h2>
                <p class="lead mb-0">Contact us today to discuss your @Model.Service.Name needs.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/Contact" class="btn btn-primary btn-lg">Contact Us</a>
            </div>
        </div>
    </div>
</section>
