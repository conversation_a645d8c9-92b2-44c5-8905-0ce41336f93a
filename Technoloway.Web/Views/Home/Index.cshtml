﻿@{
    ViewData["Title"] = "Home";
    ViewData["MetaDescription"] = "Technoloway - Innovative Software Solutions for Your Business";
    ViewData["MetaKeywords"] = "software development, web development, mobile apps, cloud solutions, IT services";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/hero-slideshow.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section with Slideshow -->
@if (ViewBag.HeroSection != null)
{
    var heroSection = ViewBag.HeroSection as Technoloway.Core.Entities.HeroSection;
    <section class="hero-dynamic" id="home">
        @if (heroSection.EnableSlideshow && heroSection.Slides.Any())
        {
            <!-- Hero Slideshow -->
            <div class="hero-slideshow"
                 data-autoplay="@heroSection.AutoPlay.ToString().ToLower()"
                 data-speed="@heroSection.SlideshowSpeed"
                 data-show-dots="@heroSection.ShowDots.ToString().ToLower()"
                 data-show-arrows="@heroSection.ShowArrows.ToString().ToLower()">

                @foreach (var slide in heroSection.Slides.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                {
                    <div class="hero-slide @(slide.MediaType == "video" ? "hero-slide-video" : "")"
                         @if (slide.MediaType == "image")
                         {
                             <text>style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('@slide.ImageUrl');"</text>
                         }
                         data-animation="@slide.AnimationType"
                         data-media-type="@slide.MediaType">

                        @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                        {
                            <div class="hero-video-background">
                                <video class="hero-video"
                                       @if (slide.VideoAutoPlay) { <text>autoplay</text> }
                                       @if (slide.VideoMuted) { <text>muted</text> }
                                       @if (slide.VideoLoop) { <text>loop</text> }
                                       @if (slide.VideoControls) { <text>controls</text> }
                                       playsinline>
                                    <source src="@slide.VideoUrl" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                <div class="hero-video-overlay"></div>
                            </div>
                        }

                        <div class="row align-items-center min-vh-100">
                            <div class="col-lg-8">
                                <div class="hero-content animate-on-scroll">
                                    <div class="hero-slide-content">
                                        @Html.Raw(slide.Content)
                                    </div>
                                        <div class="hero-actions">
                                            @if (!string.IsNullOrEmpty(slide.ButtonText) && !string.IsNullOrEmpty(slide.ButtonUrl))
                                            {
                                                <a href="@slide.ButtonUrl" class="modern-btn primary large">
                                                    <span class="btn-text">@slide.ButtonText</span>
                                                    <span class="btn-icon">
                                                        <i class="fas fa-rocket"></i>
                                                    </span>
                                                </a>
                                            }
                                            @if (!string.IsNullOrEmpty(heroSection.SecondaryButtonText) && !string.IsNullOrEmpty(heroSection.SecondaryButtonUrl))
                                            {
                                                <a href="@heroSection.SecondaryButtonUrl" class="modern-btn secondary large">
                                                    <span class="btn-text">@heroSection.SecondaryButtonText</span>
                                                    <span class="btn-icon">
                                                        <i class="fas fa-comments"></i>
                                                    </span>
                                                </a>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    @if (heroSection.EnableFloatingElements)
                                    {
                                        <div class="hero-visual animate-on-scroll" data-delay="200">
                                            <div class="hero-image-container">
                                                <div class="floating-element hero-element-1">
                                                    <i class="fas fa-code"></i>
                                                </div>
                                                <div class="floating-element hero-element-2">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </div>
                                                <div class="floating-element hero-element-3">
                                                    <i class="fas fa-cloud"></i>
                                                </div>
                                                <div class="floating-element hero-element-4">
                                                    <i class="fas fa-database"></i>
                                                </div>
                                                <div class="hero-center-graphic">
                                                    <div class="graphic-circle"></div>
                                                    <div class="graphic-inner">
                                                        <i class="fas fa-laptop-code"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                }

                @if (heroSection.ShowArrows)
                {
                    <!-- Navigation Arrows -->
                    <div class="hero-nav-arrows">
                        <button class="hero-nav-arrow hero-nav-prev">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="hero-nav-arrow hero-nav-next">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                }

                @if (heroSection.ShowDots)
                {
                    <!-- Navigation Dots -->
                    <div class="hero-nav-dots">
                        @for (int i = 0; i < heroSection.Slides.Count(s => s.IsActive); i++)
                        {
                            <button class="hero-nav-dot @(i == 0 ? "active" : "")" data-slide="@i"></button>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Single Hero Section -->
            <div class="hero-single">
                <div class="hero-background">
                    <div class="hero-particles"></div>
                    <div class="hero-gradient-overlay"></div>
                </div>
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-8">
                        <div class="hero-content animate-on-scroll">
                            <h1 class="hero-title">@Html.Raw(heroSection.MainTitle)</h1>
                                <p class="hero-subtitle">@heroSection.MainSubtitle</p>
                                @if (!string.IsNullOrEmpty(heroSection.MainDescription))
                                {
                                    <p class="hero-description">@heroSection.MainDescription</p>
                                }
                                <div class="hero-actions">
                                    @if (!string.IsNullOrEmpty(heroSection.PrimaryButtonText) && !string.IsNullOrEmpty(heroSection.PrimaryButtonUrl))
                                    {
                                        <a href="@heroSection.PrimaryButtonUrl" class="modern-btn primary large">
                                            <span class="btn-text">@heroSection.PrimaryButtonText</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-rocket"></i>
                                            </span>
                                        </a>
                                    }
                                    @if (!string.IsNullOrEmpty(heroSection.SecondaryButtonText) && !string.IsNullOrEmpty(heroSection.SecondaryButtonUrl))
                                    {
                                        <a href="@heroSection.SecondaryButtonUrl" class="modern-btn secondary large">
                                            <span class="btn-text">@heroSection.SecondaryButtonText</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-comments"></i>
                                            </span>
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            @if (heroSection.EnableFloatingElements)
                            {
                                <div class="hero-visual animate-on-scroll" data-delay="200">
                                    <div class="hero-image-container">
                                        <div class="floating-element hero-element-1">
                                            <i class="fas fa-code"></i>
                                        </div>
                                        <div class="floating-element hero-element-2">
                                            <i class="fas fa-mobile-alt"></i>
                                        </div>
                                        <div class="floating-element hero-element-3">
                                            <i class="fas fa-cloud"></i>
                                        </div>
                                        <div class="floating-element hero-element-4">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div class="hero-center-graphic">
                                            <div class="graphic-circle"></div>
                                            <div class="graphic-inner">
                                                <i class="fas fa-laptop-code"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
            </div>
        }

        <div class="hero-scroll-indicator">
            <div class="scroll-arrow" onclick="scrollToSection('#services')">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>
}
else
{
    <!-- Fallback Hero Section -->
    <section class="hero-enhanced" id="home">
        <div class="hero-background">
            <div class="hero-particles"></div>
            <div class="hero-gradient-overlay"></div>
        </div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content animate-on-scroll">
                        <h1 class="hero-title">
                            Transform Your Business with
                            <span class="hero-highlight">Innovative Software Solutions</span>
                        </h1>
                        <p class="hero-subtitle">
                            We create cutting-edge software solutions that help businesses thrive in the digital world.
                            From web applications to mobile apps, we deliver excellence in every project.
                        </p>
                        <div class="hero-actions">
                            <a href="#services" class="modern-btn primary large">
                                <span class="btn-text">Explore Our Services</span>
                                <span class="btn-icon">
                                    <i class="fas fa-rocket"></i>
                                </span>
                            </a>
                            <a asp-controller="Home" asp-action="Contact" class="modern-btn secondary large">
                                <span class="btn-text">Let's Talk</span>
                                <span class="btn-icon">
                                    <i class="fas fa-comments"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual animate-on-scroll" data-delay="200">
                        <div class="hero-image-container">
                            <div class="floating-element hero-element-1">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="floating-element hero-element-2">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="floating-element hero-element-3">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div class="floating-element hero-element-4">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="hero-center-graphic">
                                <div class="graphic-circle"></div>
                                <div class="graphic-inner">
                                    <i class="fas fa-laptop-code"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-scroll-indicator">
            <div class="scroll-arrow" onclick="scrollToSection('#services')">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>
}

<!-- Enhanced Services Section -->
<section id="services" class="modern-section services-enhanced">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Our</span> Services
                    </h2>
                    <p class="section-subtitle">
                        We offer a comprehensive range of software development services designed to transform your business and drive digital innovation.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @if (ViewBag.Services != null && ViewBag.Services.Count > 0)
            {
                int serviceIndex = 0;
                foreach (var service in ViewBag.Services)
                {
                    <div class="col-md-6 col-lg-3">
                        <div class="modern-card service-card-enhanced animate-on-scroll" data-delay="@((serviceIndex % 4 + 1) * 100)">
                            <div class="service-image-wrapper">
                                <div class="service-icon-display">
                                    @if (!string.IsNullOrEmpty(service.IconClass))
                                    {
                                        @if (service.IconClass.StartsWith("/images/"))
                                        {
                                            <img src="@service.IconClass" alt="@service.Name Icon" class="service-featured-icon" />
                                        }
                                        else
                                        {
                                            <i class="@service.IconClass service-featured-icon"></i>
                                        }
                                    }
                                    else
                                    {
                                        <i class="fas fa-cog service-featured-icon"></i>
                                    }
                                </div>
                                <div class="service-overlay">
                                    <div class="service-overlay-content">
                                        <p class="service-overlay-description">@service.Description</p>
                                        <a asp-controller="Services" asp-action="Details" asp-route-id="@service.Id"
                                           class="modern-btn secondary">
                                            <span class="btn-text">Learn More</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-arrow-right"></i>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <h4 class="card-title">@service.Name</h4>
                            </div>
                        </div>
                    </div>
                    serviceIndex++;
                }
            }
            else
            {
                <div class="modern-card empty-state-card animate-on-scroll">
                    <div class="card-body text-center">
                        <div class="empty-state-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3 class="card-title">No Services Available</h3>
                        <p class="card-subtitle mb-4">We're building amazing services. Check back soon!</p>
                        <a href="/" class="modern-btn primary">
                            <span class="btn-text">Back to Home</span>
                            <span class="btn-icon">
                                <i class="fas fa-home"></i>
                            </span>
                        </a>
                    </div>
                </div>
            }
        </div>

        <div class="text-center mt-5 animate-on-scroll" data-delay="500">
            <a asp-controller="Services" asp-action="Index" class="modern-btn primary large">
                <span class="btn-text">View All Services</span>
                <span class="btn-icon">
                    <i class="fas fa-th-large"></i>
                </span>
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Projects Section -->
<section id="projects" class="modern-section projects-enhanced">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Featured</span> Projects
                    </h2>
                    <p class="section-subtitle">
                        Discover our portfolio of successful projects that showcase our expertise in delivering innovative software solutions.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @if (ViewBag.FeaturedProjects != null && ViewBag.FeaturedProjects.Count > 0)
            {
                int projectIndex = 0;
                foreach (var project in ViewBag.FeaturedProjects)
                {
                    <div class="col-md-6 col-lg-4">
                        <div class="modern-card project-card-enhanced animate-on-scroll" data-delay="@((projectIndex % 3 + 1) * 100)">
                            <div class="project-image-wrapper">
                                <img src="@(string.IsNullOrEmpty(project.ImageUrl) ? "/images/project-placeholder.jpg" : project.ImageUrl)"
                                     alt="@project.Name"
                                     class="project-featured-image"
                                     loading="lazy" />
                                <div class="project-overlay">
                                    <div class="project-overlay-content">
                                        <h4 class="project-overlay-title">@project.Name</h4>
                                        <p class="project-overlay-description">@project.Description</p>
                                        <a asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id"
                                           class="modern-btn secondary">
                                            <span class="btn-text">View Details</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-external-link-alt"></i>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <h4 class="card-title">@project.Name</h4>
                                <p class="card-subtitle">@project.Description</p>
                                <div class="project-meta">
                                    <span class="project-status">Completed</span>
                                    <span class="project-date">@project.CompletionDate?.ToString("MMM yyyy")</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    projectIndex++;
                }
            }
            else
            {
                <div class="modern-card empty-state-card animate-on-scroll">
                    <div class="card-body text-center">
                        <div class="empty-state-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <h3 class="card-title">No Projects Available</h3>
                        <p class="card-subtitle mb-4">We're working on amazing projects. Check back soon!</p>
                        <a href="/" class="modern-btn primary">
                            <span class="btn-text">Back to Home</span>
                            <span class="btn-icon">
                                <i class="fas fa-home"></i>
                            </span>
                        </a>
                    </div>
                </div>
            }
        </div>

        <div class="text-center mt-5 animate-on-scroll" data-delay="500">
            <a asp-controller="Projects" asp-action="Index" class="modern-btn primary large">
                <span class="btn-text">View All Projects</span>
                <span class="btn-icon">
                    <i class="fas fa-briefcase"></i>
                </span>
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Technologies Section -->
<section id="technologies" class="modern-section technologies-enhanced">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Technologies We</span> Master
                    </h2>
                    <p class="section-subtitle">
                        We leverage cutting-edge technologies and frameworks to build robust, scalable, and future-ready solutions.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Technology Carousel -->
    <div class="tech-carousel-enhanced animate-on-scroll" data-delay="300">
        <div class="tech-track-enhanced">
            @if (ViewBag.Technologies != null && ViewBag.Technologies.Count > 0)
            {
                foreach (var technology in ViewBag.Technologies)
                {
                    <div class="tech-item-enhanced">
                        <div class="tech-card">
                            <img src="@(string.IsNullOrEmpty(technology.IconUrl) ? "/images/tech-placeholder.png" : technology.IconUrl)"
                                 alt="@technology.Name"
                                 class="tech-logo-enhanced"
                                 loading="lazy" />
                            <div class="tech-name">@technology.Name</div>
                        </div>
                    </div>
                }
                <!-- Duplicate for seamless loop -->
                foreach (var technology in ViewBag.Technologies)
                {
                    <div class="tech-item-enhanced">
                        <div class="tech-card">
                            <img src="@(string.IsNullOrEmpty(technology.IconUrl) ? "/images/tech-placeholder.png" : technology.IconUrl)"
                                 alt="@technology.Name"
                                 class="tech-logo-enhanced"
                                 loading="lazy" />
                            <div class="tech-name">@technology.Name</div>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Placeholder technologies for demo -->
                <div class="tech-item-enhanced">
                    <div class="tech-card">
                        <img src="/images/tech-placeholder.png" alt="Technology" class="tech-logo-enhanced" />
                        <div class="tech-name">Technology</div>
                    </div>
                </div>
                <div class="tech-item-enhanced">
                    <div class="tech-card">
                        <img src="/images/tech-placeholder.png" alt="Technology" class="tech-logo-enhanced" />
                        <div class="tech-name">Technology</div>
                    </div>
                </div>
                <div class="tech-item-enhanced">
                    <div class="tech-card">
                        <img src="/images/tech-placeholder.png" alt="Technology" class="tech-logo-enhanced" />
                        <div class="tech-name">Technology</div>
                    </div>
                </div>
                <div class="tech-item-enhanced">
                    <div class="tech-card">
                        <img src="/images/tech-placeholder.png" alt="Technology" class="tech-logo-enhanced" />
                        <div class="tech-name">Technology</div>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="container">
        <div class="text-center mt-5 animate-on-scroll" data-delay="500">
            <a asp-controller="Technologies" asp-action="Index" class="modern-btn primary large">
                <span class="btn-text">Explore All Technologies</span>
                <span class="btn-icon">
                    <i class="fas fa-code"></i>
                </span>
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Testimonials Section -->
<section id="testimonials" class="modern-section testimonials-enhanced">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">What Our Clients</span> Say
                    </h2>
                    <p class="section-subtitle">
                        Don't just take our word for it. Here's what our satisfied clients have to say about our services and expertise.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @if (ViewBag.Testimonials != null && ViewBag.Testimonials.Count > 0)
            {
                int testimonialIndex = 0;
                foreach (var testimonial in ViewBag.Testimonials)
                {
                    <div class="col-md-6 col-lg-4">
                        <div class="modern-card testimonial-card-enhanced animate-on-scroll" data-delay="@((testimonialIndex % 3 + 1) * 100)">
                            <div class="card-body">
                                <div class="testimonial-rating">
                                    @for (int i = 0; i < testimonial.Rating; i++)
                                    {
                                        <i class="fas fa-star"></i>
                                    }
                                    @for (int i = testimonial.Rating; i < 5; i++)
                                    {
                                        <i class="far fa-star"></i>
                                    }
                                </div>
                                <div class="testimonial-content">
                                    <i class="fas fa-quote-left testimonial-quote-icon"></i>
                                    <p class="testimonial-text">@testimonial.Content</p>
                                </div>
                                <div class="testimonial-author">
                                    <img src="@(string.IsNullOrEmpty(testimonial.ClientPhotoUrl) ? "/images/avatar-placeholder.jpg" : testimonial.ClientPhotoUrl)"
                                         alt="@testimonial.ClientName"
                                         class="testimonial-avatar"
                                         loading="lazy" />
                                    <div class="testimonial-info">
                                        <h5 class="author-name">@testimonial.ClientName</h5>
                                        <p class="author-title">@testimonial.ClientTitle</p>
                                        <p class="author-company">@testimonial.ClientCompany</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    testimonialIndex++;
                }
            }
            else
            {
                <div class="modern-card empty-state-card animate-on-scroll">
                    <div class="card-body text-center">
                        <div class="empty-state-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <h3 class="card-title">No Testimonials Available</h3>
                        <p class="card-subtitle mb-4">We're collecting amazing feedback. Check back soon!</p>
                        <a href="/" class="modern-btn primary">
                            <span class="btn-text">Back to Home</span>
                            <span class="btn-icon">
                                <i class="fas fa-home"></i>
                            </span>
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Enhanced CTA Section -->
<section class="modern-section" id="contact">
    <div class="container">
        <div class="modern-card cta-card-enhanced animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0 text-center text-lg-start">
                        <h2 class="section-title mb-3">
                            <span class="title-highlight">Ready to Transform</span> Your Business?
                        </h2>
                        <p class="section-subtitle mb-0">
                            Let's discuss your project and create something amazing together.
                            Get in touch with our team of experts today.
                        </p>
                    </div>
                    <div class="col-lg-4 text-center text-lg-end">
                        <div class="cta-actions">
                            <a asp-controller="Home" asp-action="Contact" class="modern-btn primary large">
                                <span class="btn-text">Get in Touch</span>
                                <span class="btn-icon">
                                    <i class="fas fa-phone"></i>
                                </span>
                            </a>
                            <a href="#services" class="modern-btn secondary large">
                                <span class="btn-text">Explore Services</span>
                                <span class="btn-icon">
                                    <i class="fas fa-arrow-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script src="~/js/hero-slideshow.js" asp-append-version="true"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Hero particles animation
            function createParticles() {
                const particlesContainer = document.querySelector('.hero-particles');
                if (!particlesContainer) return;

                for (let i = 0; i < 50; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 20 + 's';
                    particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                    particlesContainer.appendChild(particle);
                }
            }

            createParticles();

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Hero stats counter animation
            function animateCounters() {
                const counters = document.querySelectorAll('.stat-number');

                counters.forEach(counter => {
                    const target = parseInt(counter.textContent.replace(/\D/g, ''));
                    const suffix = counter.textContent.replace(/\d/g, '');
                    let current = 0;
                    const increment = target / 50;

                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            counter.textContent = target + suffix;
                            clearInterval(timer);
                        } else {
                            counter.textContent = Math.floor(current) + suffix;
                        }
                    }, 50);
                });
            }

            // Enhanced Service card hover effects with advanced micro-interactions
            const serviceCards = document.querySelectorAll('.service-card-enhanced');

            serviceCards.forEach(card => {
                // Add floating elements for enhanced interaction
                const floatingElements = document.createElement('div');
                floatingElements.className = 'floating-elements';
                for (let i = 0; i < 3; i++) {
                    const dot = document.createElement('div');
                    dot.className = 'floating-dot';
                    floatingElements.appendChild(dot);
                }
                card.appendChild(floatingElements);

                // Enhanced mouse enter effects
                card.addEventListener('mouseenter', function() {
                    // Add performance optimization classes
                    this.classList.add('will-change-transform');
                    const iconDisplay = this.querySelector('.service-icon-display');
                    const cardTitle = this.querySelector('.card-title');
                    const overlay = this.querySelector('.service-overlay');

                    if (iconDisplay) {
                        iconDisplay.classList.add('will-change-transform');
                    }
                    if (cardTitle) {
                        cardTitle.classList.add('will-change-transform');
                    }
                    if (overlay) {
                        overlay.style.opacity = '1';
                    }
                });

                // Enhanced mouse leave effects
                card.addEventListener('mouseleave', function() {
                    // Remove performance optimization classes
                    this.classList.remove('will-change-transform');
                    const iconDisplay = this.querySelector('.service-icon-display');
                    const cardTitle = this.querySelector('.card-title');
                    const overlay = this.querySelector('.service-overlay');

                    if (iconDisplay) {
                        iconDisplay.classList.remove('will-change-transform');
                    }
                    if (cardTitle) {
                        cardTitle.classList.remove('will-change-transform');
                    }
                    if (overlay) {
                        overlay.style.opacity = '0';
                    }
                });

                // Advanced ripple effect on click
                card.addEventListener('click', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const ripple = document.createElement('div');
                    ripple.className = 'ripple';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';

                    this.appendChild(ripple);

                    // Remove ripple after animation
                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }, 800);
                });

                // Touch device support
                if ('ontouchstart' in window) {
                    card.addEventListener('touchstart', function() {
                        this.classList.add('touch-active');
                    });

                    card.addEventListener('touchend', function() {
                        setTimeout(() => {
                            this.classList.remove('touch-active');
                        }, 150);
                    });
                }

                // Intersection Observer for performance optimization
                const cardObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('in-viewport');
                        } else {
                            entry.target.classList.remove('in-viewport');
                            // Remove performance classes when out of viewport
                            entry.target.classList.remove('will-change-transform');
                        }
                    });
                }, { threshold: 0.1 });

                cardObserver.observe(card);

                // Add subtle random animation delays for organic feel
                const randomDelay = Math.random() * 0.3;
                card.style.animationDelay = randomDelay + 's';
            });

            // Project card hover effects
            const projectCards = document.querySelectorAll('.project-card-enhanced');

            projectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    const image = this.querySelector('.project-featured-image');
                    const overlay = this.querySelector('.project-overlay');
                    if (image) {
                        image.style.transform = 'scale(1.05)';
                    }
                    if (overlay) {
                        overlay.style.opacity = '1';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const image = this.querySelector('.project-featured-image');
                    const overlay = this.querySelector('.project-overlay');
                    if (image) {
                        image.style.transform = 'scale(1)';
                    }
                    if (overlay) {
                        overlay.style.opacity = '0';
                    }
                });
            });

            // Technology carousel enhanced animation
            const techTrack = document.querySelector('.tech-track-enhanced');
            if (techTrack) {
                let scrollAmount = 0;
                const scrollSpeed = 1;

                function scrollTech() {
                    scrollAmount += scrollSpeed;
                    if (scrollAmount >= techTrack.scrollWidth / 2) {
                        scrollAmount = 0;
                    }
                    techTrack.style.transform = `translateX(-${scrollAmount}px)`;
                    requestAnimationFrame(scrollTech);
                }

                scrollTech();
            }

            // Testimonial card hover effects
            const testimonialCards = document.querySelectorAll('.testimonial-card-enhanced');

            testimonialCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    const avatar = this.querySelector('.testimonial-avatar');
                    if (avatar) {
                        avatar.style.transform = 'scale(1.1)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const avatar = this.querySelector('.testimonial-avatar');
                    if (avatar) {
                        avatar.style.transform = 'scale(1)';
                    }
                });
            });

            // Scroll indicator animation
            const scrollIndicator = document.querySelector('.hero-scroll-indicator');
            if (scrollIndicator) {
                scrollIndicator.addEventListener('click', function() {
                    document.querySelector('#services').scrollIntoView({
                        behavior: 'smooth'
                    });
                });

                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 100) {
                        scrollIndicator.style.opacity = '0';
                    } else {
                        scrollIndicator.style.opacity = '1';
                    }
                });
            }

            // Trigger counter animation when hero is visible
            const heroObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(animateCounters, 1000);
                        heroObserver.unobserve(entry.target);
                    }
                });
            });

            const heroSection = document.querySelector('.hero-enhanced');
            if (heroSection) {
                heroObserver.observe(heroSection);
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });



        // Scroll to section function
        function scrollToSection(selector) {
            const element = document.querySelector(selector);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Make scrollToSection globally available
        window.scrollToSection = scrollToSection;
    </script>
}
