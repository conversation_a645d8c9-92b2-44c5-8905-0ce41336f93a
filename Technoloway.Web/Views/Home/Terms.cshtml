@model Technoloway.Core.Entities.LegalPage

@{
    ViewData["Title"] = Model?.Title ?? "Terms of Service";
    ViewData["Description"] = Model?.MetaDescription ?? "Terms of Service for Technoloway - Professional web development and technology solutions.";
}

<!-- Modern Hero Section -->
<section class="legal-hero-section">
    <div class="hero-background">
        <div class="hero-pattern"></div>
        <div class="hero-gradient"></div>
    </div>

    <div class="container">
        <div class="row align-items-center min-vh-60">
            <div class="col-lg-10 mx-auto">
                <div class="hero-content text-center">
                    <!-- Breadcrumb Navigation -->
                    <nav aria-label="breadcrumb" class="hero-breadcrumb mb-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item">
                                <a href="/" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Home</span>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="breadcrumb-separator">•</span>
                            </li>
                            <li class="breadcrumb-item active">
                                <span class="breadcrumb-current">Terms of Service</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Hero Icon -->
                    <div class="hero-icon-wrapper mb-4">
                        <div class="hero-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="hero-icon-bg"></div>
                    </div>

                    <!-- Hero Title -->
                    <h1 class="hero-title mb-4">
                        @if (Model != null)
                        {
                            @Model.Title
                        }
                        else
                        {
                            <span class="title-main">Terms of</span>
                            <span class="title-highlight">Service</span>
                        }
                    </h1>

                    <!-- Hero Subtitle -->
                    <p class="hero-subtitle mb-5">
                        @if (Model != null && !string.IsNullOrEmpty(Model.MetaDescription))
                        {
                            @Model.MetaDescription
                        }
                        else
                        {
                            <text>Clear, transparent terms that govern our professional relationship.<br>
                            Please review these carefully before using our services.</text>
                        }
                    </p>

                    <!-- Hero Stats -->
                    <div class="hero-stats">
                        <div class="row justify-content-center">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-shield-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>Transparent</h4>
                                        <p>Clear & Fair Terms</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-balance-scale"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>Balanced</h4>
                                        <p>Mutual Protection</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-handshake"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h4>Partnership</h4>
                                        <p>Collaborative Approach</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator" onclick="scrollToContent()">
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
</section>

<!-- Terms Content Section -->
<section class="modern-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card">
                    <div class="card-body p-5">
                        <div class="terms-content">
                            <div class="mb-5">
                                <p class="lead text-muted">
                                    <strong>Last updated:</strong> @(Model?.LastModified.ToString("MMMM dd, yyyy") ?? DateTime.Now.ToString("MMMM dd, yyyy"))
                                </p>
                                @if (Model != null)
                                {
                                    @Html.Raw(Model.Content)
                                }
                                else
                                {
                                    <p>
                                        These Terms of Service ("Terms") govern your use of Technoloway's website and services.
                                        By accessing or using our services, you agree to be bound by these Terms.
                                    </p>
                                }
                            </div>

                            @if (Model != null && Model.Sections.Any())
                            {
                                @foreach (var section in Model.Sections.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                                {
                                    <div class="terms-section mb-5">
                                        <h3 class="section-title">
                                            @if (!string.IsNullOrEmpty(section.IconClass))
                                            {
                                                <i class="@(section.IconClass) text-primary me-2"></i>
                                            }
                                            @(section.Title)
                                        </h3>
                                        @Html.Raw(section.Content)
                                    </div>
                                }
                            }
                            else
                            {
                                <!-- Fallback static content -->
                                <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-handshake text-primary me-2"></i>
                                    1. Acceptance of Terms
                                </h3>
                                <p>
                                    By accessing and using Technoloway's services, you accept and agree to be bound by the terms
                                    and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                                </p>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-cogs text-primary me-2"></i>
                                    2. Services Description
                                </h3>
                                <p>
                                    Technoloway provides web development, software development, and technology consulting services.
                                    Our services include but are not limited to:
                                </p>
                                <ul class="custom-list">
                                    <li>Custom web application development</li>
                                    <li>Mobile application development</li>
                                    <li>Software consulting and architecture</li>
                                    <li>Technology solutions and integration</li>
                                    <li>Maintenance and support services</li>
                                </ul>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-user-shield text-primary me-2"></i>
                                    3. User Responsibilities
                                </h3>
                                <p>As a user of our services, you agree to:</p>
                                <ul class="custom-list">
                                    <li>Provide accurate and complete information</li>
                                    <li>Maintain the security of your account credentials</li>
                                    <li>Use our services in compliance with applicable laws</li>
                                    <li>Respect intellectual property rights</li>
                                    <li>Not engage in any harmful or malicious activities</li>
                                </ul>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-copyright text-primary me-2"></i>
                                    4. Intellectual Property
                                </h3>
                                <p>
                                    All content, features, and functionality of our services are owned by Technoloway and are
                                    protected by international copyright, trademark, and other intellectual property laws.
                                </p>
                                <p>
                                    Custom work developed for clients remains the property of the client upon full payment,
                                    unless otherwise specified in the project agreement.
                                </p>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    5. Payment Terms
                                </h3>
                                <p>
                                    Payment terms are specified in individual project agreements. Generally:
                                </p>
                                <ul class="custom-list">
                                    <li>Invoices are due within 30 days of issuance</li>
                                    <li>Late payments may incur additional fees</li>
                                    <li>Services may be suspended for overdue accounts</li>
                                    <li>All prices are in USD unless otherwise specified</li>
                                </ul>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-shield-alt text-primary me-2"></i>
                                    6. Limitation of Liability
                                </h3>
                                <p>
                                    Technoloway shall not be liable for any indirect, incidental, special, consequential,
                                    or punitive damages resulting from your use of our services. Our total liability
                                    shall not exceed the amount paid for the specific service in question.
                                </p>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-edit text-primary me-2"></i>
                                    7. Modifications
                                </h3>
                                <p>
                                    We reserve the right to modify these Terms at any time. Changes will be effective
                                    immediately upon posting. Your continued use of our services constitutes acceptance
                                    of the modified Terms.
                                </p>
                            </div>

                            <div class="terms-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-gavel text-primary me-2"></i>
                                    8. Governing Law
                                </h3>
                                <p>
                                    These Terms shall be governed by and construed in accordance with the laws of
                                    [Your Jurisdiction], without regard to its conflict of law provisions.
                                </p>
                            </div>

                            <div class="terms-section">
                                <h3 class="section-title">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    9. Contact Information
                                </h3>
                                <p>
                                    If you have any questions about these Terms of Service, please contact us:
                                </p>
                                <div class="contact-info">
                                    <p><strong>Email:</strong> <EMAIL></p>
                                    <p><strong>Phone:</strong> +****************</p>
                                    <p><strong>Address:</strong> 123 Tech Street, Innovation City, TC 12345</p>
                                </div>
                            </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="modern-section bg-light py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Ready to Get Started?</h3>
                <p class="text-muted mb-4">
                    Now that you've reviewed our terms, let's discuss your project requirements.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/contact" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>
                        Contact Us
                    </a>
                    <a href="/services" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-cogs me-2"></i>
                        View Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Legal Hero Section */
    .legal-hero-section {
        position: relative;
        min-height: 70vh;
        display: flex;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .hero-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
        background-size: 60px 60px;
        animation: patternMove 20s linear infinite;
    }

    .hero-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .min-vh-60 {
        min-height: 60vh;
    }

    /* Breadcrumb Styling */
    .hero-breadcrumb .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-link {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .breadcrumb-link:hover {
        color: white;
        transform: translateY(-1px);
    }

    .breadcrumb-separator {
        color: rgba(255, 255, 255, 0.6);
        margin: 0 1rem;
    }

    .breadcrumb-current {
        color: #ffd700;
        font-weight: 600;
    }

    /* Hero Icon */
    .hero-icon-wrapper {
        position: relative;
        display: inline-block;
    }

    .hero-icon {
        position: relative;
        z-index: 2;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #ffd700;
        backdrop-filter: blur(10px);
        animation: iconFloat 3s ease-in-out infinite;
    }

    .hero-icon-bg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        animation: iconPulse 2s ease-in-out infinite;
    }

    /* Hero Title */
    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }

    .title-main {
        color: white;
        display: block;
    }

    .title-highlight {
        color: #ffd700;
        display: block;
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
        max-width: 600px;
        margin: 0 auto;
    }

    /* Hero Stats */
    .hero-stats {
        margin-top: 3rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .stat-item:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 215, 0, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: #ffd700;
        flex-shrink: 0;
    }

    .stat-content h4 {
        color: white;
        font-weight: 700;
        margin-bottom: 0.25rem;
        font-size: 1.1rem;
    }

    .stat-content p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.9rem;
    }

    /* Scroll Indicator */
    .scroll-indicator {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 10;
        cursor: pointer;
        animation: bounce 2s infinite;
    }

    .scroll-indicator:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(-50%) translateY(-5px) scale(1.1);
        box-shadow: 0 12px 40px rgba(255, 215, 0, 0.3);
        border-color: rgba(255, 215, 0, 0.5);
    }

    .scroll-indicator:active {
        transform: translateX(-50%) translateY(-2px) scale(1.05);
        background: rgba(255, 255, 255, 0.3);
    }

    .scroll-indicator i {
        font-size: 1.8rem;
        color: #ffd700;
        text-shadow: 0 2px 8px rgba(255, 215, 0, 0.5);
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
        transition: all 0.3s ease;
    }

    .scroll-indicator:hover i {
        color: #ffed4e;
        transform: translateY(2px);
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.5));
    }

    /* Animations */
    @@keyframes patternMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(60px, 60px); }
    }

    @@keyframes iconFloat {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    @@keyframes iconPulse {
        0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
        50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.4; }
    }

    @@keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    /* Content Sections */
    .terms-content {
        line-height: 1.7;
    }

    .terms-section {
        border-left: 4px solid var(--primary-color);
        padding-left: 2rem;
        margin-left: 1rem;
    }

    .section-title {
        color: var(--text-dark);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .custom-list {
        padding-left: 1.5rem;
    }

    .custom-list li {
        margin-bottom: 0.5rem;
        color: var(--text-muted);
    }

    .contact-info p {
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .stat-item {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .hero-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .hero-icon-bg {
            width: 90px;
            height: 90px;
        }
    }
</style>

<script>
    function scrollToContent() {
        const contentSection = document.querySelector('.modern-section.py-5');
        if (contentSection) {
            contentSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
</script>
