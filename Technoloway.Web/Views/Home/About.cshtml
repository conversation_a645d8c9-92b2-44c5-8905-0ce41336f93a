@model Technoloway.Core.Entities.AboutPage

@{
    ViewData["Title"] = Model?.Title ?? "About Us";
    ViewData["MetaDescription"] = Model?.MetaDescription ?? "Learn about Technoloway, our mission, values, and the team behind our innovative software solutions.";
    ViewData["MetaKeywords"] = Model?.MetaKeywords ?? "about us, software company, development team, tech experts, software engineers";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />

    <style>
        /* Hero Image Display */
        .hero-image-display {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .hero-image-display:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .hero-image-display img {
            transition: transform 0.3s ease;
        }

        .hero-image-display:hover img {
            transform: scale(1.05);
        }

        /* Additional styles for dynamic sections */
        .content-section-icon,
        .feature-section-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            transition: all 0.3s ease;
        }

        .content-section-card:hover .content-section-icon,
        .feature-section-card:hover .feature-section-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .content-section-image img,
        .feature-section-image img {
            max-height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
            width: 100%;
        }

        .content-section-card:hover .content-section-image img,
        .feature-section-card:hover .feature-section-image img {
            transform: scale(1.05);
        }

        .content-section-card,
        .feature-section-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .content-section-card:hover,
        .feature-section-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .content-section-card .card-subtitle,
        .feature-section-card .card-subtitle {
            color: #6c757d;
            line-height: 1.6;
        }

        .content-section-card .card-subtitle p,
        .feature-section-card .card-subtitle p {
            margin-bottom: 0.5rem;
        }

        .content-section-card .card-subtitle p:last-child,
        .feature-section-card .card-subtitle p:last-child {
            margin-bottom: 0;
        }

        /* Enhanced image display for sections */
        .content-section-image,
        .feature-section-image {
            overflow: hidden;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
<partial name="_HeroSection" />

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header" @if (Model != null && !string.IsNullOrEmpty(Model.HeroImageUrl)) { <text>style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('@Model.HeroImageUrl'); background-size: cover; background-position: center; background-attachment: fixed;"</text> }>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">About Us</span>
                        </div>
                        <h1 class="page-title">
                            @if (Model != null)
                            {
                                @Html.Raw(Model.HeroTitle)
                            }
                            else
                            {
                                @Html.Raw("<span class=\"title-highlight\">About</span> Technoloway")
                            }
                        </h1>
                        <p class="page-subtitle">
                            @if (Model != null)
                            {
                                @Model.HeroSubtitle
                            }
                            else
                            {
                                @:Learn about our mission, values, and the passionate team behind our innovative software solutions that help businesses thrive in the digital world.
                            }
                        </p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="page-header-visual">
                        <div class="floating-element about-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="floating-element about-icon-2">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="floating-element about-icon-3">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- About Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="about-image-wrapper animate-on-scroll">
                    <div class="about-image-container">
                        <img src="@(Model?.StoryImageUrl ?? "~/images/about-us.jpg")" alt="About Technoloway" class="about-featured-image" />
                        <div class="about-image-overlay"></div>
                        <div class="about-stats">
                            @if (Model != null)
                            {
                                @if (!string.IsNullOrEmpty(Model.Stat1Number))
                                {
                                    <div class="stat-item">
                                        <div class="stat-number">@Model.Stat1Number</div>
                                        <div class="stat-label">@Model.Stat1Label</div>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(Model.Stat2Number))
                                {
                                    <div class="stat-item">
                                        <div class="stat-number">@Model.Stat2Number</div>
                                        <div class="stat-label">@Model.Stat2Label</div>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(Model.Stat3Number))
                                {
                                    <div class="stat-item">
                                        <div class="stat-number">@Model.Stat3Number</div>
                                        <div class="stat-label">@Model.Stat3Label</div>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(Model.Stat4Number))
                                {
                                    <div class="stat-item">
                                        <div class="stat-number">@Model.Stat4Number</div>
                                        <div class="stat-label">@Model.Stat4Label</div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="stat-item">
                                    <div class="stat-number">8+</div>
                                    <div class="stat-label">Years Experience</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">100+</div>
                                    <div class="stat-label">Projects Completed</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">50+</div>
                                    <div class="stat-label">Happy Clients</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">24/7</div>
                                    <div class="stat-label">Support Available</div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content animate-on-scroll" data-delay="200">
                    <h2 class="section-title text-start mb-4">
                        @if (Model != null)
                        {
                            @Html.Raw(Model.StoryTitle)
                        }
                        else
                        {
                            @Html.Raw("<span class=\"title-highlight\">Our</span> Story")
                        }
                    </h2>
                    <p class="section-subtitle text-start mb-4">
                        @if (Model != null)
                        {
                            @Model.StorySubtitle
                        }
                        else
                        {
                            @:Technoloway was founded in 2015 with a mission to deliver innovative software solutions that help businesses thrive in the digital age.
                        }
                    </p>
                    <div class="about-description mb-5">
                        @if (Model != null)
                        {
                            @Html.Raw(Model.StoryContent)
                        }
                        else
                        {
                            <p class="mb-4">
                                We started as a small team of passionate developers and have grown into a full-service software development company with expertise in web development, mobile applications, cloud solutions, and more.
                            </p>
                            <p class="mb-4">
                                Our commitment to quality, innovation, and client satisfaction has made us a trusted partner for businesses of all sizes, from startups to large enterprises.
                            </p>
                        }
                    </div>
                    <div class="about-actions">
                        <a href="#team" class="modern-btn primary">
                            <span class="btn-text">Meet Our Team</span>
                            <span class="btn-icon">
                                <i class="fas fa-users"></i>
                            </span>
                        </a>
                        <a asp-controller="Home" asp-action="Contact" class="modern-btn secondary">
                            <span class="btn-text">Work With Us</span>
                            <span class="btn-icon">
                                <i class="fas fa-handshake"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mission & Values Section - Modern Design -->
<section class="modern-section mission-values-section">
    <div class="container">
        <!-- Mission & Vision -->
        <div class="row mb-5">
            @if (Model != null && !string.IsNullOrEmpty(Model.MissionTitle))
            {
                <div class="col-md-6 mb-4">
                    <div class="modern-card mission-vision-card animate-on-scroll" data-delay="100">
                        <div class="card-body text-center">
                            <div class="mission-vision-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h3 class="card-title">
                                @if (Model.MissionTitle.Contains("Our"))
                                {
                                    @Html.Raw(Model.MissionTitle.Replace("Our", "<span class=\"title-highlight\">Our</span>"))
                                }
                                else
                                {
                                    @Model.MissionTitle
                                }
                            </h3>
                            <div class="card-subtitle">
                                @Html.Raw(Model.MissionContent)
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="col-md-6 mb-4">
                    <div class="modern-card mission-vision-card animate-on-scroll" data-delay="100">
                        <div class="card-body text-center">
                            <div class="mission-vision-icon">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h3 class="card-title"><span class="title-highlight">Our</span> Mission</h3>
                            <p class="card-subtitle">
                                To empower businesses with innovative software solutions that drive growth, efficiency, and competitive advantage in the digital marketplace.
                            </p>
                        </div>
                    </div>
                </div>
            }

            @if (Model != null && !string.IsNullOrEmpty(Model.VisionTitle))
            {
                <div class="col-md-6 mb-4">
                    <div class="modern-card mission-vision-card animate-on-scroll" data-delay="200">
                        <div class="card-body text-center">
                            <div class="mission-vision-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <h3 class="card-title">
                                @if (Model.VisionTitle.Contains("Our"))
                                {
                                    @Html.Raw(Model.VisionTitle.Replace("Our", "<span class=\"title-highlight\">Our</span>"))
                                }
                                else
                                {
                                    @Model.VisionTitle
                                }
                            </h3>
                            <div class="card-subtitle">
                                @Html.Raw(Model.VisionContent)
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="col-md-6 mb-4">
                    <div class="modern-card mission-vision-card animate-on-scroll" data-delay="200">
                        <div class="card-body text-center">
                            <div class="mission-vision-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <h3 class="card-title"><span class="title-highlight">Our</span> Vision</h3>
                            <p class="card-subtitle">
                                To be the leading software development partner known for delivering exceptional quality, innovation, and value to our clients worldwide.
                            </p>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Values Section -->
        @if (Model != null && !string.IsNullOrEmpty(Model.ValuesTitle))
        {
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8 text-center">
                    <div class="section-header animate-on-scroll">
                        <h2 class="section-title">
                            @if (Model.ValuesTitle.Contains("Our"))
                            {
                                @Html.Raw(Model.ValuesTitle.Replace("Our", "<span class=\"title-highlight\">Our</span>"))
                            }
                            else
                            {
                                @Html.Raw(Model.ValuesTitle)
                            }
                        </h2>
                        <div class="section-subtitle">
                            @Html.Raw(Model.ValuesContent)
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8 text-center">
                    <div class="section-header animate-on-scroll">
                        <h2 class="section-title">
                            <span class="title-highlight">Our</span> Values
                        </h2>
                        <p class="section-subtitle">
                            The core principles that guide everything we do and shape our company culture.
                        </p>
                    </div>
                </div>
            </div>
        }

        <!-- Dynamic Sections from Database -->
        @if (Model?.Sections?.Any() == true)
        {
            <div class="row g-4">
                @{
                    var valueSections = Model.Sections.Where(s => s.SectionType == "value" && s.IsActive).OrderBy(s => s.DisplayOrder).ToList();
                    int valueIndex = 0;
                }
                @foreach (var valueSection in valueSections)
                {
                    <div class="col-md-4">
                        <div class="modern-card values-card animate-on-scroll" data-delay="@((valueIndex % 3 + 1) * 100)">
                            <div class="card-body text-center">
                                <div class="values-icon">
                                    @if (!string.IsNullOrEmpty(valueSection.IconClass))
                                    {
                                        <i class="@valueSection.IconClass"></i>
                                    }
                                    else
                                    {
                                        <i class="fas fa-star"></i>
                                    }
                                </div>
                                <h4 class="card-title">@valueSection.Title</h4>
                                <div class="card-subtitle">
                                    @Html.Raw(valueSection.Content)
                                </div>
                            </div>
                        </div>
                    </div>
                    valueIndex++;
                }
            </div>
        }
        else
        {
            <!-- Fallback static values -->
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="modern-card values-card animate-on-scroll" data-delay="100">
                        <div class="card-body text-center">
                            <div class="values-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h4 class="card-title">Innovation</h4>
                            <p class="card-subtitle">
                                We embrace new technologies and creative approaches to solve complex problems and stay ahead of industry trends.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="modern-card values-card animate-on-scroll" data-delay="200">
                        <div class="card-body text-center">
                            <div class="values-icon">
                                <i class="fas fa-gem"></i>
                            </div>
                            <h4 class="card-title">Quality</h4>
                            <p class="card-subtitle">
                                We are committed to delivering high-quality solutions that exceed expectations and stand the test of time.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="modern-card values-card animate-on-scroll" data-delay="300">
                        <div class="card-body text-center">
                            <div class="values-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <h4 class="card-title">Integrity</h4>
                            <p class="card-subtitle">
                                We operate with honesty, transparency, and ethical business practices in all our client relationships.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</section>

<!-- Additional Content Sections from Database -->
@if (Model?.Sections?.Any() == true)
{
    var contentSections = Model.Sections.Where(s => s.SectionType == "content" && s.IsActive).OrderBy(s => s.DisplayOrder).ToList();
    var featureSections = Model.Sections.Where(s => s.SectionType == "feature" && s.IsActive).OrderBy(s => s.DisplayOrder).ToList();

    @if (contentSections.Any())
    {
        <section class="modern-section">
            <div class="container">
                <div class="row g-4">
                    @{
                        int contentIndex = 0;
                    }
                    @foreach (var contentSection in contentSections)
                    {
                        <div class="col-md-6 col-lg-4">
                            <div class="modern-card content-section-card animate-on-scroll" data-delay="@((contentIndex % 3 + 1) * 100)">
                                <div class="card-body text-center">
                                    @if (!string.IsNullOrEmpty(contentSection.IconClass))
                                    {
                                        <div class="content-section-icon">
                                            <i class="@contentSection.IconClass"></i>
                                        </div>
                                    }
                                    @if (!string.IsNullOrEmpty(contentSection.ImageUrl))
                                    {
                                        <div class="content-section-image mb-3">
                                            <img src="@contentSection.ImageUrl" alt="@contentSection.Title" class="img-fluid rounded" />
                                        </div>
                                    }
                                    <h4 class="card-title">@contentSection.Title</h4>
                                    <div class="card-subtitle">
                                        @Html.Raw(contentSection.Content)
                                    </div>
                                </div>
                            </div>
                        </div>
                        contentIndex++;
                    }
                </div>
            </div>
        </section>
    }

    @if (featureSections.Any())
    {
        <section class="modern-section">
            <div class="container">
                <div class="row justify-content-center mb-5">
                    <div class="col-lg-8 text-center">
                        <div class="section-header animate-on-scroll">
                            <h2 class="section-title">
                                <span class="title-highlight">Our</span> Features
                            </h2>
                            <p class="section-subtitle">
                                Discover what makes us different and why clients choose us for their software development needs.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row g-4">
                    @{
                        int featureIndex = 0;
                    }
                    @foreach (var featureSection in featureSections)
                    {
                        <div class="col-md-6 col-lg-4">
                            <div class="modern-card feature-section-card animate-on-scroll" data-delay="@((featureIndex % 3 + 1) * 100)">
                                <div class="card-body text-center">
                                    @if (!string.IsNullOrEmpty(featureSection.IconClass))
                                    {
                                        <div class="feature-section-icon">
                                            <i class="@featureSection.IconClass"></i>
                                        </div>
                                    }
                                    @if (!string.IsNullOrEmpty(featureSection.ImageUrl))
                                    {
                                        <div class="feature-section-image mb-3">
                                            <img src="@featureSection.ImageUrl" alt="@featureSection.Title" class="img-fluid rounded" />
                                        </div>
                                    }
                                    <h4 class="card-title">@featureSection.Title</h4>
                                    <div class="card-subtitle">
                                        @Html.Raw(featureSection.Content)
                                    </div>
                                </div>
                            </div>
                        </div>
                        featureIndex++;
                    }
                </div>
            </div>
        </section>
    }
}

<!-- Team Section - Modern Design -->
<section id="team" class="modern-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Meet Our</span> Team
                    </h2>
                    <p class="section-subtitle">
                        The talented professionals behind our success. We're passionate about technology and committed to delivering exceptional results.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @if (ViewBag.TeamMembers != null && ViewBag.TeamMembers.Count > 0)
            {
                int teamIndex = 0;
                foreach (var member in ViewBag.TeamMembers)
                {
                    <div class="col-md-6 col-lg-3">
                        <div class="modern-card team-member-card animate-on-scroll" data-delay="@((teamIndex % 4 + 1) * 100)">
                            <div class="card-body text-center">
                                <div class="team-avatar-wrapper">
                                    <img src="@(string.IsNullOrEmpty(member.PhotoUrl) ? "/images/team/avatar-placeholder.jpg" : member.PhotoUrl)"
                                         alt="@member.Name"
                                         class="team-avatar" />
                                    <div class="team-status-badge">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">@member.Name</h5>
                                <p class="card-subtitle mb-4">@member.Position</p>
                                <div class="team-social-links">
                                    @if (!string.IsNullOrEmpty(member.LinkedInUrl))
                                    {
                                        <a href="@member.LinkedInUrl" target="_blank" class="social-link linkedin">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                    }
                                    @if (!string.IsNullOrEmpty(member.TwitterUrl))
                                    {
                                        <a href="@member.TwitterUrl" target="_blank" class="social-link twitter">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    }
                                    @if (!string.IsNullOrEmpty(member.GithubUrl))
                                    {
                                        <a href="@member.GithubUrl" target="_blank" class="social-link github">
                                            <i class="fab fa-github"></i>
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    teamIndex++;
                }
            }
            else
            {
                <div class="modern-card empty-state-card animate-on-scroll">
                    <div class="card-body text-center">
                        <div class="empty-state-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="card-title">No Team Members Available</h3>
                        <p class="card-subtitle mb-4">We're building an amazing team. Check back soon!</p>
                        <a href="/" class="modern-btn primary">
                            <span class="btn-text">Back to Home</span>
                            <span class="btn-icon">
                                <i class="fas fa-home"></i>
                            </span>
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- CTA Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="modern-card cta-card animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0 text-center text-lg-start">
                        <h2 class="section-title mb-3">
                            @if (Model != null && !string.IsNullOrEmpty(Model.CtaTitle))
                            {
                                @Html.Raw(Model.CtaTitle)
                            }
                            else
                            {
                                @Html.Raw("<span class=\"title-highlight\">Ready to Work</span> With Us?")
                            }
                        </h2>
                        <p class="section-subtitle mb-0">
                            @if (Model != null && !string.IsNullOrEmpty(Model.CtaSubtitle))
                            {
                                @Model.CtaSubtitle
                            }
                            else
                            {
                                @:Let's discuss how we can help your business succeed with innovative software solutions.
                                @:Our team is ready to turn your ideas into reality.
                            }
                        </p>
                    </div>
                    <div class="col-lg-4 text-center text-lg-end">
                        <div class="d-flex gap-3 justify-content-center justify-content-lg-end flex-wrap">
                            @if (Model != null && !string.IsNullOrEmpty(Model.CtaPrimaryButtonText))
                            {
                                <a href="@(Model.CtaPrimaryButtonUrl ?? "/contact")" class="modern-btn primary">
                                    <span class="btn-text">@Model.CtaPrimaryButtonText</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                </a>
                            }
                            else
                            {
                                <a asp-controller="Home" asp-action="Contact" class="modern-btn primary">
                                    <span class="btn-text">Contact Us Today</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                </a>
                            }

                            @if (Model != null && !string.IsNullOrEmpty(Model.CtaSecondaryButtonText))
                            {
                                <a href="@(Model.CtaSecondaryButtonUrl ?? "/services")" class="modern-btn secondary">
                                    <span class="btn-text">@Model.CtaSecondaryButtonText</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-cogs"></i>
                                    </span>
                                </a>
                            }
                            else
                            {
                                <a asp-controller="Services" asp-action="Index" class="modern-btn secondary">
                                    <span class="btn-text">View Our Services</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-cogs"></i>
                                    </span>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // About image hover effects
            const aboutImageWrapper = document.querySelector('.about-image-wrapper');
            if (aboutImageWrapper) {
                aboutImageWrapper.addEventListener('mouseenter', function() {
                    const image = this.querySelector('.about-featured-image');
                    const stats = this.querySelector('.about-stats');
                    if (image) {
                        image.style.transform = 'scale(1.05)';
                    }
                    if (stats) {
                        stats.style.opacity = '1';
                        stats.style.transform = 'translateY(0)';
                    }
                });

                aboutImageWrapper.addEventListener('mouseleave', function() {
                    const image = this.querySelector('.about-featured-image');
                    const stats = this.querySelector('.about-stats');
                    if (image) {
                        image.style.transform = 'scale(1)';
                    }
                    if (stats) {
                        stats.style.opacity = '0.9';
                        stats.style.transform = 'translateY(10px)';
                    }
                });
            }

            // Mission/Vision and Values card hover effects
            const missionVisionCards = document.querySelectorAll('.mission-vision-card, .values-card');

            missionVisionCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    const icon = this.querySelector('.mission-vision-icon, .values-icon');
                    if (icon) {
                        icon.style.transform = 'scale(1.1) rotate(5deg)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const icon = this.querySelector('.mission-vision-icon, .values-icon');
                    if (icon) {
                        icon.style.transform = 'scale(1) rotate(0deg)';
                    }
                });
            });

            // Team member card hover effects
            const teamCards = document.querySelectorAll('.team-member-card');

            teamCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    const avatar = this.querySelector('.team-avatar');
                    const socialLinks = this.querySelector('.team-social-links');
                    if (avatar) {
                        avatar.style.transform = 'scale(1.05)';
                    }
                    if (socialLinks) {
                        socialLinks.style.opacity = '1';
                        socialLinks.style.transform = 'translateY(0)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const avatar = this.querySelector('.team-avatar');
                    const socialLinks = this.querySelector('.team-social-links');
                    if (avatar) {
                        avatar.style.transform = 'scale(1)';
                    }
                    if (socialLinks) {
                        socialLinks.style.opacity = '0.8';
                        socialLinks.style.transform = 'translateY(5px)';
                    }
                });
            });

            // Social link hover effects
            const socialLinks = document.querySelectorAll('.social-link');

            socialLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1) rotate(5deg)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}
