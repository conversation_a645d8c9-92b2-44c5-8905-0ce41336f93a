﻿@model Technoloway.Core.Entities.LegalPage

@{
    ViewData["Title"] = Model?.Title ?? "Privacy Policy";
    ViewData["Description"] = Model?.MetaDescription ?? "Privacy Policy for Technoloway - How we collect, use, and protect your personal information.";
}

<!-- Modern Privacy Hero Section -->
<section class="privacy-hero-section">
    <div class="hero-background">
        <div class="privacy-pattern"></div>
        <div class="privacy-gradient"></div>
    </div>

    <div class="container">
        <div class="row align-items-center min-vh-60">
            <div class="col-lg-10 mx-auto">
                <div class="hero-content text-center">
                    <!-- Breadcrumb Navigation -->
                    <nav aria-label="breadcrumb" class="hero-breadcrumb mb-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item">
                                <a href="/" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Home</span>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="breadcrumb-separator">•</span>
                            </li>
                            <li class="breadcrumb-item active">
                                <span class="breadcrumb-current">Privacy Policy</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Hero Icon -->
                    <div class="hero-icon-wrapper mb-4">
                        <div class="hero-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="hero-icon-bg"></div>
                        <div class="privacy-rings">
                            <div class="ring ring-1"></div>
                            <div class="ring ring-2"></div>
                            <div class="ring ring-3"></div>
                        </div>
                    </div>

                    <!-- Hero Title -->
                    <h1 class="hero-title mb-4">
                        @if (Model != null)
                        {
                            @Model.Title
                        }
                        else
                        {
                            <span class="title-main">Privacy</span>
                            <span class="title-highlight">Policy</span>
                        }
                    </h1>

                    <!-- Hero Subtitle -->
                    <p class="hero-subtitle mb-5">
                        @if (Model != null && !string.IsNullOrEmpty(Model.MetaDescription))
                        {
                            @Model.MetaDescription
                        }
                        else
                        {
                            <text>Your privacy matters to us. We're committed to protecting your personal information<br>
                            and being transparent about how we collect, use, and safeguard your data.</text>
                        }
                    </p>

                    <!-- Privacy Features -->
                    <div class="privacy-features">
                        <div class="row justify-content-center">
                            <div class="col-md-3 col-sm-6">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h5>Secure</h5>
                                        <p>Data Protection</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h5>Private</h5>
                                        <p>No Data Selling</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h5>Control</h5>
                                        <p>Your Rights</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h5>Compliant</h5>
                                        <p>GDPR Ready</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trust Indicators -->
                    <div class="trust-indicators">
                        <div class="trust-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Last Updated: @DateTime.Now.ToString("MMMM yyyy")</span>
                        </div>
                        <div class="trust-item">
                            <i class="fas fa-globe"></i>
                            <span>GDPR Compliant</span>
                        </div>
                        <div class="trust-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator" onclick="scrollToContent()">
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
</section>

<!-- Privacy Content Section -->
<section class="modern-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card">
                    <div class="card-body p-5">
                        <div class="privacy-content">
                            <div class="mb-5">
                                <p class="lead text-muted">
                                    <strong>Last updated:</strong> @(Model?.LastModified.ToString("MMMM dd, yyyy") ?? DateTime.Now.ToString("MMMM dd, yyyy"))
                                </p>
                                @if (Model != null)
                                {
                                    @Html.Raw(Model.Content)
                                }
                                else
                                {
                                    <p>
                                        This Privacy Policy describes how Technoloway collects, uses, and protects your personal
                                        information when you use our website and services.
                                    </p>
                                }
                            </div>

                            @if (Model != null && Model.Sections.Any())
                            {
                                @foreach (var section in Model.Sections.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                                {
                                    <div class="privacy-section mb-5">
                                        <h3 class="section-title">
                                            @if (!string.IsNullOrEmpty(section.IconClass))
                                            {
                                                <i class="@(section.IconClass) text-primary me-2"></i>
                                            }
                                            @(section.Title)
                                        </h3>
                                        @Html.Raw(section.Content)
                                    </div>
                                }
                            }
                            else
                            {
                                <!-- Fallback static content -->
                                <div class="privacy-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    1. Information We Collect
                                </h3>
                                <p>We collect information you provide directly to us, such as:</p>
                                <ul class="custom-list">
                                    <li><strong>Personal Information:</strong> Name, email address, phone number, company details</li>
                                    <li><strong>Project Information:</strong> Requirements, specifications, and communication history</li>
                                    <li><strong>Account Information:</strong> Login credentials and preferences</li>
                                    <li><strong>Payment Information:</strong> Billing address and payment method details</li>
                                </ul>

                                <h5 class="mt-4 mb-3">Automatically Collected Information</h5>
                                <ul class="custom-list">
                                    <li>IP address and device information</li>
                                    <li>Browser type and version</li>
                                    <li>Pages visited and time spent on our website</li>
                                    <li>Referring website information</li>
                                </ul>
                            </div>

                            <div class="privacy-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-cogs text-primary me-2"></i>
                                    2. How We Use Your Information
                                </h3>
                                <p>We use the collected information for:</p>
                                <ul class="custom-list">
                                    <li>Providing and improving our services</li>
                                    <li>Communicating with you about projects and updates</li>
                                    <li>Processing payments and managing accounts</li>
                                    <li>Sending important notifications and updates</li>
                                    <li>Analyzing website usage to improve user experience</li>
                                    <li>Complying with legal obligations</li>
                                </ul>
                            </div>

                            <div class="privacy-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-share-alt text-primary me-2"></i>
                                    3. Information Sharing
                                </h3>
                                <p>We do not sell, trade, or rent your personal information. We may share information only in these circumstances:</p>
                                <ul class="custom-list">
                                    <li><strong>With your consent:</strong> When you explicitly agree to sharing</li>
                                    <li><strong>Service providers:</strong> Third-party vendors who assist in our operations</li>
                                    <li><strong>Legal requirements:</strong> When required by law or legal process</li>
                                    <li><strong>Business transfers:</strong> In case of merger, acquisition, or asset sale</li>
                                </ul>
                            </div>

                            <div class="privacy-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-shield-alt text-primary me-2"></i>
                                    4. Data Security
                                </h3>
                                <p>We implement appropriate security measures to protect your information:</p>
                                <ul class="custom-list">
                                    <li>SSL encryption for data transmission</li>
                                    <li>Secure servers and databases</li>
                                    <li>Regular security audits and updates</li>
                                    <li>Access controls and authentication</li>
                                    <li>Employee training on data protection</li>
                                </ul>
                            </div>

                            <div class="privacy-section mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-user-cog text-primary me-2"></i>
                                    5. Your Rights and Choices
                                </h3>
                                <p>You have the right to:</p>
                                <ul class="custom-list">
                                    <li><strong>Access:</strong> Request information about data we have about you</li>
                                    <li><strong>Correction:</strong> Update or correct inaccurate information</li>
                                    <li><strong>Deletion:</strong> Request deletion of your personal data</li>
                                    <li><strong>Portability:</strong> Receive your data in a portable format</li>
                                    <li><strong>Opt-out:</strong> Unsubscribe from marketing communications</li>
                                </ul>
                            </div>

                            <div class="privacy-section">
                                <h3 class="section-title">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    6. Contact Us
                                </h3>
                                <p>
                                    If you have questions about this Privacy Policy or our data practices, please contact us:
                                </p>
                                <div class="contact-info">
                                    <p><strong>Email:</strong> <EMAIL></p>
                                    <p><strong>Phone:</strong> +****************</p>
                                    <p><strong>Address:</strong> 123 Tech Street, Innovation City, TC 12345</p>
                                </div>
                            </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="modern-section bg-light py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Questions About Your Privacy?</h3>
                <p class="text-muted mb-4">
                    We're committed to protecting your privacy. Contact us if you have any concerns.
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/contact" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>
                        Contact Us
                    </a>
                    <a href="/terms" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-file-contract me-2"></i>
                        Terms of Service
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Privacy Hero Section */
    .privacy-hero-section {
        position: relative;
        min-height: 75vh;
        display: flex;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .privacy-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
            linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.1) 75%),
            linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.1) 75%);
        background-size: 40px 40px;
        background-position: 0 0, 0 20px, 20px -20px, -20px 0px;
        animation: privacyPatternMove 30s linear infinite;
    }

    .privacy-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(79, 172, 254, 0.9) 0%, rgba(0, 242, 254, 0.9) 100%);
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .min-vh-60 {
        min-height: 60vh;
    }

    /* Breadcrumb Styling */
    .hero-breadcrumb .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-link {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .breadcrumb-link:hover {
        color: white;
        transform: translateY(-1px);
    }

    .breadcrumb-separator {
        color: rgba(255, 255, 255, 0.6);
        margin: 0 1rem;
    }

    .breadcrumb-current {
        color: #00f2fe;
        font-weight: 600;
        text-shadow: 0 0 10px rgba(0, 242, 254, 0.5);
    }

    /* Hero Icon */
    .hero-icon-wrapper {
        position: relative;
        display: inline-block;
    }

    .hero-icon {
        position: relative;
        z-index: 3;
        width: 90px;
        height: 90px;
        background: rgba(255, 255, 255, 0.15);
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: #00f2fe;
        backdrop-filter: blur(15px);
        animation: shieldFloat 4s ease-in-out infinite;
        box-shadow: 0 0 30px rgba(0, 242, 254, 0.3);
    }

    .hero-icon-bg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 140px;
        height: 140px;
        background: radial-gradient(circle, rgba(0, 242, 254, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        animation: shieldPulse 3s ease-in-out infinite;
        z-index: 1;
    }

    .privacy-rings {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
    }

    .ring {
        position: absolute;
        border: 2px solid rgba(0, 242, 254, 0.3);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .ring-1 {
        width: 120px;
        height: 120px;
        animation: ringRotate 8s linear infinite;
    }

    .ring-2 {
        width: 160px;
        height: 160px;
        animation: ringRotate 12s linear infinite reverse;
    }

    .ring-3 {
        width: 200px;
        height: 200px;
        animation: ringRotate 16s linear infinite;
    }

    /* Hero Title */
    .hero-title {
        font-size: 3.8rem;
        font-weight: 800;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }

    .title-main {
        color: white;
        display: block;
    }

    .title-highlight {
        color: #00f2fe;
        display: block;
        background: linear-gradient(45deg, #00f2fe, #4facfe);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(0, 242, 254, 0.5);
    }

    .hero-subtitle {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.95);
        line-height: 1.6;
        max-width: 700px;
        margin: 0 auto;
    }

    /* Privacy Features */
    .privacy-features {
        margin-top: 3rem;
    }

    .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 2rem 1rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        backdrop-filter: blur(15px);
        transition: all 0.4s ease;
        margin-bottom: 1.5rem;
        height: 100%;
    }

    .feature-item:hover {
        transform: translateY(-8px) scale(1.02);
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: rgba(0, 242, 254, 0.2);
        border: 2px solid rgba(0, 242, 254, 0.4);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: #00f2fe;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .feature-item:hover .feature-icon {
        background: rgba(0, 242, 254, 0.3);
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(0, 242, 254, 0.4);
    }

    .feature-content h5 {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }

    .feature-content p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.95rem;
    }

    /* Trust Indicators */
    .trust-indicators {
        margin-top: 3rem;
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .trust-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.9rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .trust-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .trust-item i {
        color: #00f2fe;
        font-size: 1rem;
    }

    /* Scroll Indicator */
    .scroll-indicator {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        backdrop-filter: blur(15px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 242, 254, 0.2);
        transition: all 0.3s ease;
        z-index: 10;
        cursor: pointer;
        animation: bounce 2s infinite;
    }

    .scroll-indicator:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateX(-50%) translateY(-5px) scale(1.1);
        box-shadow: 0 12px 40px rgba(0, 242, 254, 0.4);
        border-color: rgba(0, 242, 254, 0.6);
    }

    .scroll-indicator:active {
        transform: translateX(-50%) translateY(-2px) scale(1.05);
        background: rgba(255, 255, 255, 0.35);
    }

    .scroll-indicator i {
        font-size: 1.8rem;
        color: #00f2fe;
        text-shadow: 0 2px 8px rgba(0, 242, 254, 0.5);
        filter: drop-shadow(0 0 8px rgba(0, 242, 254, 0.4));
        transition: all 0.3s ease;
    }

    .scroll-indicator:hover i {
        color: #4facfe;
        transform: translateY(2px);
        filter: drop-shadow(0 0 12px rgba(0, 242, 254, 0.6));
    }

    /* Animations */
    @@keyframes privacyPatternMove {
        0% { transform: translate(0, 0); }
        100% { transform: translate(40px, 40px); }
    }

    @@keyframes shieldFloat {
        0%, 100% { transform: translateY(0) rotate(0deg); }
        50% { transform: translateY(-15px) rotate(5deg); }
    }

    @@keyframes shieldPulse {
        0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
        50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
    }

    @@keyframes ringRotate {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    @@keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    /* Content Sections */
    .privacy-content {
        line-height: 1.7;
    }

    .privacy-section {
        border-left: 4px solid var(--primary-color);
        padding-left: 2rem;
        margin-left: 1rem;
    }

    .section-title {
        color: var(--text-dark);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .custom-list {
        padding-left: 1.5rem;
    }

    .custom-list li {
        margin-bottom: 0.5rem;
        color: var(--text-muted);
    }

    .contact-info p {
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .hero-title {
            font-size: 2.8rem;
        }

        .hero-subtitle {
            font-size: 1.15rem;
        }

        .feature-item {
            margin-bottom: 1rem;
        }

        .trust-indicators {
            gap: 1rem;
        }

        .trust-item {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
        }

        .hero-icon {
            width: 70px;
            height: 70px;
            font-size: 2rem;
        }

        .hero-icon-bg {
            width: 110px;
            height: 110px;
        }

        .ring-1 { width: 100px; height: 100px; }
        .ring-2 { width: 130px; height: 130px; }
        .ring-3 { width: 160px; height: 160px; }
    }

    @@media (max-width: 576px) {
        .trust-indicators {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<script>
    function scrollToContent() {
        const contentSection = document.querySelector('.modern-section.py-5');
        if (contentSection) {
            contentSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
</script>
