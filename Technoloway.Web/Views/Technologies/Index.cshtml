@model IEnumerable<Technoloway.Core.Entities.Technology>

@{
    ViewData["Title"] = "Our Technologies";
    ViewData["MetaDescription"] = "Explore the cutting-edge technologies we use to build innovative software solutions for our clients.";
    ViewData["MetaKeywords"] = "technologies, software development, web development, mobile development, cloud solutions";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
<partial name="_HeroSection" />

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header fixed-height-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Technologies</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Our</span> Technologies
                        </h1>
                        <p class="page-subtitle">
                            Explore the cutting-edge technologies we use to build innovative software solutions for our clients.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Technologies Introduction -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Cutting-Edge</span> Technologies
                    </h2>
                    <p class="section-subtitle">
                        At Technoloway, we stay at the forefront of technology to deliver innovative solutions for our clients.
                        Our team is proficient in a wide range of technologies, frameworks, and platforms that enable us to build
                        robust, scalable, and secure applications.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Grid -->
<section class="modern-section" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
    <div class="container">
        <div class="row g-4">
            @foreach (var tech in Model)
            {
                <div class="col-md-6 col-lg-4">
                    <div class="modern-card animate-on-scroll" style="height: 100%;">
                        <div class="card-body text-center">
                            <div class="tech-icon-wrapper mb-4">
                                <img src="@tech.IconUrl" alt="@tech.Name" class="tech-icon-img">
                            </div>
                            <h3 class="card-title">@tech.Name</h3>
                            <p class="card-subtitle mb-4">@tech.Description</p>
                            <a asp-action="Details" asp-route-id="@tech.Id" class="modern-btn primary">
                                <span class="btn-text">Learn More</span>
                                <span class="btn-icon">
                                    <i class="fas fa-arrow-right"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Technology Categories -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Our Technology</span> Stack
                    </h2>
                    <p class="section-subtitle">
                        We use a comprehensive stack of technologies to deliver solutions that meet the unique needs of each client.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="modern-card tech-stack-card animate-on-scroll">
                    <div class="card-body">
                        <div class="tech-category-icon">
                            <i class="fas fa-laptop-code"></i>
                        </div>
                        <h3 class="card-title">Frontend</h3>
                        <ul class="tech-list">
                            <li>React</li>
                            <li>Angular</li>
                            <li>Vue.js</li>
                            <li>HTML5/CSS3</li>
                            <li>JavaScript/TypeScript</li>
                            <li>Bootstrap/Tailwind CSS</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card tech-stack-card animate-on-scroll" data-delay="100">
                    <div class="card-body">
                        <div class="tech-category-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="card-title">Backend</h3>
                        <ul class="tech-list">
                            <li>ASP.NET Core</li>
                            <li>Node.js</li>
                            <li>Python/Django</li>
                            <li>Java/Spring Boot</li>
                            <li>PHP/Laravel</li>
                            <li>Ruby on Rails</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card tech-stack-card animate-on-scroll" data-delay="200">
                    <div class="card-body">
                        <div class="tech-category-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="card-title">Databases</h3>
                        <ul class="tech-list">
                            <li>SQL Server</li>
                            <li>PostgreSQL</li>
                            <li>MySQL</li>
                            <li>MongoDB</li>
                            <li>Redis</li>
                            <li>Elasticsearch</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card tech-stack-card animate-on-scroll" data-delay="300">
                    <div class="card-body">
                        <div class="tech-category-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <h3 class="card-title">Cloud & DevOps</h3>
                        <ul class="tech-list">
                            <li>Azure</li>
                            <li>AWS</li>
                            <li>Google Cloud</li>
                            <li>Docker</li>
                            <li>Kubernetes</li>
                            <li>CI/CD Pipelines</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Development Process -->
<section class="modern-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title text-white">
                        <span class="title-highlight">Our Development</span> Process
                    </h2>
                    <p class="section-subtitle text-white opacity-90">
                        We follow a structured development process that ensures high-quality, reliable, and maintainable software.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="modern-card process-card animate-on-scroll">
                    <div class="card-body text-center">
                        <div class="process-number">
                            <span>1</span>
                        </div>
                        <h3 class="card-title">Planning & Analysis</h3>
                        <p class="card-subtitle">We start by understanding your business requirements and objectives, then develop a comprehensive plan.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="modern-card process-card animate-on-scroll" data-delay="100">
                    <div class="card-body text-center">
                        <div class="process-number">
                            <span>2</span>
                        </div>
                        <h3 class="card-title">Design & Development</h3>
                        <p class="card-subtitle">Our team designs and develops your solution using the most appropriate technologies for your needs.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="modern-card process-card animate-on-scroll" data-delay="200">
                    <div class="card-body text-center">
                        <div class="process-number">
                            <span>3</span>
                        </div>
                        <h3 class="card-title">Testing & Deployment</h3>
                        <p class="card-subtitle">We rigorously test your solution to ensure quality, then deploy it to your preferred environment.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="modern-section">
    <div class="container">
        <div class="modern-card cta-card animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0">
                        <h2 class="section-title mb-3">
                            <span class="title-highlight">Ready to Build</span> Your Next Project?
                        </h2>
                        <p class="section-subtitle mb-0">Contact us today to discuss how we can help you leverage these technologies for your business.</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <a href="/Contact" class="modern-btn primary large">
                            <span class="btn-text">Get in Touch</span>
                            <span class="btn-icon">
                                <i class="fas fa-arrow-right"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Tech icon hover effects
            const techCards = document.querySelectorAll('.modern-card');

            techCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}