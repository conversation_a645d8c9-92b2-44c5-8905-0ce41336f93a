/* Hero Slideshow Styles */
.hero-dynamic {
    position: relative;
    overflow: hidden;
}

.hero-slideshow {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    visibility: hidden;
    transition: opacity 1s ease-in-out, visibility 1s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
    visibility: visible;
    z-index: 1;
}

.hero-slide[data-animation="fade"] {
    transition: opacity 1s ease-in-out, visibility 1s ease-in-out;
}

.hero-slide[data-animation="slide"] {
    transform: translateX(100%);
    transition: transform 1s ease-in-out, opacity 1s ease-in-out, visibility 1s ease-in-out;
}

.hero-slide[data-animation="slide"].active {
    transform: translateX(0);
}

.hero-slide[data-animation="zoom"] {
    transform: scale(1.1);
    transition: transform 1s ease-in-out, opacity 1s ease-in-out, visibility 1s ease-in-out;
}

.hero-slide[data-animation="zoom"].active {
    transform: scale(1);
}

/* Video Slide Styles */
.hero-slide-video {
    background: none !important;
}

.hero-video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.hero-video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
    z-index: 0;
}

.hero-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));
    z-index: 1;
}

/* Hero Content Styles */
.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle-large {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #e9ecef;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #dee2e6;
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
}

.badge-icon {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Navigation Arrows */
.hero-nav-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none;
}

.hero-nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-nav-arrow:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.hero-nav-prev {
    left: 2rem;
}

.hero-nav-next {
    right: 2rem;
}

/* Navigation Dots */
.hero-nav-dots {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 10;
}

.hero-nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-nav-dot.active {
    background: white;
    transform: scale(1.2);
}

.hero-nav-dot:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
}

/* Single Hero Section */
.hero-single {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 0;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.hero-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    z-index: 1;
}

/* Floating Elements */
.hero-visual {
    position: relative;
    z-index: 2;
}

.hero-image-container {
    position: relative;
    width: 400px;
    height: 400px;
    margin: 0 auto;
}

.floating-element {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    animation: float 6s ease-in-out infinite;
}

.hero-element-1 {
    top: 0;
    left: 0;
    animation-delay: 0s;
}

.hero-element-2 {
    top: 0;
    right: 0;
    animation-delay: 1.5s;
}

.hero-element-3 {
    bottom: 0;
    left: 0;
    animation-delay: 3s;
}

.hero-element-4 {
    bottom: 0;
    right: 0;
    animation-delay: 4.5s;
}

.hero-center-graphic {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
}

.graphic-circle {
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.graphic-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle-large {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-nav-arrow {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .hero-nav-prev {
        left: 1rem;
    }

    .hero-nav-next {
        right: 1rem;
    }

    .hero-image-container {
        width: 300px;
        height: 300px;
    }

    .floating-element {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .graphic-inner {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle-large {
        font-size: 1.1rem;
    }

    .hero-nav-dots {
        bottom: 1rem;
    }

    .hero-image-container {
        width: 250px;
        height: 250px;
    }
}
