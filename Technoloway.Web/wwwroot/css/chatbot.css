/* Technolo<PERSON>ay Chatbot Styles */
:root {
    --chatbot-primary: #667eea;
    --chatbot-primary-dark: #5a67d8;
    --chatbot-secondary: #764ba2;
    --chatbot-success: #48bb78;
    --chatbot-warning: #ed8936;
    --chatbot-error: #f56565;
    --chatbot-text-primary: #2d3748;
    --chatbot-text-secondary: #718096;
    --chatbot-bg-primary: #ffffff;
    --chatbot-bg-secondary: #f7fafc;
    --chatbot-border-color: #e2e8f0;
    --chatbot-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --chatbot-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --chatbot-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --chatbot-border-radius: 12px;
    --chatbot-transition: all 0.3s ease;
}

/* Reset and Base Styles */
.chatbot-container * {
    box-sizing: border-box;
}

.hidden {
    display: none !important;
}

/* Chatbot Toggle Button */
.chatbot-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--chatbot-primary), var(--chatbot-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--chatbot-shadow-lg);
    transition: var(--chatbot-transition);
    z-index: 1000;
    color: white;
    font-size: 24px;
    border: none;
    font-family: inherit;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 25px rgba(102, 126, 234, 0.4);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--chatbot-error);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Chatbot Container */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 380px;
    height: 600px;
    background: var(--chatbot-bg-primary);
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow-lg);
    display: flex;
    flex-direction: column;
    z-index: 1001;
    overflow: hidden;
    transition: var(--chatbot-transition);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chatbot-container.minimized {
    height: 60px;
}

/* Header */
.chatbot-header {
    background: linear-gradient(135deg, var(--chatbot-primary), var(--chatbot-secondary));
    color: white;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bot-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.bot-info h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    opacity: 0.9;
}

.status.online i {
    color: var(--chatbot-success);
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--chatbot-transition);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Messages Container */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: var(--chatbot-bg-secondary);
}

.message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, var(--chatbot-primary), var(--chatbot-secondary));
    color: white;
}

.user-message .message-avatar {
    background: var(--chatbot-text-secondary);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.message-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow-sm);
    margin-bottom: 4px;
    word-wrap: break-word;
}

.user-message .message-bubble {
    background: var(--chatbot-primary);
    color: white;
}

.message-bubble p {
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

.message-time {
    font-size: 11px;
    color: var(--chatbot-text-secondary);
    padding: 0 4px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 16px;
}

.quick-action-btn {
    background: white;
    border: 2px solid var(--chatbot-border-color);
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--chatbot-transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--chatbot-text-primary);
    font-family: inherit;
}

.quick-action-btn:hover {
    border-color: var(--chatbot-primary);
    background: var(--chatbot-primary);
    color: white;
}

.quick-action-btn i {
    font-size: 16px;
}

/* Typing Indicator */
.typing-indicator {
    padding: 0 16px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
    background: white;
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow-sm);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: var(--chatbot-text-secondary);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Input Area */
.input-area {
    padding: 16px;
    background: white;
    border-top: 1px solid var(--chatbot-border-color);
}

.input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

#message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--chatbot-border-color);
    border-radius: 25px;
    outline: none;
    font-size: 14px;
    transition: var(--chatbot-transition);
    font-family: inherit;
}

#message-input:focus {
    border-color: var(--chatbot-primary);
}

.send-btn {
    width: 40px;
    height: 40px;
    background: var(--chatbot-primary);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--chatbot-transition);
}

.send-btn:disabled {
    background: var(--chatbot-text-secondary);
    cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
    background: var(--chatbot-primary-dark);
    transform: scale(1.1);
}

/* Suggested Responses */
.suggested-responses {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-btn {
    background: var(--chatbot-bg-secondary);
    border: 1px solid var(--chatbot-border-color);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--chatbot-transition);
    font-family: inherit;
}

.suggestion-btn:hover {
    background: var(--chatbot-primary);
    color: white;
    border-color: var(--chatbot-primary);
}

/* Footer */
.chatbot-footer {
    padding: 8px 16px;
    background: var(--chatbot-bg-secondary);
    border-top: 1px solid var(--chatbot-border-color);
    text-align: center;
    font-size: 11px;
    color: var(--chatbot-text-secondary);
}

/* Mobile Responsive */
@media (max-width: 480px) {
    .chatbot-container {
        width: calc(100vw - 20px);
        height: calc(100vh - 40px);
        bottom: 10px;
        right: 10px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .chatbot-toggle {
        bottom: 15px;
        right: 15px;
        width: 55px;
        height: 55px;
        font-size: 22px;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-content {
    background: white;
    border-radius: var(--chatbot-border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--chatbot-shadow-lg);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--chatbot-border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--chatbot-primary), var(--chatbot-secondary));
    color: white;
    border-radius: var(--chatbot-border-radius) var(--chatbot-border-radius) 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: white;
    padding: 4px;
    border-radius: 4px;
    transition: var(--chatbot-transition);
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Form Styles */
.contact-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--chatbot-text-primary);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--chatbot-border-color);
    border-radius: 8px;
    font-size: 14px;
    transition: var(--chatbot-transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--chatbot-primary);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--chatbot-transition);
    font-size: 14px;
    font-family: inherit;
}

.btn-primary {
    background: var(--chatbot-primary);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: var(--chatbot-primary-dark);
}

.btn-secondary {
    background: transparent;
    color: var(--chatbot-text-secondary);
    border: 2px solid var(--chatbot-border-color);
}

.btn-secondary:hover {
    border-color: var(--chatbot-text-secondary);
    background: var(--chatbot-bg-secondary);
}

/* Ensure chatbot doesn't interfere with existing site elements */
.chatbot-container,
.chatbot-toggle {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Override any conflicting styles */
.chatbot-container *,
.chatbot-toggle *,
.modal * {
    box-sizing: border-box;
}

/* Ensure proper z-index stacking */
.chatbot-toggle {
    z-index: 9999;
}

.chatbot-container {
    z-index: 10000;
}

.modal {
    z-index: 10001;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}
