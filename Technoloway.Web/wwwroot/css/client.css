/* Client Portal Modern Styles */

:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --dark-bg: #1e293b;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 0.75rem;
    --border-radius-sm: 0.5rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    overflow-x: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
    line-height: 1.6;
}

/* Dark mode support */
[data-theme="dark"] {
    --light-bg: #0f172a;
    --card-bg: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #334155;
}

#wrapper {
    display: flex;
    min-height: 100vh;
}

/* Enhanced Modern Sidebar */
.modern-sidebar {
    min-height: 100vh;
    width: 300px;
    margin-left: -300px;
    transition: var(--transition);
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

.modern-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    pointer-events: none;
}

/* Legacy sidebar wrapper for compatibility */
#sidebar-wrapper {
    min-height: 100vh;
    width: 300px;
    margin-left: -300px;
    transition: var(--transition);
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

#sidebar-wrapper .sidebar-heading {
    padding: 1.5rem 1.25rem;
    font-size: 1.25rem;
    font-weight: 700;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

#sidebar-wrapper .list-group {
    width: 280px;
    background: transparent;
}

#page-content-wrapper {
    min-width: 100vw;
    transition: var(--transition);
    background: var(--light-bg);
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

#wrapper.toggled #page-content-wrapper {
    margin-left: 280px;
    min-width: calc(100vw - 280px);
}

/* Modern Navigation Items */
.list-group-item {
    border: none;
    padding: 1rem 1.25rem;
    margin: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.list-group-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    z-index: -1;
}

.list-group-item:hover::before,
.list-group-item.active::before {
    width: 100%;
}

.list-group-item:hover,
.list-group-item.active {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.list-group-item i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

/* Top Navigation */
.navbar {
    background: var(--card-bg) !important;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
}

#sidebarToggle {
    background: var(--primary-color) !important;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    transition: var(--transition);
}

#sidebarToggle:hover {
    background: var(--primary-dark) !important;
    transform: scale(1.05);
}

/* Modern Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    background: var(--card-bg);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    padding: 1.25rem 1.5rem;
    color: white;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-card.success::before { background: var(--success-color); }
.stats-card.warning::before { background: var(--warning-color); }
.stats-card.danger::before { background: var(--danger-color); }
.stats-card.info::before { background: var(--info-color); }

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-icon.primary { background: rgba(79, 70, 229, 0.1); color: var(--primary-color); }
.stats-icon.success { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
.stats-icon.warning { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
.stats-icon.danger { background: rgba(239, 68, 68, 0.1); color: var(--danger-color); }
.stats-icon.info { background: rgba(59, 130, 246, 0.1); color: var(--info-color); }

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Modern Buttons */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Tables */
.table {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: var(--light-bg);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.table tbody td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background: rgba(79, 70, 229, 0.05);
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background: var(--border-color);
    overflow: hidden;
}

.progress-bar {
    border-radius: 4px;
    transition: var(--transition);
}

/* Badges */
.badge {
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    padding: 0.5rem 0.75rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    border-left: 4px solid;
}

.alert-success { border-left-color: var(--success-color); background: rgba(16, 185, 129, 0.1); }
.alert-warning { border-left-color: var(--warning-color); background: rgba(245, 158, 11, 0.1); }
.alert-danger { border-left-color: var(--danger-color); background: rgba(239, 68, 68, 0.1); }
.alert-info { border-left-color: var(--info-color); background: rgba(59, 130, 246, 0.1); }

/* Form Controls */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: var(--card-bg);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    #sidebar-wrapper {
        width: 100%;
        margin-left: -100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0;
    }

    #wrapper.toggled #page-content-wrapper {
        margin-left: 0;
        min-width: 100vw;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Message Styles */
.message-bubble {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    word-wrap: break-word;
}

.message-bubble.client {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 0.5rem;
}

.message-bubble.admin {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 0.5rem;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Document Preview */
.document-preview {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--light-bg);
    transition: var(--transition);
}

.document-preview:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-indicator.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-indicator.in-progress {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-indicator.overdue {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid #198754 !important;
}

/* Enhanced Sidebar Styles */
.sidebar-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 0.75rem;
}

.sidebar-section-header {
    margin-top: 1rem;
}

.sidebar-footer {
    margin-top: auto;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    background: rgba(255, 255, 255, 0.05);
}

/* Main Content Area */
.main-content {
    min-height: calc(100vh - 80px);
    padding: 0;
}

/* Breadcrumb Styling */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-secondary);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

/* Notification Dropdown */
.notification-dropdown {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
}

.notification-dropdown .dropdown-header {
    background: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

.notification-dropdown .dropdown-item {
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.notification-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* Enhanced Alert Styles */
.alert {
    margin: 1rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-sm);
}

.alert .btn-close {
    filter: brightness(0.8);
}

/* Project Card Enhancements */
.project-card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.project-card .card-img-top {
    position: relative;
    overflow: hidden;
}

.project-card .card-img-top::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(79, 70, 229, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .card-img-top::after {
    opacity: 1;
}

/* Invoice Status Badges */
.invoice-status {
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.invoice-status.paid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.invoice-status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.invoice-status.overdue {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Document Preview Styles */
.document-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    background: var(--card-bg);
}

.document-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.document-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.document-icon.pdf { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
.document-icon.doc { background: rgba(13, 110, 253, 0.1); color: #0d6efd; }
.document-icon.image { background: rgba(25, 135, 84, 0.1); color: #198754; }
.document-icon.archive { background: rgba(108, 117, 125, 0.1); color: #6c757d; }

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Responsive Enhancements */
@media (max-width: 992px) {
    .stats-card {
        margin-bottom: 1.5rem;
    }

    .project-card {
        margin-bottom: 2rem;
    }

    .main-content {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .sidebar-wrapper,
    .navbar,
    .btn,
    .dropdown {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

.border-left-info {
    border-left: 0.25rem solid #0dcaf0 !important;
}

.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Modern Sidebar Components */
.sidebar-header {
    padding: 1rem 1rem 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    transition: var(--transition);
}

.sidebar-brand:hover {
    color: white;
    transform: scale(1.02);
}

.brand-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), #3730a3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.brand-logo {
    width: 20px;
    height: 20px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.brand-text {
    flex: 1;
}

.brand-name {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 0;
}

.brand-subtitle {
    display: block;
    font-size: 0.7rem;
    opacity: 0.7;
    font-weight: 500;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 0.25rem 0;
    flex: 1;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 0.5rem;
}

.nav-section-title {
    padding: 0 1rem 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(255, 255, 255, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.25rem;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 0.05rem 0.5rem;
    border-radius: 8px;
    font-weight: 500;
}

.nav-item:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.nav-item.active {
    color: white;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(79, 70, 229, 0.3);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.nav-item.active .nav-indicator {
    width: 4px;
    background: linear-gradient(180deg, var(--primary-color), #3730a3);
    box-shadow: 0 0 8px rgba(79, 70, 229, 0.6);
}

.nav-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    margin-right: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
}

.nav-text {
    flex: 1;
    font-size: 0.85rem;
    font-weight: 500;
}

.nav-badge {
    background: linear-gradient(135deg, var(--primary-color), #3730a3);
    color: white;
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    margin-left: 0.25rem;
}

.nav-badge.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.nav-indicator {
    width: 0;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 2px 0 0 2px;
    transition: var(--transition);
}

/* Enhanced Sidebar Footer */
.sidebar-footer {
    padding: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.sidebar-user {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: var(--transition);
}

.sidebar-user:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #3730a3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 1rem;
    color: white;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    line-height: 1.1;
}

.user-role {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

/* Sidebar Animation Enhancements */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.nav-item {
    animation: fadeInUp 0.3s ease-out;
}

@media (min-width: 768px) {
    #sidebar-wrapper,
    .modern-sidebar {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper,
    #wrapper.toggled .modern-sidebar {
        margin-left: -300px;
    }
}
