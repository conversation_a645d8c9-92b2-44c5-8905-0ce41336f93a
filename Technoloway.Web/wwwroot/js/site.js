﻿// Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
// for details on configuring this project to bundle and minify static web assets.

// Hide top corner buttons
document.addEventListener('DOMContentLoaded', function() {
    // Function to remove top corner buttons
    function removeTopCornerButtons() {
        // Hide any existing top-corner-buttons
        const selectors = [
            '.top-corner-buttons',
            '[class*="top-corner"]',
            '[class*="corner-button"]',
            '.corner-buttons',
            'div[style*="position: absolute"][style*="top: 0"][style*="right: 0"]'
        ];

        selectors.forEach(function(selector) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(function(element) {
                element.style.display = 'none';
                element.style.visibility = 'hidden';
                element.style.opacity = '0';
                element.style.pointerEvents = 'none';
                element.style.zIndex = '-9999';

                // Try to remove the element completely
                try {
                    element.remove();
                } catch (e) {
                    console.log('Could not remove element:', e);
                }
            });
        });
    }

    // Run immediately
    removeTopCornerButtons();

    // Run again after a short delay to catch any elements added after initial load
    setTimeout(removeTopCornerButtons, 500);
    setTimeout(removeTopCornerButtons, 1000);
    setTimeout(removeTopCornerButtons, 2000);

    // Create a MutationObserver to hide any dynamically added top-corner-buttons
    const observer = new MutationObserver(function(mutations) {
        removeTopCornerButtons();
    });

    // Start observing the document body for changes
    observer.observe(document.body, { childList: true, subtree: true });
});
