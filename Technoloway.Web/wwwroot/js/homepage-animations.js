/**
 * Modern Homepage Animations
 * High-performance animations with Intersection Observer API
 */

class HomepageAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollProgress();
        this.setupIntersectionObserver();
        this.setupNavbarEffects();
        this.setupTypewriterEffect();
        this.setupParallaxEffects();
        this.setupTechCarousel();
        this.setupLazyLoading();
        this.setupSmoothScrolling();
        this.setupPerformanceOptimizations();
    }

    // Scroll Progress Indicator
    setupScrollProgress() {
        const progressBar = document.querySelector('.scroll-progress');
        if (!progressBar) return;

        const updateProgress = () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = `${scrollPercent}%`;
        };

        window.addEventListener('scroll', this.throttle(updateProgress, 10));
    }

    // Intersection Observer for Animations
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('in-view');
                    // Unobserve after animation to improve performance
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe all animation elements
        const animatedElements = document.querySelectorAll(
            '.observe-fade-up, .observe-fade-left, .observe-fade-right, .observe-scale'
        );
        
        animatedElements.forEach(el => observer.observe(el));
    }

    // Enhanced Navbar Effects
    setupNavbarEffects() {
        const navbar = document.querySelector('.navbar-modern');
        if (!navbar) return;

        let lastScrollY = window.pageYOffset;
        let ticking = false;

        const updateNavbar = () => {
            const scrollY = window.pageYOffset;
            
            if (scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Hide navbar on scroll down, show on scroll up
            if (scrollY > lastScrollY && scrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = scrollY;
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavbar);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick);

        // Active section highlighting
        this.setupActiveNavigation();
    }

    // Active Navigation Highlighting
    setupActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.getAttribute('id');
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${id}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        }, { threshold: 0.5 });

        sections.forEach(section => observer.observe(section));
    }

    // Typewriter Effect for Hero Title
    setupTypewriterEffect() {
        const typewriterElement = document.querySelector('.typewriter-text');
        if (!typewriterElement) return;

        const text = typewriterElement.textContent;
        typewriterElement.textContent = '';
        typewriterElement.style.borderRight = '2px solid';
        typewriterElement.style.animation = 'blink 1s infinite';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                typewriterElement.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                // Remove cursor after typing is complete
                setTimeout(() => {
                    typewriterElement.style.borderRight = 'none';
                    typewriterElement.style.animation = 'none';
                }, 1000);
            }
        };

        // Start typewriter effect when hero section is visible
        const heroObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(typeWriter, 500);
                    heroObserver.unobserve(entry.target);
                }
            });
        });

        const heroSection = document.querySelector('.hero-modern');
        if (heroSection) {
            heroObserver.observe(heroSection);
        }
    }

    // Parallax Effects
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.parallax-element');
        
        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            
            parallaxElements.forEach(element => {
                const rate = scrolled * -0.5;
                element.style.transform = `translateY(${rate}px)`;
            });
        };

        window.addEventListener('scroll', this.throttle(updateParallax, 16));
    }

    // Technology Carousel
    setupTechCarousel() {
        const techTrack = document.querySelector('.tech-track');
        if (!techTrack) return;

        // Clone items for infinite scroll
        const techItems = techTrack.children;
        const itemsArray = Array.from(techItems);
        
        // Clone all items to create seamless loop
        itemsArray.forEach(item => {
            const clone = item.cloneNode(true);
            techTrack.appendChild(clone);
        });

        // Pause animation on hover
        techTrack.addEventListener('mouseenter', () => {
            techTrack.style.animationPlayState = 'paused';
        });

        techTrack.addEventListener('mouseleave', () => {
            techTrack.style.animationPlayState = 'running';
        });
    }

    // Lazy Loading for Images
    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('loading-skeleton');
                    img.classList.add('fade-in');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('loading-skeleton');
            imageObserver.observe(img);
        });
    }

    // Smooth Scrolling for Anchor Links
    setupSmoothScrolling() {
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
                    
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Performance Optimizations
    setupPerformanceOptimizations() {
        // Add will-change property to animated elements
        const animatedElements = document.querySelectorAll(
            '.service-card-modern, .project-card-modern, .testimonial-card-modern'
        );
        
        animatedElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.classList.add('will-change-transform');
            });
            
            element.addEventListener('mouseleave', () => {
                element.classList.remove('will-change-transform');
            });
        });

        // Preload critical images
        this.preloadCriticalImages();
    }

    // Preload Critical Images
    preloadCriticalImages() {
        const criticalImages = [
            '/images/hero-image.png',
            '/images/technoloway-logo.svg'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    // Utility: Throttle function for performance
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Utility: Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// CSS Animation for typewriter cursor
const style = document.createElement('style');
style.textContent = `
    @keyframes blink {
        0%, 50% { border-color: transparent; }
        51%, 100% { border-color: var(--primary-color); }
    }
`;
document.head.appendChild(style);

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HomepageAnimations();
});

// Handle page visibility changes for performance
document.addEventListener('visibilitychange', () => {
    const animations = document.querySelectorAll('.tech-track');
    animations.forEach(animation => {
        if (document.hidden) {
            animation.style.animationPlayState = 'paused';
        } else {
            animation.style.animationPlayState = 'running';
        }
    });
});

// Export for potential external use
window.HomepageAnimations = HomepageAnimations;
