// Hero Slideshow JavaScript
class HeroSlideshow {
    constructor(container) {
        this.container = container;
        this.slides = container.querySelectorAll('.hero-slide');
        this.currentSlide = 0;
        this.isPlaying = false;
        this.autoplayTimer = null;
        
        // Get configuration from data attributes
        this.config = {
            autoplay: container.dataset.autoplay === 'true',
            speed: parseInt(container.dataset.speed) || 5000,
            showDots: container.dataset.showDots === 'true',
            showArrows: container.dataset.showArrows === 'true'
        };
        
        this.init();
    }
    
    init() {
        if (this.slides.length === 0) return;
        
        // Show first slide
        this.slides[0].classList.add('active');
        
        // Initialize navigation
        this.initNavigation();
        
        // Initialize video handling
        this.initVideoHandling();
        
        // Start autoplay if enabled
        if (this.config.autoplay) {
            this.startAutoplay();
        }
        
        // Pause on hover
        this.container.addEventListener('mouseenter', () => this.pauseAutoplay());
        this.container.addEventListener('mouseleave', () => {
            if (this.config.autoplay) this.startAutoplay();
        });
        
        // Pause when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoplay();
                this.pauseAllVideos();
            } else if (this.config.autoplay) {
                this.startAutoplay();
                this.playCurrentVideo();
            }
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.prevSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
        });
        
        // Touch/swipe support
        this.initTouchSupport();
    }
    
    initNavigation() {
        // Arrow navigation
        if (this.config.showArrows) {
            const prevBtn = this.container.querySelector('.hero-nav-prev');
            const nextBtn = this.container.querySelector('.hero-nav-next');
            
            if (prevBtn) prevBtn.addEventListener('click', () => this.prevSlide());
            if (nextBtn) nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        // Dot navigation
        if (this.config.showDots) {
            const dots = this.container.querySelectorAll('.hero-nav-dot');
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => this.goToSlide(index));
            });
        }
    }
    
    initVideoHandling() {
        this.slides.forEach((slide, index) => {
            const video = slide.querySelector('.hero-video');
            if (video) {
                // Ensure video is ready
                video.addEventListener('loadeddata', () => {
                    if (index === this.currentSlide) {
                        this.playVideo(video);
                    }
                });
                
                // Handle video end (for non-looping videos)
                video.addEventListener('ended', () => {
                    if (!video.loop && this.config.autoplay) {
                        this.nextSlide();
                    }
                });
            }
        });
    }
    
    initTouchSupport() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;
        
        this.container.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        this.container.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            // Only trigger if horizontal swipe is more significant than vertical
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            }
        });
    }
    
    goToSlide(index) {
        if (index === this.currentSlide || index < 0 || index >= this.slides.length) return;
        
        // Pause current video
        this.pauseCurrentVideo();
        
        // Remove active class from current slide
        this.slides[this.currentSlide].classList.remove('active');
        
        // Update current slide index
        this.currentSlide = index;
        
        // Add active class to new slide
        this.slides[this.currentSlide].classList.add('active');
        
        // Play video if current slide has one
        this.playCurrentVideo();
        
        // Update dots
        this.updateDots();
        
        // Reset autoplay timer
        if (this.config.autoplay) {
            this.resetAutoplay();
        }
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }
    
    prevSlide() {
        const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prevIndex);
    }
    
    playCurrentVideo() {
        const currentSlide = this.slides[this.currentSlide];
        const video = currentSlide.querySelector('.hero-video');
        if (video) {
            this.playVideo(video);
        }
    }
    
    pauseCurrentVideo() {
        const currentSlide = this.slides[this.currentSlide];
        const video = currentSlide.querySelector('.hero-video');
        if (video) {
            this.pauseVideo(video);
        }
    }
    
    playVideo(video) {
        if (video && video.paused) {
            video.play().catch(e => {
                console.log('Video autoplay prevented:', e);
            });
        }
    }
    
    pauseVideo(video) {
        if (video && !video.paused) {
            video.pause();
        }
    }
    
    pauseAllVideos() {
        this.slides.forEach(slide => {
            const video = slide.querySelector('.hero-video');
            if (video) {
                this.pauseVideo(video);
            }
        });
    }
    
    updateDots() {
        const dots = this.container.querySelectorAll('.hero-nav-dot');
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentSlide);
        });
    }
    
    startAutoplay() {
        if (this.isPlaying) return;
        
        this.isPlaying = true;
        this.autoplayTimer = setInterval(() => {
            this.nextSlide();
        }, this.config.speed);
    }
    
    pauseAutoplay() {
        if (!this.isPlaying) return;
        
        this.isPlaying = false;
        if (this.autoplayTimer) {
            clearInterval(this.autoplayTimer);
            this.autoplayTimer = null;
        }
    }
    
    resetAutoplay() {
        this.pauseAutoplay();
        if (this.config.autoplay) {
            this.startAutoplay();
        }
    }
}

// Initialize slideshow when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const slideshowContainer = document.querySelector('.hero-slideshow');
    if (slideshowContainer) {
        new HeroSlideshow(slideshowContainer);
    }
});
