using System.Drawing;
using System.Drawing.Imaging;

namespace Technoloway.Web.Services;

public interface IFileUploadService
{
    Task<string> UploadImageAsync(IFormFile file, string folder, string? existingFileName = null);
    bool DeleteImage(string filePath);
    bool IsValidImageFile(IFormFile file);
    string GenerateUniqueFileName(string originalFileName);
}

public class FileUploadService : IFileUploadService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<FileUploadService> _logger;
    private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
    private readonly long _maxFileSize = 5 * 1024 * 1024; // 5MB

    public FileUploadService(IWebHostEnvironment environment, ILogger<FileUploadService> logger)
    {
        _environment = environment;
        _logger = logger;
    }

    public async Task<string> UploadImageAsync(IFormFile file, string folder, string? existingFileName = null)
    {
        try
        {
            if (!IsValidImageFile(file))
            {
                throw new ArgumentException("Invalid image file");
            }

            // Create upload directory if it doesn't exist
            var uploadPath = Path.Combine(_environment.WebRootPath, "images", folder);
            if (!Directory.Exists(uploadPath))
            {
                Directory.CreateDirectory(uploadPath);
            }

            // Delete existing file if provided
            if (!string.IsNullOrEmpty(existingFileName))
            {
                var existingPath = Path.Combine(uploadPath, existingFileName);
                if (File.Exists(existingPath))
                {
                    File.Delete(existingPath);
                }
            }

            // Generate unique filename
            var fileName = GenerateUniqueFileName(file.FileName);
            var filePath = Path.Combine(uploadPath, fileName);

            // Save the file
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Optimize image (resize if too large)
            OptimizeImage(filePath);

            // Return the relative URL
            return $"/images/{folder}/{fileName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image file");
            throw;
        }
    }

    public bool DeleteImage(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            // Convert relative URL to physical path
            var physicalPath = Path.Combine(_environment.WebRootPath, filePath.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));

            if (File.Exists(physicalPath))
            {
                File.Delete(physicalPath);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting image file: {FilePath}", filePath);
            return false;
        }
    }

    public bool IsValidImageFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return false;

        if (file.Length > _maxFileSize)
            return false;

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_allowedExtensions.Contains(extension))
            return false;

        // Check MIME type
        var validMimeTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
        if (!validMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
            return false;

        // For WebP files, we can't use System.Drawing.Image as it doesn't support WebP in older .NET versions
        // So we'll validate based on file signature (magic bytes) for WebP
        if (extension == ".webp")
        {
            return IsValidWebPFile(file);
        }

        // Check if it's actually an image by trying to read it (for other formats)
        try
        {
            using var stream = file.OpenReadStream();
            using var image = Image.FromStream(stream);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private bool IsValidWebPFile(IFormFile file)
    {
        try
        {
            using var stream = file.OpenReadStream();
            var buffer = new byte[12];
            stream.Read(buffer, 0, 12);

            // WebP file signature: "RIFF" + 4 bytes + "WEBP"
            return buffer[0] == 0x52 && buffer[1] == 0x49 && buffer[2] == 0x46 && buffer[3] == 0x46 && // "RIFF"
                   buffer[8] == 0x57 && buffer[9] == 0x45 && buffer[10] == 0x42 && buffer[11] == 0x50; // "WEBP"
        }
        catch
        {
            return false;
        }
    }

    public string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);

        // Clean the filename
        nameWithoutExtension = string.Join("", nameWithoutExtension.Split(Path.GetInvalidFileNameChars()));
        nameWithoutExtension = nameWithoutExtension.Replace(" ", "-").ToLowerInvariant();

        // Add timestamp for uniqueness
        var timestamp = DateTime.Now.ToString("yyyyMMdd-HHmmss");
        return $"{nameWithoutExtension}-{timestamp}{extension}";
    }

    private void OptimizeImage(string filePath)
    {
        try
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            // Skip optimization for WebP files as System.Drawing doesn't support them
            if (extension == ".webp")
            {
                _logger.LogInformation("Skipping optimization for WebP file: {FilePath}", filePath);
                return;
            }

            using var image = Image.FromFile(filePath);

            // Only resize if image is larger than 800px in any dimension
            if (image.Width > 800 || image.Height > 800)
            {
                var maxSize = 800;
                var ratio = Math.Min((double)maxSize / image.Width, (double)maxSize / image.Height);
                var newWidth = (int)(image.Width * ratio);
                var newHeight = (int)(image.Height * ratio);

                using var resizedImage = new Bitmap(newWidth, newHeight);
                using var graphics = Graphics.FromImage(resizedImage);

                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                graphics.DrawImage(image, 0, 0, newWidth, newHeight);

                // Save the optimized image with original format
                var tempPath = filePath + ".tmp";
                var format = GetImageFormat(extension);
                resizedImage.Save(tempPath, format);

                // Replace original with optimized version
                File.Delete(filePath);
                File.Move(tempPath, filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not optimize image: {FilePath}", filePath);
            // Continue without optimization if it fails
        }
    }

    private ImageFormat GetImageFormat(string extension)
    {
        return extension switch
        {
            ".jpg" or ".jpeg" => ImageFormat.Jpeg,
            ".png" => ImageFormat.Png,
            ".gif" => ImageFormat.Gif,
            _ => ImageFormat.Jpeg
        };
    }
}
