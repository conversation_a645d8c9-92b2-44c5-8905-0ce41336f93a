using System.Drawing;
using System.Drawing.Imaging;
using Microsoft.Extensions.Options;
using Technoloway.Core.Configuration;

namespace Technoloway.Web.Services;

public interface ISecureFileUploadService
{
    Task<string> UploadImageAsync(IFormFile file, string folder, string? existingFileName = null);
    Task<string> UploadDocumentAsync(IFormFile file, string folder, string? existingFileName = null);
    bool DeleteFile(string filePath);
    bool IsValidImageFile(IFormFile file);
    bool IsValidDocumentFile(IFormFile file);
    string GenerateSecureFileName(string originalFileName);
}

public class SecureFileUploadService : ISecureFileUploadService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SecureFileUploadService> _logger;
    private readonly FileUploadSettings _settings;

    // File signatures (magic bytes) for validation
    private static readonly Dictionary<string, byte[][]> FileSignatures = new()
    {
        { ".jpg", new[] { new byte[] { 0xFF, 0xD8, 0xFF } } },
        { ".jpeg", new[] { new byte[] { 0xFF, 0xD8, 0xFF } } },
        { ".png", new[] { new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A } } },
        { ".gif", new[] { new byte[] { 0x47, 0x49, 0x46, 0x38, 0x37, 0x61 }, new byte[] { 0x47, 0x49, 0x46, 0x38, 0x39, 0x61 } } },
        { ".webp", new[] { new byte[] { 0x52, 0x49, 0x46, 0x46 } } }, // RIFF header
        { ".pdf", new[] { new byte[] { 0x25, 0x50, 0x44, 0x46 } } }, // %PDF
    };

    public SecureFileUploadService(
        IWebHostEnvironment environment,
        ILogger<SecureFileUploadService> logger,
        IOptions<FileUploadSettings> settings)
    {
        _environment = environment;
        _logger = logger;
        _settings = settings.Value;
    }

    public async Task<string> UploadImageAsync(IFormFile file, string folder, string? existingFileName = null)
    {
        try
        {
            if (!IsValidImageFile(file))
            {
                throw new ArgumentException("Invalid image file");
            }

            return await UploadFileAsync(file, folder, "images", existingFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image file");
            throw;
        }
    }

    public async Task<string> UploadDocumentAsync(IFormFile file, string folder, string? existingFileName = null)
    {
        try
        {
            if (!IsValidDocumentFile(file))
            {
                throw new ArgumentException("Invalid document file");
            }

            return await UploadFileAsync(file, folder, "documents", existingFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document file");
            throw;
        }
    }

    private async Task<string> UploadFileAsync(IFormFile file, string folder, string baseFolder, string? existingFileName = null)
    {
        // Create secure upload directory
        var uploadPath = Path.Combine(_environment.WebRootPath, baseFolder, folder);
        if (!Directory.Exists(uploadPath))
        {
            Directory.CreateDirectory(uploadPath);
        }

        // Delete existing file if provided
        if (!string.IsNullOrEmpty(existingFileName))
        {
            var existingPath = Path.Combine(uploadPath, existingFileName);
            if (File.Exists(existingPath))
            {
                File.Delete(existingPath);
            }
        }

        // Generate secure filename
        var fileName = GenerateSecureFileName(file.FileName);
        var filePath = Path.Combine(uploadPath, fileName);

        // Save the file with security checks
        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            await file.CopyToAsync(stream);
        }

        // Additional security: scan file content
        if (_settings.ScanForViruses)
        {
            await ScanFileForThreats(filePath);
        }

        // Optimize images
        if (baseFolder == "images")
        {
            OptimizeImage(filePath);
        }

        // Return the relative URL
        return $"/{baseFolder}/{folder}/{fileName}";
    }

    public bool IsValidImageFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return false;

        // Check file size
        if (file.Length > _settings.MaxFileSizeBytes)
        {
            _logger.LogWarning("File size {Size} exceeds maximum allowed {MaxSize}", file.Length, _settings.MaxFileSizeBytes);
            return false;
        }

        // Check file extension
        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_settings.AllowedImageExtensions.Contains(extension))
        {
            _logger.LogWarning("File extension {Extension} not allowed", extension);
            return false;
        }

        // Check MIME type if enabled
        if (_settings.EnableContentTypeValidation)
        {
            var validMimeTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
            if (!validMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
            {
                _logger.LogWarning("MIME type {MimeType} not allowed", file.ContentType);
                return false;
            }
        }

        // Check file signature if enabled
        if (_settings.EnableFileSignatureValidation)
        {
            if (!IsValidFileSignature(file, extension))
            {
                _logger.LogWarning("File signature validation failed for {Extension}", extension);
                return false;
            }
        }

        return true;
    }

    public bool IsValidDocumentFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return false;

        // Check file size
        if (file.Length > _settings.MaxFileSizeBytes)
        {
            _logger.LogWarning("File size {Size} exceeds maximum allowed {MaxSize}", file.Length, _settings.MaxFileSizeBytes);
            return false;
        }

        // Check file extension
        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_settings.AllowedDocumentExtensions.Contains(extension))
        {
            _logger.LogWarning("File extension {Extension} not allowed", extension);
            return false;
        }

        // Check MIME type if enabled
        if (_settings.EnableContentTypeValidation)
        {
            var validMimeTypes = new[] { "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain" };
            if (!validMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
            {
                _logger.LogWarning("MIME type {MimeType} not allowed", file.ContentType);
                return false;
            }
        }

        // Check file signature if enabled
        if (_settings.EnableFileSignatureValidation)
        {
            if (!IsValidFileSignature(file, extension))
            {
                _logger.LogWarning("File signature validation failed for {Extension}", extension);
                return false;
            }
        }

        return true;
    }

    private bool IsValidFileSignature(IFormFile file, string extension)
    {
        if (!FileSignatures.ContainsKey(extension))
            return true; // If we don't have signature data, allow it

        try
        {
            using var stream = file.OpenReadStream();
            var signatures = FileSignatures[extension];

            foreach (var signature in signatures)
            {
                stream.Position = 0;
                var buffer = new byte[signature.Length];
                var bytesRead = stream.Read(buffer, 0, signature.Length);

                if (bytesRead == signature.Length && buffer.SequenceEqual(signature))
                {
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file signature");
            return false;
        }
    }

    public string GenerateSecureFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName).ToLowerInvariant();
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);

        // Sanitize filename - remove dangerous characters
        var invalidChars = Path.GetInvalidFileNameChars().Concat(new[] { ' ', '(', ')', '[', ']', '{', '}', '&', '$', '#', '@', '!', '%', '^', '*', '+', '=', '|', '\\', '/', ':', ';', '"', '\'', '<', '>', '?', '~', '`' }).ToArray();
        nameWithoutExtension = string.Join("", nameWithoutExtension.Split(invalidChars));

        // Limit length and ensure it's not empty
        nameWithoutExtension = nameWithoutExtension.Length > 50 ? nameWithoutExtension[..50] : nameWithoutExtension;
        if (string.IsNullOrEmpty(nameWithoutExtension))
        {
            nameWithoutExtension = "file";
        }

        // Add timestamp and random component for uniqueness
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss");
        var randomComponent = Guid.NewGuid().ToString("N")[..8];

        return $"{nameWithoutExtension}-{timestamp}-{randomComponent}{extension}";
    }

    public bool DeleteFile(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            // Convert relative URL to physical path
            var physicalPath = Path.Combine(_environment.WebRootPath, filePath.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));

            // Security check: ensure the file is within the allowed upload directory
            var allowedPath = Path.Combine(_environment.WebRootPath, "images");
            var allowedDocPath = Path.Combine(_environment.WebRootPath, "documents");

            if (!physicalPath.StartsWith(allowedPath) && !physicalPath.StartsWith(allowedDocPath))
            {
                _logger.LogWarning("Attempted to delete file outside allowed directories: {Path}", physicalPath);
                return false;
            }

            if (File.Exists(physicalPath))
            {
                File.Delete(physicalPath);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
            return false;
        }
    }

    private async Task ScanFileForThreats(string filePath)
    {
        // Placeholder for virus scanning integration
        // In production, integrate with Windows Defender, ClamAV, or cloud-based scanning services
        await Task.CompletedTask;
        _logger.LogInformation("File scanned for threats: {FilePath}", filePath);
    }

    private void OptimizeImage(string filePath)
    {
        try
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            // Skip optimization for WebP files
            if (extension == ".webp")
            {
                _logger.LogInformation("Skipping optimization for WebP file: {FilePath}", filePath);
                return;
            }

            using var image = Image.FromFile(filePath);

            // Only resize if image is larger than 1200px in any dimension
            if (image.Width > 1200 || image.Height > 1200)
            {
                var maxSize = 1200;
                var ratio = Math.Min((double)maxSize / image.Width, (double)maxSize / image.Height);
                var newWidth = (int)(image.Width * ratio);
                var newHeight = (int)(image.Height * ratio);

                using var resizedImage = new Bitmap(newWidth, newHeight);
                using var graphics = Graphics.FromImage(resizedImage);

                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                graphics.DrawImage(image, 0, 0, newWidth, newHeight);

                // Save the optimized image
                var tempPath = filePath + ".tmp";
                var format = GetImageFormat(extension);
                resizedImage.Save(tempPath, format);

                // Replace original with optimized version
                File.Delete(filePath);
                File.Move(tempPath, filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not optimize image: {FilePath}", filePath);
        }
    }

    private static ImageFormat GetImageFormat(string extension)
    {
        return extension switch
        {
            ".jpg" or ".jpeg" => ImageFormat.Jpeg,
            ".png" => ImageFormat.Png,
            ".gif" => ImageFormat.Gif,
            _ => ImageFormat.Jpeg
        };
    }
}
