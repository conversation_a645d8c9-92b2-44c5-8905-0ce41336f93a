using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.ViewComponents;

public class ContactInfoViewComponent : ViewComponent
{
    private readonly IRepository<SiteSetting> _siteSettingRepository;

    public ContactInfoViewComponent(IRepository<SiteSetting> siteSettingRepository)
    {
        _siteSettingRepository = siteSettingRepository;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        try
        {
            // Get contact-related site settings
            var contactSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Contact" && s.IsPublic);
            var socialSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Social" && s.IsPublic);



            var contactInfo = new ContactInfoViewModel
            {
                ContactSettings = contactSettings.ToList(),
                SocialSettings = socialSettings.ToList()
            };

            return View(contactInfo);
        }
        catch (Exception ex)
        {
            // Log error and return default values
            Console.WriteLine($"Error loading contact info: {ex.Message}");

            var defaultContactInfo = new ContactInfoViewModel
            {
                ContactSettings = new List<SiteSetting>(),
                SocialSettings = new List<SiteSetting>()
            };

            return View(defaultContactInfo);
        }
    }
}

public class ContactInfoViewModel
{
    public List<SiteSetting> ContactSettings { get; set; } = new List<SiteSetting>();
    public List<SiteSetting> SocialSettings { get; set; } = new List<SiteSetting>();
}
