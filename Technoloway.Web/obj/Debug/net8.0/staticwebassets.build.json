{"Version": 1, "Hash": "7ioZjr0Cwa6PGaTcmqfkus2OKieG6MsRY3NOutvT55E=", "Source": "Technoloway.Web", "BasePath": "_content/Technoloway.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Technoloway.Web/wwwroot", "Source": "Technoloway.Web", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/css/site.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/css/site.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/favicon.ico", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/favicon.ico"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/js/site.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/js/site.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/LICENSE", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/bootstrap/LICENSE"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/additional-methods.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/additional-methods.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/additional-methods.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/jquery.validate.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/LICENSE.md", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery-validation/LICENSE.md"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.min.js"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.min.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/dist/jquery.min.map"}, {"Identity": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/LICENSE.txt", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.ui/8.0.11/staticwebassets/V5/lib/jquery/LICENSE.txt"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/bundle/Technoloway.Web.styles.css", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/bundle/", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/bundle/Technoloway.Web.styles.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/projectbundle/Technoloway.Web.bundle.scp.css", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/projectbundle/", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/obj/Debug/net8.0/scopedcss/projectbundle/Technoloway.Web.bundle.scp.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/admin.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/admin.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/chatbot.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/chatbot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/chatbot.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/client.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/client.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/custom.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/custom.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/hero-slideshow.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/hero-slideshow.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/modern-homepage.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/modern-homepage.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/css/site.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/favicon.ico", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/about-us.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/about-us.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/about-us.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/AboutUs.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/AboutUs.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/AboutUs.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/blog/clarity-20250526-094901.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/blog/clarity-20250526-094901.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/blog/clarity-20250526-094901.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/blog/microservices-dotnet.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/blog/microservices-dotnet.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/blog/microservices-dotnet.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/blog/programing-20250526-094827.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/blog/programing-20250526-094827.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/blog/programing-20250526-094827.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/angular-20250526-114413.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/angular-20250526-114413.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/angular-20250526-114413.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/clarity-20250526-093428.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/clarity-20250526-093428.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/clarity-20250526-093428.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/dotnet-20250524-111123-20250526-143429.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/dotnet-20250524-111123-20250526-143429.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/dotnet-20250524-111123-20250526-143429.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/nodejs-20250526-130439.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/nodejs-20250526-130439.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/nodejs-20250526-130439.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/programing-20250526-093444.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/programing-20250526-093444.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/programing-20250526-093444.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/clients/twitter-20250526-093410.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/clients/twitter-20250526-093410.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/clients/twitter-20250526-093410.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/ai-ml.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/ai-ml.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/ai-ml.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/ecommerce.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/ecommerce.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/ecommerce.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/hero-image1.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/hero-image1.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/hero-image1.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/Slide1.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/Slide1.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/Slide1.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/Slide2.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/Slide2.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/Slide2.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/hero/Slide3.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/hero/Slide3.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/hero/Slide3.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/projects/angular-20250526-100837.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/projects/angular-20250526-100837.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/projects/angular-20250526-100837.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/projects/ecommerce-platform.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/projects/ecommerce-platform.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/projects/ecommerce-platform.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/projects/financial-dashboard.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/projects/financial-dashboard.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/projects/financial-dashboard.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/projects/fitness-app.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/projects/fitness-app.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/projects/fitness-app.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/services/front-end-20250528-145844.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/services/front-end-20250528-145844.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/services/front-end-20250528-145844.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/services/mobile-app-20250528-150721.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/services/mobile-app-20250528-150721.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/services/mobile-app-20250528-150721.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/services/social-media-20250528-150030.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/services/social-media-20250528-150030.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/services/social-media-20250528-150030.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/services/website-20250528-150619.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/services/website-20250528-150619.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/services/website-20250528-150619.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/about-us-20250524-125357.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/about-us-20250524-125357.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/about-us-20250524-125357.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/angular-20250524-140842.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/angular-20250524-140842.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/angular-20250524-140842.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/azure-20250524-121937.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/azure-20250524-121937.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/azure-20250524-121937.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/azure-20250524-124504.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/azure-20250524-124504.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/azure-20250524-124504.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/azure-20250524-125012.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/azure-20250524-125012.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/azure-20250524-125012.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-121853-20250524-130031.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-121853-20250524-130031.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-121853-20250524-130031.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-121853.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-121853.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-121853.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-121926.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-121926.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-121926.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-123513-20250524-130133.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-123513-20250524-130133.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-123513-20250524-130133.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-123513.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-123513.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-123513.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-124315.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-124315.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-124315.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/dotnet-20250524-130914.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/dotnet-20250524-130914.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/dotnet-20250524-130914.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/facebook-20250524-153022.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/facebook-20250524-153022.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/facebook-20250524-153022.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/instagram-20250524-153148.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/instagram-20250524-153148.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/instagram-20250524-153148.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/instagram-20250526-085239.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/instagram-20250526-085239.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/instagram-20250526-085239.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/linkedin-20250524-153227.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/linkedin-20250524-153227.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/linkedin-20250524-153227.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/logo-20250525-225349.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/logo-20250525-225349.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/logo-20250525-225349.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/logo-20250525-225349.png.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/logo-20250525-225349.png.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/logo-20250525-225349.png.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-121841.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-121841.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-121841.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-122235.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-122235.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-122235.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-123459.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-123459.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-123459.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-125243.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-125243.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-125243.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-134051.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-134051.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-134051.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/nodejs-20250524-135948.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/nodejs-20250524-135948.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/nodejs-20250524-135948.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/programing-20250524-150152.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/programing-20250524-150152.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/programing-20250524-150152.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/programing-20250524-150245.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/programing-20250524-150245.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/programing-20250524-150245.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/programing-20250524-151525.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/programing-20250524-151525.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/programing-20250524-151525.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-111935.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-111935.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-111935.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-122447.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-122447.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-122447.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-123116.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-123116.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-123116.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-123816.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-123816.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-123816.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-124338.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-124338.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-124338.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-124453.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-124453.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-124453.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-124515.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-124515.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-124515.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-124534.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-124534.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-124534.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-124941.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-124941.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-124941.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-125202.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-125202.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-125202.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-135923.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-135923.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-135923.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-140821.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-140821.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-140821.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-143944.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-143944.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-143944.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/react-20250524-144109.png.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/react-20250524-144109.png.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/react-20250524-144109.png.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/twitter-20250524-153416.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/twitter-20250524-153416.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/twitter-20250524-153416.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/settings/twitter-20250526-085053.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/settings/twitter-20250526-085053.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/settings/twitter-20250526-085053.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/team/img_20200225_233233525-20250524-105554.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/team/img_20200225_233233525-20250524-105554.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/team/img_20200225_233233525-20250524-105554.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/team/instagram-20250526-094943.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/team/instagram-20250526-094943.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/team/instagram-20250526-094943.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/team/twitter-20250526-094930.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/team/twitter-20250526-094930.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/team/twitter-20250526-094930.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/angular.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/angular.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/angular.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/azure.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/azure.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/azure.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/delphi.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/delphi.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/dotnet-20250524-111123-20250526-095131.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/dotnet-20250524-111123-20250526-095131.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/dotnet-20250524-111123-20250526-095131.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/haskell.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/haskell.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/nodejs.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/nodejs.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/nodejs.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/nodejs.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/python.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/python.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/react.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/react.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/react.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technologies/swift.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technologies/swift.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technoloway-logo.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technoloway-logo.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/technoloway-logo.svg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/technoloway-logo.svg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/testimonials/micha<PERSON>-johnson.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/testimonials/micha<PERSON>-johnson.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/testimonials/micha<PERSON>-johnson.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/testimonials/sarah-williams-20250526-091712.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/testimonials/sarah-williams-20250526-091712.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/testimonials/sarah-williams-20250526-091712.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/1ad93520-6c54-41a4-8b7f-8d025da883b9_2025_05_07_12_54_46-20250529-210633.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/1ad93520-6c54-41a4-8b7f-8d025da883b9_2025_05_07_12_54_46-20250529-210633.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/1ad93520-6c54-41a4-8b7f-8d025da883b9_2025_05_07_12_54_46-20250529-210633.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-140027.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-140027.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-140027.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-140027.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-140027.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-140027.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-140855.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-140855.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-140855.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-140855.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-140855.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-140855.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151315.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151315.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151315.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151315.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151315.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151315.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151427.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151427.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151427.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151427.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151427.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151427.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151552.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151552.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151552.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250530-151552.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250530-151552.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250530-151552.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250602-213602.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250602-213602.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250602-213602.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/2786296-20250602-213602.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/2786296-20250602-213602.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/2786296-20250602-213602.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/400w-7ffrtxsdj80-20250602-191333.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/400w-7ffrtxsdj80-20250602-191333.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/400w-7ffrtxsdj80-20250602-191333.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/400w-ktrfdslr1sa-20250602-183848.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/400w-ktrfdslr1sa-20250602-183848.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/400w-ktrfdslr1sa-20250602-183848.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/400w-ktrfdslr1sa-20250602-191134.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/400w-ktrfdslr1sa-20250602-191134.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/400w-ktrfdslr1sa-20250602-191134.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/b1.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/b1.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/b1.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/b2.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/b2.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/b2.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/b3.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/b3.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/b3.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/b4.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/b4.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/b4.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner1-20250530-131352.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner1-20250530-131352.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner1-20250530-131352.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner1-20250530-131352.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner1-20250530-131352.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner1-20250530-131352.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner1-20250530-131534.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner1-20250530-131534.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner1-20250530-131534.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner1-20250530-131534.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner1-20250530-131534.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner1-20250530-131534.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-132919.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-132919.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-132919.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-132919.png.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-132919.png.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-132919.png.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-134407.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-134407.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-134407.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-134407.png.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-134407.png.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-134407.png.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-141025.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-141025.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-141025.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/banner11-20250530-141025.png.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/banner11-20250530-141025.png.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/banner11-20250530-141025.png.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4icemofdnco-20250530-120956.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4ICEmOFdnco.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4ICEmOFdnco.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-beige-simple-elegant-personal-linkedin-banner-4ICEmOFdnco.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-and-ivory-modern-name-youtube-channel-art-5cO9Q_UyXz0.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-and-ivory-modern-name-youtube-channel-art-5cO9Q_UyXz0.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-and-ivory-modern-name-youtube-channel-art-5cO9Q_UyXz0.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-and-white-modern-minimalist-planter-youtube-banner-24eDzDjjRRk.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-and-white-modern-minimalist-planter-youtube-banner-24eDzDjjRRk.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-and-white-modern-minimalist-planter-youtube-banner-24eDzDjjRRk.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-luowclsll8y-20250530-115625.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-LUOWclslL8Y.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-modern-personal-linkedin-banner-LUOWclslL8Y.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-modern-personal-linkedin-banner-LUOWclslL8Y.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-black-white-grayscale-mono-moody-forrest-vlog-vlogger-youtube-channel-art-banner-ZmihhQa941E.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-black-white-grayscale-mono-moody-forrest-vlog-vlogger-youtube-channel-art-banner-ZmihhQa941E.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-black-white-grayscale-mono-moody-forrest-vlog-vlogger-youtube-channel-art-banner-ZmihhQa941E.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-red-futuristic-game-youtube-channel-art-rn37wsFPATo.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-red-futuristic-game-youtube-channel-art-rn37wsFPATo.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-red-futuristic-game-youtube-channel-art-rn37wsFPATo.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250530-115053.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250530-115053.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250530-115053.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-d-v_icmtf-i-20250602-185959.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-D-V_IcMtf-I.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-D-V_IcMtf-I.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-white-modern-professional-general-linkedin-banner-D-V_IcMtf-I.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blue-and-white-simple-daily-vlogger-youtube-banner-u_1ijMIgkVU.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blue-and-white-simple-daily-vlogger-youtube-banner-u_1ijMIgkVU.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blue-and-white-simple-daily-vlogger-youtube-banner-u_1ijMIgkVU.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nunmg4s1vq8-20250530-115218.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nuNmg4S1vq8.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-blush-wave-linkedin-banner-nuNmg4S1vq8.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-blush-wave-linkedin-banner-nuNmg4S1vq8.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-brown-simple-adventure-youtube-banner-R3n7i7Sds-I.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-brown-simple-adventure-youtube-banner-R3n7i7Sds-I.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-brown-simple-adventure-youtube-banner-R3n7i7Sds-I.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-feminine-torn-paper-name-youtube-channel-art-ksyh471Uzks.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-feminine-torn-paper-name-youtube-channel-art-ksyh471Uzks.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-feminine-torn-paper-name-youtube-channel-art-ksyh471Uzks.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/canva-neutral-simple-minimalist-lifestyle-blogger-youtube-channel-art-gD1YcRQ0FGk.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/canva-neutral-simple-minimalist-lifestyle-blogger-youtube-channel-art-gD1YcRQ0FGk.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/canva-neutral-simple-minimalist-lifestyle-blogger-youtube-channel-art-gD1YcRQ0FGk.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/ecommerce-20250530-103530.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/ecommerce-20250530-103530.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/ecommerce-20250530-103530.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/ecommerce-20250530-103530.jpg.tmp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/ecommerce-20250530-103530.jpg.tmp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/ecommerce-20250530-103530.jpg.tmp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/sarah-williams-20250530-104154.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/sarah-williams-20250530-104154.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/sarah-williams-20250530-104154.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/slide1-20250529-142300.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/slide1-20250529-142300.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/slide1-20250529-142300.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/slide2-20250529-142217.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/slide2-20250529-142217.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/slide2-20250529-142217.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/slide2-20250529-142318.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/slide2-20250529-142318.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/slide2-20250529-142318.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/slide3-20250529-142324.webp", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/slide3-20250529-142324.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/slide3-20250529-142324.webp"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/images/uploads/website-20250530-093119.png", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/uploads/website-20250530-093119.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/uploads/website-20250530-093119.png"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/admin.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/admin.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/chatbot.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/chatbot.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/chatbot.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/client.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/client.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/hero-slideshow.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/hero-slideshow.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/homepage-animations.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/homepage-animations.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/js/site.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/bootstrap/LICENSE", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.slim.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.slim.min.js", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.js"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/dist/jquery.slim.min.map", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.slim.min.map"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/uploads/b1.jpg", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "uploads/b1.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/uploads/b1.jpg"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/videos/uploads/output_2-20250529-143805.mp4", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "videos/uploads/output_2-20250529-143805.mp4", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/videos/uploads/output_2-20250529-143805.mp4"}, {"Identity": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/videos/uploads/output_2-20250530-121526.mp4", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "/Volumes/Files/Technoloway (Processing)/Technoloway.Web/wwwroot/", "BasePath": "_content/Technoloway.Web", "RelativePath": "videos/uploads/output_2-20250530-121526.mp4", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/videos/uploads/output_2-20250530-121526.mp4"}]}