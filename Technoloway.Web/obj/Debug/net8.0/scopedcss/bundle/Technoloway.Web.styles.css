/* _content/Technoloway.Web/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-gsobuq1hsa] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-gsobuq1hsa] {
  color: #0077cc;
}

.btn-primary[b-gsobuq1hsa] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-gsobuq1hsa], .nav-pills .show > .nav-link[b-gsobuq1hsa] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-gsobuq1hsa] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-gsobuq1hsa] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-gsobuq1hsa] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-gsobuq1hsa] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-gsobuq1hsa] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
