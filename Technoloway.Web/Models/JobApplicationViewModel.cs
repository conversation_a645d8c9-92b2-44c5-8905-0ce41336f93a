using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace Technoloway.Web.Models;

public class JobApplicationViewModel
{
    public int JobListingId { get; set; }
    
    public string JobTitle { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Name is required")]
    [Display(Name = "Full Name")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Phone number is required")]
    [Display(Name = "Phone Number")]
    public string Phone { get; set; } = string.Empty;
    
    [Display(Name = "Resume/CV")]
    public IFormFile? Resume { get; set; }
    
    [Required(ErrorMessage = "Cover letter is required")]
    [Display(Name = "Cover Letter")]
    public string CoverLetter { get; set; } = string.Empty;
}
